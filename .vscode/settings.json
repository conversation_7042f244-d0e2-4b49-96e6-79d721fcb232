{"python.defaultInterpreterPath": "./venv/bin/python", "python.terminal.activateEnvironment": true, "python.envFile": "${workspaceFolder}/.env", "python.analysis.extraPaths": ["./", "/home/<USER>/Documents/repositories/document-data-extraction"], "python.analysis.autoImportCompletions": true, "python.analysis.autoSearchPaths": true, "python.analysis.diagnosticMode": "workspace", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.linting.lintOnSave": true, "python.formatting.provider": "black", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.pytestArgs": ["Augment_test"], "debug.allowBreakpointsEverywhere": true, "debug.showBreakpointsInOverviewRuler": true, "debug.showInlineBreakpointCandidates": true, "debug.inlineValues": "on", "debug.toolBarLocation": "floating", "debug.console.fontSize": 14, "debug.console.lineHeight": 20, "debug.console.wordWrap": true, "files.associations": {"*.py": "python"}, "editor.rulers": [88, 120], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}:/home/<USER>/Documents/repositories/document-data-extraction"}}