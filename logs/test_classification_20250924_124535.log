2025-09-24 12:45:35,309 - INFO - Logging initialized. Log file: logs/test_classification_20250924_124535.log
2025-09-24 12:45:35,309 - INFO - 📁 Found 13 files to process
2025-09-24 12:45:35,309 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 12:45:35,309 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 12:45:35,309 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 12:45:35,309 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 12:45:35,309 - INFO - ⬆️ [12:45:35] Uploading: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:45:37,284 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/2b55d4e0_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:45:37,284 - INFO - 🔍 [12:45:37] Starting classification: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:45:37,285 - INFO - ⬆️ [12:45:37] Uploading: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:45:37,286 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:37,312 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:37,322 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2b55d4e0_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:45:37,322 - INFO - Processing PDF from S3...
2025-09-24 12:45:37,323 - INFO - Downloading PDF from S3 to /tmp/tmpkstthd7g/2b55d4e0_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:45:38,415 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/bf06f130_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:45:38,415 - INFO - 🔍 [12:45:38] Starting classification: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:45:38,416 - INFO - ⬆️ [12:45:38] Uploading: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:45:38,423 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:38,437 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:38,440 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bf06f130_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:45:38,441 - INFO - Processing PDF from S3...
2025-09-24 12:45:38,441 - INFO - Downloading PDF from S3 to /tmp/tmp5vs4ut5n/bf06f130_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:45:38,939 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:45:38,940 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:38,942 - INFO - Splitting PDF 2b55d4e0_I_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 12:45:38,948 - INFO - Split PDF into 1 pages
2025-09-24 12:45:38,948 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:38,948 - INFO - Expected pages: [1]
2025-09-24 12:45:39,559 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/e587485d_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:45:39,559 - INFO - 🔍 [12:45:39] Starting classification: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:45:39,560 - INFO - ⬆️ [12:45:39] Uploading: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:45:39,561 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:39,585 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:39,590 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e587485d_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:45:39,590 - INFO - Processing PDF from S3...
2025-09-24 12:45:39,590 - INFO - Downloading PDF from S3 to /tmp/tmpha8r0htj/e587485d_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:45:40,520 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:45:40,521 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:40,522 - INFO - Splitting PDF bf06f130_NMFC_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 12:45:40,527 - INFO - Split PDF into 2 pages
2025-09-24 12:45:40,527 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:40,527 - INFO - Expected pages: [1, 2]
2025-09-24 12:45:41,010 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/b8b1bfca_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:45:41,010 - INFO - 🔍 [12:45:41] Starting classification: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:45:41,010 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:41,010 - INFO - ⬆️ [12:45:41] Uploading: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:45:41,017 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:41,020 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b8b1bfca_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:45:41,020 - INFO - Processing PDF from S3...
2025-09-24 12:45:41,020 - INFO - Downloading PDF from S3 to /tmp/tmp8gz4qb8n/b8b1bfca_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:45:41,650 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:45:41,650 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:41,652 - INFO - Splitting PDF e587485d_NMFC_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 12:45:41,654 - INFO - Split PDF into 3 pages
2025-09-24 12:45:41,654 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:41,654 - INFO - Expected pages: [1, 2, 3]
2025-09-24 12:45:41,664 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/07489e0e_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:45:41,665 - INFO - 🔍 [12:45:41] Starting classification: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:45:41,665 - INFO - ⬆️ [12:45:41] Uploading: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:45:41,670 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:41,692 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:41,699 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/07489e0e_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:45:41,700 - INFO - Processing PDF from S3...
2025-09-24 12:45:41,701 - INFO - Downloading PDF from S3 to /tmp/tmpiipuur4n/07489e0e_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:45:42,335 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/b61f4632_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:45:42,336 - INFO - 🔍 [12:45:42] Starting classification: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:45:42,337 - INFO - ⬆️ [12:45:42] Uploading: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:45:42,338 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:42,363 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:42,368 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b61f4632_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:45:42,369 - INFO - Processing PDF from S3...
2025-09-24 12:45:42,369 - INFO - Downloading PDF from S3 to /tmp/tmpcx_yklcj/b61f4632_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:45:42,911 - INFO - Page 1: Extracted 589 characters, 36 lines from 2b55d4e0_I_QHD3LC0DU6S8O2YVVS60_41619c11_page_001.pdf
2025-09-24 12:45:42,911 - INFO - Successfully processed page 1
2025-09-24 12:45:42,912 - INFO - Combined 1 pages into final text
2025-09-24 12:45:42,912 - INFO - Text validation for 2b55d4e0_I_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 12:45:42,913 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:42,913 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:43,031 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:45:43,031 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:43,033 - INFO - Splitting PDF 07489e0e_NMFC_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 12:45:43,039 - INFO - Split PDF into 2 pages
2025-09-24 12:45:43,040 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:43,040 - INFO - Expected pages: [1, 2]
2025-09-24 12:45:43,106 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/25788df0_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:45:43,107 - INFO - 🔍 [12:45:43] Starting classification: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:45:43,108 - INFO - ⬆️ [12:45:43] Uploading: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:45:43,109 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:43,155 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:43,159 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/25788df0_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:45:43,159 - INFO - Processing PDF from S3...
2025-09-24 12:45:43,159 - INFO - Downloading PDF from S3 to /tmp/tmp43f0i_7m/25788df0_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:45:43,580 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 12:45:43,580 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:43,581 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 12:45:43,582 - INFO - Splitting PDF b8b1bfca_NMFC_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 12:45:43,617 - INFO - Split PDF into 2 pages
2025-09-24 12:45:43,617 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:43,617 - INFO - Expected pages: [1, 2]
2025-09-24 12:45:43,810 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/f2b730a2_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:45:43,810 - INFO - 🔍 [12:45:43] Starting classification: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:45:43,811 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:43,811 - INFO - ⬆️ [12:45:43] Uploading: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:45:43,823 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:43,832 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f2b730a2_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:45:43,833 - INFO - Processing PDF from S3...
2025-09-24 12:45:43,833 - INFO - Downloading PDF from S3 to /tmp/tmpa464xv23/f2b730a2_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:45:44,178 - INFO - Page 2: Extracted 764 characters, 54 lines from bf06f130_NMFC_DH0JZ2JWDGRHD26BX74C_df3ef9a7_page_002.pdf
2025-09-24 12:45:44,181 - INFO - Successfully processed page 2
2025-09-24 12:45:44,479 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/ebe46b61_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:45:44,479 - INFO - 🔍 [12:45:44] Starting classification: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:45:44,480 - INFO - ⬆️ [12:45:44] Uploading: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:45:44,482 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:44,498 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:44,506 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ebe46b61_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:45:44,506 - INFO - Processing PDF from S3...
2025-09-24 12:45:44,506 - INFO - Downloading PDF from S3 to /tmp/tmp_7daq5pl/ebe46b61_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:45:45,061 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:45:45,061 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:45,062 - INFO - Splitting PDF b61f4632_NMFC_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 12:45:45,064 - INFO - Split PDF into 1 pages
2025-09-24 12:45:45,064 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:45,064 - INFO - Expected pages: [1]
2025-09-24 12:45:45,143 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/e12b6247_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:45:45,143 - INFO - 🔍 [12:45:45] Starting classification: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:45:45,144 - INFO - ⬆️ [12:45:45] Uploading: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:45:45,146 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:45,163 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:45,167 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e12b6247_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:45:45,167 - INFO - Processing PDF from S3...
2025-09-24 12:45:45,168 - INFO - Downloading PDF from S3 to /tmp/tmpoljxqbo7/e12b6247_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:45:45,548 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2b55d4e0_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:45:45,684 - INFO - Page 1: Extracted 854 characters, 69 lines from bf06f130_NMFC_DH0JZ2JWDGRHD26BX74C_df3ef9a7_page_001.pdf
2025-09-24 12:45:45,684 - INFO - Successfully processed page 1
2025-09-24 12:45:45,685 - INFO - Combined 2 pages into final text
2025-09-24 12:45:45,685 - INFO - Text validation for bf06f130_NMFC_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 12:45:45,686 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:45,686 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:45,696 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 12:45:45,697 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:45,698 - INFO - Splitting PDF 25788df0_W_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 12:45:45,700 - INFO - Split PDF into 1 pages
2025-09-24 12:45:45,700 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:45,700 - INFO - Expected pages: [1]
2025-09-24 12:45:45,811 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/df588938_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:45:45,812 - INFO - 🔍 [12:45:45] Starting classification: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:45:45,813 - INFO - ⬆️ [12:45:45] Uploading: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:45:45,814 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:45,836 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:45,841 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/df588938_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:45:45,841 - INFO - Processing PDF from S3...
2025-09-24 12:45:45,841 - INFO - Downloading PDF from S3 to /tmp/tmp9tbjsj21/df588938_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:45:45,872 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:45:45,873 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:45,873 - INFO - Splitting PDF f2b730a2_W_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 12:45:45,875 - INFO - Split PDF into 1 pages
2025-09-24 12:45:45,875 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:45,875 - INFO - Expected pages: [1]
2025-09-24 12:45:46,083 - INFO - Page 2: Extracted 850 characters, 59 lines from e587485d_NMFC_NJ4WSZ8BUQAW48V6403N_165b4b33_page_002.pdf
2025-09-24 12:45:46,083 - INFO - Successfully processed page 2
2025-09-24 12:45:46,131 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:45:46,131 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:46,135 - INFO - Splitting PDF ebe46b61_W_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 12:45:46,141 - INFO - Split PDF into 1 pages
2025-09-24 12:45:46,141 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:46,141 - INFO - Expected pages: [1]
2025-09-24 12:45:46,457 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/bcabb846_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:45:46,457 - INFO - 🔍 [12:45:46] Starting classification: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:45:46,457 - INFO - ⬆️ [12:45:46] Uploading: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:45:46,457 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:46,462 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:46,467 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bcabb846_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:45:46,467 - INFO - Processing PDF from S3...
2025-09-24 12:45:46,468 - INFO - Downloading PDF from S3 to /tmp/tmpt8mdapqe/bcabb846_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:45:46,612 - INFO - Page 3: Extracted 850 characters, 59 lines from e587485d_NMFC_NJ4WSZ8BUQAW48V6403N_165b4b33_page_003.pdf
2025-09-24 12:45:46,612 - INFO - Successfully processed page 3
2025-09-24 12:45:46,823 - INFO - Page 1: Extracted 980 characters, 76 lines from e587485d_NMFC_NJ4WSZ8BUQAW48V6403N_165b4b33_page_001.pdf
2025-09-24 12:45:46,823 - INFO - Successfully processed page 1
2025-09-24 12:45:46,823 - INFO - Combined 3 pages into final text
2025-09-24 12:45:46,823 - INFO - Text validation for e587485d_NMFC_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 12:45:46,824 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:46,824 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:47,137 - INFO - Page 2: Extracted 913 characters, 56 lines from 07489e0e_NMFC_R1V0MO844PBLWNEAUETU_69cf0f0d_page_002.pdf
2025-09-24 12:45:47,138 - INFO - Successfully processed page 2
2025-09-24 12:45:47,147 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/1098571d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:45:47,147 - INFO - 🔍 [12:45:47] Starting classification: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:45:47,148 - INFO - Initializing TextractProcessor...
2025-09-24 12:45:47,154 - INFO - Initializing BedrockProcessor...
2025-09-24 12:45:47,158 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1098571d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:45:47,160 - INFO - Processing PDF from S3...
2025-09-24 12:45:47,164 - INFO - Downloading PDF from S3 to /tmp/tmp95sq9vhs/1098571d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:45:47,175 - INFO - 

I_QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "inspection_cert"
        }
    ]
}
2025-09-24 12:45:47,175 - INFO - 

✓ Saved result: output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 12:45:47,189 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:45:47,190 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:47,191 - INFO - Splitting PDF e12b6247_W_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 12:45:47,194 - INFO - Split PDF into 1 pages
2025-09-24 12:45:47,194 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:47,194 - INFO - Expected pages: [1]
2025-09-24 12:45:47,440 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:45:47,440 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:47,442 - INFO - Splitting PDF df588938_W_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 12:45:47,443 - INFO - Split PDF into 1 pages
2025-09-24 12:45:47,443 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:47,444 - INFO - Expected pages: [1]
2025-09-24 12:45:47,501 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2b55d4e0_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:45:47,841 - INFO - Page 1: Extracted 1511 characters, 86 lines from 07489e0e_NMFC_R1V0MO844PBLWNEAUETU_69cf0f0d_page_001.pdf
2025-09-24 12:45:47,842 - INFO - Successfully processed page 1
2025-09-24 12:45:47,842 - INFO - Combined 2 pages into final text
2025-09-24 12:45:47,842 - INFO - Text validation for 07489e0e_NMFC_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 12:45:47,842 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:47,842 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:48,156 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:45:48,156 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:48,159 - INFO - Splitting PDF bcabb846_W_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 12:45:48,162 - INFO - Split PDF into 1 pages
2025-09-24 12:45:48,162 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:48,163 - INFO - Expected pages: [1]
2025-09-24 12:45:48,734 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bf06f130_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:45:48,763 - INFO - 

NMFC_DH0JZ2JWDGRHD26BX74C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:45:48,764 - INFO - 

✓ Saved result: output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:45:49,085 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bf06f130_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:45:49,222 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:45:49,222 - INFO - Splitting PDF into individual pages...
2025-09-24 12:45:49,225 - INFO - Splitting PDF 1098571d_W_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 12:45:49,236 - INFO - Split PDF into 1 pages
2025-09-24 12:45:49,236 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:45:49,236 - INFO - Expected pages: [1]
2025-09-24 12:45:49,573 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e587485d_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:45:49,606 - INFO - Page 2: Extracted 540 characters, 29 lines from b8b1bfca_NMFC_OR9EL08KIKNQPZ3UV3HH_4cab7ad4_page_002.pdf
2025-09-24 12:45:49,608 - INFO - Successfully processed page 2
2025-09-24 12:45:49,610 - INFO - 

NMFC_NJ4WSZ8BUQAW48V6403N.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 3,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:45:49,610 - INFO - 

✓ Saved result: output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:45:49,869 - INFO - Page 1: Extracted 443 characters, 72 lines from b61f4632_NMFC_RUDVGETVRZO7XX6YNW7I_c2a26cc1_page_001.pdf
2025-09-24 12:45:49,870 - INFO - Successfully processed page 1
2025-09-24 12:45:49,870 - INFO - Combined 1 pages into final text
2025-09-24 12:45:49,870 - INFO - Text validation for b61f4632_NMFC_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 12:45:49,870 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:49,870 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:49,943 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e587485d_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:45:50,334 - INFO - Page 1: Extracted 626 characters, 49 lines from ebe46b61_W_DFY1VDZWR7NBDLJV02G2_bf3c10ec_page_001.pdf
2025-09-24 12:45:50,334 - INFO - Successfully processed page 1
2025-09-24 12:45:50,334 - INFO - Combined 1 pages into final text
2025-09-24 12:45:50,334 - INFO - Text validation for ebe46b61_W_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 12:45:50,335 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:50,335 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:50,877 - INFO - Page 1: Extracted 1120 characters, 87 lines from b8b1bfca_NMFC_OR9EL08KIKNQPZ3UV3HH_4cab7ad4_page_001.pdf
2025-09-24 12:45:50,877 - INFO - Successfully processed page 1
2025-09-24 12:45:50,878 - INFO - Combined 2 pages into final text
2025-09-24 12:45:50,878 - INFO - Text validation for b8b1bfca_NMFC_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 12:45:50,881 - INFO - Page 1: Extracted 732 characters, 59 lines from f2b730a2_W_DCY7SLNMWUXIENOREHQF_c8f56e2e_page_001.pdf
2025-09-24 12:45:50,881 - INFO - Successfully processed page 1
2025-09-24 12:45:50,882 - INFO - Combined 1 pages into final text
2025-09-24 12:45:50,882 - INFO - Text validation for f2b730a2_W_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 12:45:50,882 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:50,882 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:50,884 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:50,884 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:50,964 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/07489e0e_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:45:51,025 - INFO - 

NMFC_R1V0MO844PBLWNEAUETU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:45:51,025 - INFO - 

✓ Saved result: output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:45:51,345 - INFO - Page 1: Extracted 802 characters, 30 lines from df588938_W_K9VSARJOKAIZHNJ5RBDT_9a3be85f_page_001.pdf
2025-09-24 12:45:51,345 - INFO - Successfully processed page 1
2025-09-24 12:45:51,345 - INFO - Combined 1 pages into final text
2025-09-24 12:45:51,345 - INFO - Text validation for df588938_W_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 12:45:51,346 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:51,346 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:51,355 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/07489e0e_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:45:51,868 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b61f4632_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:45:51,887 - INFO - 

NMFC_RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 12:45:51,887 - INFO - 

✓ Saved result: output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 12:45:52,217 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b61f4632_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:45:52,356 - INFO - Page 1: Extracted 580 characters, 48 lines from bcabb846_W_WRKSHW76B3QUG47QWR75_3fb9958d_page_001.pdf
2025-09-24 12:45:52,357 - INFO - Successfully processed page 1
2025-09-24 12:45:52,357 - INFO - Combined 1 pages into final text
2025-09-24 12:45:52,357 - INFO - Text validation for bcabb846_W_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 12:45:52,358 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:52,358 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:52,424 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ebe46b61_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:45:52,443 - INFO - 

W_DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:45:52,443 - INFO - 

✓ Saved result: output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:45:52,625 - INFO - Page 1: Extracted 517 characters, 31 lines from e12b6247_W_HFPAXYL947DH59AB12FL_b8b58cca_page_001.pdf
2025-09-24 12:45:52,625 - INFO - Successfully processed page 1
2025-09-24 12:45:52,626 - INFO - Combined 1 pages into final text
2025-09-24 12:45:52,626 - INFO - Text validation for e12b6247_W_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 12:45:52,626 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:52,626 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:52,767 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ebe46b61_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:45:53,038 - INFO - Page 1: Extracted 939 characters, 64 lines from 25788df0_W_A34CDFDJ66EDOZEKZWJL_64cb732a_page_001.pdf
2025-09-24 12:45:53,038 - INFO - Successfully processed page 1
2025-09-24 12:45:53,038 - INFO - Combined 1 pages into final text
2025-09-24 12:45:53,038 - INFO - Text validation for 25788df0_W_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 12:45:53,039 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:53,039 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:53,479 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/df588938_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:45:53,493 - INFO - 

W_K9VSARJOKAIZHNJ5RBDT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 12:45:53,493 - INFO - 

✓ Saved result: output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 12:45:53,660 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b8b1bfca_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:45:53,713 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f2b730a2_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:45:53,826 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/df588938_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:45:53,855 - INFO - 

NMFC_OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:45:53,856 - INFO - 

✓ Saved result: output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:45:54,181 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b8b1bfca_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:45:54,202 - INFO - 

W_DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:45:54,202 - INFO - 

✓ Saved result: output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 12:45:54,461 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bcabb846_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:45:54,528 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f2b730a2_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:45:54,546 - INFO - 

W_WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:45:54,546 - INFO - 

✓ Saved result: output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 12:45:54,876 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bcabb846_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:45:54,930 - INFO - Page 1: Extracted 528 characters, 31 lines from 1098571d_W_XCJLXZK140FUS8020ZAG_d1e63db2_page_001.pdf
2025-09-24 12:45:54,931 - INFO - Successfully processed page 1
2025-09-24 12:45:54,931 - INFO - Combined 1 pages into final text
2025-09-24 12:45:54,931 - INFO - Text validation for 1098571d_W_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 12:45:54,932 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:45:54,932 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:45:55,033 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e12b6247_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:45:55,050 - INFO - 

W_HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:45:55,050 - INFO - 

✓ Saved result: output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 12:45:55,281 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/25788df0_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:45:55,380 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e12b6247_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:45:55,401 - INFO - 

W_A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:45:55,401 - INFO - 

✓ Saved result: output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 12:45:55,747 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/25788df0_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:45:57,435 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1098571d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:45:57,447 - INFO - 

W_XCJLXZK140FUS8020ZAG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 12:45:57,447 - INFO - 

✓ Saved result: output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 12:45:57,779 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1098571d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:45:57,780 - INFO - 
📊 Processing Summary:
2025-09-24 12:45:57,780 - INFO -    Total files: 13
2025-09-24 12:45:57,781 - INFO -    Successful: 13
2025-09-24 12:45:57,781 - INFO -    Failed: 0
2025-09-24 12:45:57,781 - INFO -    Duration: 22.47 seconds
2025-09-24 12:45:57,781 - INFO -    Output directory: output
2025-09-24 12:45:57,781 - INFO - 
📋 Successfully Processed Files:
2025-09-24 12:45:57,781 - INFO -    📄 I_QHD3LC0DU6S8O2YVVS60.pdf: {"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}
2025-09-24 12:45:57,781 - INFO -    📄 NMFC_DH0JZ2JWDGRHD26BX74C.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:45:57,781 - INFO -    📄 NMFC_NJ4WSZ8BUQAW48V6403N.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"},{"page_no":3,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:45:57,781 - INFO -    📄 NMFC_OR9EL08KIKNQPZ3UV3HH.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:45:57,781 - INFO -    📄 NMFC_R1V0MO844PBLWNEAUETU.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:45:57,782 - INFO -    📄 NMFC_RUDVGETVRZO7XX6YNW7I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 12:45:57,782 - INFO -    📄 W_A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:45:57,782 - INFO -    📄 W_DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:45:57,782 - INFO -    📄 W_DFY1VDZWR7NBDLJV02G2.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:45:57,782 - INFO -    📄 W_HFPAXYL947DH59AB12FL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:45:57,782 - INFO -    📄 W_K9VSARJOKAIZHNJ5RBDT.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 12:45:57,782 - INFO -    📄 W_WRKSHW76B3QUG47QWR75.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:45:57,783 - INFO -    📄 W_XCJLXZK140FUS8020ZAG.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 12:45:57,783 - INFO - 
============================================================================================================================================
2025-09-24 12:45:57,784 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 12:45:57,784 - INFO - ============================================================================================================================================
2025-09-24 12:45:57,784 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 12:45:57,784 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 12:45:57,784 - INFO - I_QHD3LC0DU6S8O2YVVS60.pdf                         1      inspection_cert      run1_I_QHD3LC0DU6S8O2YVVS60.json                  
2025-09-24 12:45:57,784 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:45:57,784 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 12:45:57,784 - INFO - 
2025-09-24 12:45:57,784 - INFO - NMFC_DH0JZ2JWDGRHD26BX74C.pdf                      1      weight_and_inspect... run1_NMFC_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 12:45:57,784 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:45:57,784 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:45:57,784 - INFO - 
2025-09-24 12:45:57,785 - INFO - NMFC_DH0JZ2JWDGRHD26BX74C.pdf                      2      weight_and_inspect... run1_NMFC_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 12:45:57,785 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:45:57,785 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:45:57,785 - INFO - 
2025-09-24 12:45:57,785 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      1      weight_and_inspect... run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 12:45:57,785 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:45:57,785 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:45:57,785 - INFO - 
2025-09-24 12:45:57,785 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      2      weight_and_inspect... run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 12:45:57,785 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:45:57,785 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:45:57,785 - INFO - 
2025-09-24 12:45:57,785 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      3      weight_and_inspect... run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 12:45:57,785 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:45:57,786 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:45:57,786 - INFO - 
2025-09-24 12:45:57,786 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      1      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 12:45:57,786 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:45:57,786 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:45:57,786 - INFO - 
2025-09-24 12:45:57,786 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      2      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 12:45:57,786 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:45:57,786 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:45:57,786 - INFO - 
2025-09-24 12:45:57,786 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      1      weight_and_inspect... run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 12:45:57,786 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:45:57,786 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:45:57,786 - INFO - 
2025-09-24 12:45:57,786 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      2      weight_and_inspect... run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 12:45:57,786 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:45:57,786 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:45:57,787 - INFO - 
2025-09-24 12:45:57,787 - INFO - NMFC_RUDVGETVRZO7XX6YNW7I.pdf                      1      nmfc_cert            run1_NMFC_RUDVGETVRZO7XX6YNW7I.json               
2025-09-24 12:45:57,787 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:45:57,787 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 12:45:57,787 - INFO - 
2025-09-24 12:45:57,787 - INFO - W_A34CDFDJ66EDOZEKZWJL.pdf                         1      weight_and_inspect... run1_W_A34CDFDJ66EDOZEKZWJL.json                  
2025-09-24 12:45:57,787 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:45:57,787 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 12:45:57,787 - INFO - 
2025-09-24 12:45:57,787 - INFO - W_DCY7SLNMWUXIENOREHQF.pdf                         1      weight_and_inspect... run1_W_DCY7SLNMWUXIENOREHQF.json                  
2025-09-24 12:45:57,787 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:45:57,787 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 12:45:57,787 - INFO - 
2025-09-24 12:45:57,787 - INFO - W_DFY1VDZWR7NBDLJV02G2.pdf                         1      weight_and_inspect... run1_W_DFY1VDZWR7NBDLJV02G2.json                  
2025-09-24 12:45:57,787 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:45:57,787 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:45:57,787 - INFO - 
2025-09-24 12:45:57,787 - INFO - W_HFPAXYL947DH59AB12FL.pdf                         1      weight_and_inspect... run1_W_HFPAXYL947DH59AB12FL.json                  
2025-09-24 12:45:57,788 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:45:57,788 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 12:45:57,788 - INFO - 
2025-09-24 12:45:57,788 - INFO - W_K9VSARJOKAIZHNJ5RBDT.pdf                         1      scale_ticket         run1_W_K9VSARJOKAIZHNJ5RBDT.json                  
2025-09-24 12:45:57,788 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:45:57,788 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 12:45:57,788 - INFO - 
2025-09-24 12:45:57,788 - INFO - W_WRKSHW76B3QUG47QWR75.pdf                         1      weight_and_inspect... run1_W_WRKSHW76B3QUG47QWR75.json                  
2025-09-24 12:45:57,788 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:45:57,788 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 12:45:57,788 - INFO - 
2025-09-24 12:45:57,788 - INFO - W_XCJLXZK140FUS8020ZAG.pdf                         1      scale_ticket         run1_W_XCJLXZK140FUS8020ZAG.json                  
2025-09-24 12:45:57,789 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:45:57,789 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 12:45:57,789 - INFO - 
2025-09-24 12:45:57,789 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 12:45:57,789 - INFO - Total entries: 18
2025-09-24 12:45:57,789 - INFO - ============================================================================================================================================
2025-09-24 12:45:57,789 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 12:45:57,789 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 12:45:57,789 - INFO -   1. I_QHD3LC0DU6S8O2YVVS60.pdf          Page 1   → inspection_cert | run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 12:45:57,789 - INFO -   2. NMFC_DH0JZ2JWDGRHD26BX74C.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:45:57,790 - INFO -   3. NMFC_DH0JZ2JWDGRHD26BX74C.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:45:57,790 - INFO -   4. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:45:57,790 - INFO -   5. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:45:57,790 - INFO -   6. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 3   → weight_and_inspection_cert | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:45:57,790 - INFO -   7. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:45:57,790 - INFO -   8. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:45:57,790 - INFO -   9. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:45:57,790 - INFO -  10. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:45:57,790 - INFO -  11. NMFC_RUDVGETVRZO7XX6YNW7I.pdf       Page 1   → nmfc_cert       | run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 12:45:57,790 - INFO -  12. W_A34CDFDJ66EDOZEKZWJL.pdf          Page 1   → weight_and_inspection_cert | run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 12:45:57,790 - INFO -  13. W_DCY7SLNMWUXIENOREHQF.pdf          Page 1   → weight_and_inspection_cert | run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 12:45:57,790 - INFO -  14. W_DFY1VDZWR7NBDLJV02G2.pdf          Page 1   → weight_and_inspection_cert | run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:45:57,790 - INFO -  15. W_HFPAXYL947DH59AB12FL.pdf          Page 1   → weight_and_inspection_cert | run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 12:45:57,790 - INFO -  16. W_K9VSARJOKAIZHNJ5RBDT.pdf          Page 1   → scale_ticket    | run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 12:45:57,790 - INFO -  17. W_WRKSHW76B3QUG47QWR75.pdf          Page 1   → weight_and_inspection_cert | run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 12:45:57,791 - INFO -  18. W_XCJLXZK140FUS8020ZAG.pdf          Page 1   → scale_ticket    | run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 12:45:57,791 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 12:45:57,791 - INFO - 
✅ Test completed: {'total_files': 13, 'processed': 13, 'failed': 0, 'errors': [], 'duration_seconds': 22.47104, 'processed_files': [{'filename': 'I_QHD3LC0DU6S8O2YVVS60.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf'}, {'filename': 'NMFC_DH0JZ2JWDGRHD26BX74C.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf'}, {'filename': 'NMFC_NJ4WSZ8BUQAW48V6403N.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 3, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf'}, {'filename': 'NMFC_OR9EL08KIKNQPZ3UV3HH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf'}, {'filename': 'NMFC_R1V0MO844PBLWNEAUETU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf'}, {'filename': 'NMFC_RUDVGETVRZO7XX6YNW7I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf'}, {'filename': 'W_A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf'}, {'filename': 'W_DCY7SLNMWUXIENOREHQF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf'}, {'filename': 'W_DFY1VDZWR7NBDLJV02G2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf'}, {'filename': 'W_HFPAXYL947DH59AB12FL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf'}, {'filename': 'W_K9VSARJOKAIZHNJ5RBDT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf'}, {'filename': 'W_WRKSHW76B3QUG47QWR75.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf'}, {'filename': 'W_XCJLXZK140FUS8020ZAG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf'}]}
