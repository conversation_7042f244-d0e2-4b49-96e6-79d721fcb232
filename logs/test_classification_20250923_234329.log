2025-09-23 23:43:29,823 - INFO - Logging initialized. Log file: logs/test_classification_20250923_234329.log
2025-09-23 23:43:29,823 - INFO - 📁 Found 8 files to process
2025-09-23 23:43:29,823 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:43:29,823 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:43:29,823 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:43:29,823 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:43:29,823 - INFO - ⬆️ [23:43:29] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:43:31,649 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/0dede49b_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:43:31,650 - INFO - 🔍 [23:43:31] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:43:31,650 - INFO - ⬆️ [23:43:31] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:43:31,651 - INFO - Initializing TextractProcessor...
2025-09-23 23:43:31,674 - INFO - Initializing BedrockProcessor...
2025-09-23 23:43:31,678 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0dede49b_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:43:31,679 - INFO - Processing PDF from S3...
2025-09-23 23:43:31,679 - INFO - Downloading PDF from S3 to /tmp/tmpshi4efjr/0dede49b_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:43:33,303 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:43:33,303 - INFO - Splitting PDF into individual pages...
2025-09-23 23:43:33,305 - INFO - Splitting PDF 0dede49b_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:43:33,312 - INFO - Split PDF into 1 pages
2025-09-23 23:43:33,312 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:43:33,312 - INFO - Expected pages: [1]
2025-09-23 23:43:36,342 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/96beb481_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:43:36,342 - INFO - 🔍 [23:43:36] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:43:36,342 - INFO - ⬆️ [23:43:36] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:43:36,343 - INFO - Initializing TextractProcessor...
2025-09-23 23:43:36,352 - INFO - Initializing BedrockProcessor...
2025-09-23 23:43:36,356 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/96beb481_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:43:36,356 - INFO - Processing image from S3...
2025-09-23 23:43:37,216 - INFO - Page 1: Extracted 1260 characters, 84 lines from 0dede49b_BQJUG5URFR2GH9ECWFV4_06fd004d_page_001.pdf
2025-09-23 23:43:37,216 - INFO - Successfully processed page 1
2025-09-23 23:43:37,216 - INFO - Combined 1 pages into final text
2025-09-23 23:43:37,217 - INFO - Text validation for 0dede49b_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:43:37,217 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:43:37,217 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:43:37,228 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/72dc6724_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:43:37,228 - INFO - 🔍 [23:43:37] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:43:37,228 - INFO - ⬆️ [23:43:37] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:43:37,229 - INFO - Initializing TextractProcessor...
2025-09-23 23:43:37,245 - INFO - Initializing BedrockProcessor...
2025-09-23 23:43:37,248 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/72dc6724_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:43:37,248 - INFO - Processing PDF from S3...
2025-09-23 23:43:37,248 - INFO - Downloading PDF from S3 to /tmp/tmplerhgqmm/72dc6724_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:43:37,864 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/25f61bb2_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:43:37,865 - INFO - 🔍 [23:43:37] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:43:37,865 - INFO - ⬆️ [23:43:37] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:43:37,867 - INFO - Initializing TextractProcessor...
2025-09-23 23:43:37,883 - INFO - Initializing BedrockProcessor...
2025-09-23 23:43:37,886 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/25f61bb2_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:43:37,886 - INFO - Processing PDF from S3...
2025-09-23 23:43:37,887 - INFO - Downloading PDF from S3 to /tmp/tmp39snn81o/25f61bb2_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:43:39,261 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0dede49b_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:43:39,452 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:43:39,453 - INFO - Splitting PDF into individual pages...
2025-09-23 23:43:39,454 - INFO - Splitting PDF 72dc6724_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:43:39,460 - INFO - Split PDF into 1 pages
2025-09-23 23:43:39,460 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:43:39,460 - INFO - Expected pages: [1]
2025-09-23 23:43:40,034 - INFO - S3 Image temp/96beb481_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:43:40,035 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:43:40,035 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:43:40,729 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:43:40,729 - INFO - Splitting PDF into individual pages...
2025-09-23 23:43:40,732 - INFO - Splitting PDF 25f61bb2_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:43:40,747 - INFO - Split PDF into 7 pages
2025-09-23 23:43:40,747 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:43:40,747 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:43:42,341 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/49f97dad_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:43:42,342 - INFO - 🔍 [23:43:42] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:43:42,342 - INFO - Initializing TextractProcessor...
2025-09-23 23:43:42,346 - INFO - ⬆️ [23:43:42] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:43:42,368 - INFO - Initializing BedrockProcessor...
2025-09-23 23:43:42,372 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/49f97dad_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:43:42,372 - INFO - Processing image from S3...
2025-09-23 23:43:44,210 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/96beb481_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:43:44,490 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/119d582b_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:43:44,491 - INFO - 🔍 [23:43:44] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:43:44,492 - INFO - ⬆️ [23:43:44] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:43:44,494 - INFO - Initializing TextractProcessor...
2025-09-23 23:43:44,520 - INFO - Initializing BedrockProcessor...
2025-09-23 23:43:44,531 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/119d582b_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:43:44,532 - INFO - Processing image from S3...
2025-09-23 23:43:44,705 - INFO - Page 1: Extracted 519 characters, 34 lines from 72dc6724_KE7TCH9TPQZFVA5CZ3HT_318f552b_page_001.pdf
2025-09-23 23:43:44,705 - INFO - Successfully processed page 1
2025-09-23 23:43:44,705 - INFO - Combined 1 pages into final text
2025-09-23 23:43:44,705 - INFO - Text validation for 72dc6724_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:43:44,706 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:43:44,706 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:43:45,031 - INFO - Page 1: Extracted 1731 characters, 110 lines from 25f61bb2_O2IU5G77LYNTYE0RP1TI_d5953348_page_001.pdf
2025-09-23 23:43:45,031 - INFO - Successfully processed page 1
2025-09-23 23:43:45,122 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/fbab2053_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:43:45,122 - INFO - 🔍 [23:43:45] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:43:45,123 - INFO - ⬆️ [23:43:45] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:43:45,124 - INFO - Initializing TextractProcessor...
2025-09-23 23:43:45,139 - INFO - Initializing BedrockProcessor...
2025-09-23 23:43:45,142 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fbab2053_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:43:45,142 - INFO - Processing image from S3...
2025-09-23 23:43:45,634 - INFO - Page 2: Extracted 1821 characters, 105 lines from 25f61bb2_O2IU5G77LYNTYE0RP1TI_d5953348_page_002.pdf
2025-09-23 23:43:45,635 - INFO - Successfully processed page 2
2025-09-23 23:43:45,758 - INFO - Page 4: Extracted 2242 characters, 148 lines from 25f61bb2_O2IU5G77LYNTYE0RP1TI_d5953348_page_004.pdf
2025-09-23 23:43:45,758 - INFO - Successfully processed page 4
2025-09-23 23:43:45,947 - INFO - Page 6: Extracted 1973 characters, 129 lines from 25f61bb2_O2IU5G77LYNTYE0RP1TI_d5953348_page_006.pdf
2025-09-23 23:43:45,947 - INFO - Successfully processed page 6
2025-09-23 23:43:46,181 - INFO - Page 5: Extracted 2059 characters, 131 lines from 25f61bb2_O2IU5G77LYNTYE0RP1TI_d5953348_page_005.pdf
2025-09-23 23:43:46,181 - INFO - Successfully processed page 5
2025-09-23 23:43:46,356 - INFO - Page 3: Extracted 2265 characters, 147 lines from 25f61bb2_O2IU5G77LYNTYE0RP1TI_d5953348_page_003.pdf
2025-09-23 23:43:46,356 - INFO - Successfully processed page 3
2025-09-23 23:43:46,459 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/da0567ac_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:43:46,459 - INFO - 🔍 [23:43:46] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:43:46,461 - INFO - Initializing TextractProcessor...
2025-09-23 23:43:46,474 - INFO - Initializing BedrockProcessor...
2025-09-23 23:43:46,481 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/da0567ac_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:43:46,483 - INFO - Processing image from S3...
2025-09-23 23:43:46,503 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:43:46,503 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:43:46,818 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0dede49b_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:43:46,832 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-23 23:43:46,832 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:43:47,059 - INFO - S3 Image temp/fbab2053_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:43:47,060 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:43:47,060 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:43:47,195 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/96beb481_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:43:47,779 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/72dc6724_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:43:47,793 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:43:47,793 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:43:47,803 - INFO - Page 7: Extracted 417 characters, 27 lines from 25f61bb2_O2IU5G77LYNTYE0RP1TI_d5953348_page_007.pdf
2025-09-23 23:43:47,803 - INFO - Successfully processed page 7
2025-09-23 23:43:47,804 - INFO - Combined 7 pages into final text
2025-09-23 23:43:47,806 - INFO - Text validation for 25f61bb2_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:43:47,806 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:43:47,807 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:43:48,001 - INFO - S3 Image temp/49f97dad_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:43:48,001 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:43:48,001 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:43:48,089 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/72dc6724_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:43:48,548 - INFO - S3 Image temp/da0567ac_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:43:48,548 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:43:48,548 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:43:48,818 - INFO - S3 Image temp/119d582b_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:43:48,818 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:43:48,819 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:43:49,138 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fbab2053_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:43:49,143 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:43:49,144 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:43:49,442 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fbab2053_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:43:49,962 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/49f97dad_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:43:50,009 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:43:50,009 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:43:50,166 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/25f61bb2_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:43:50,346 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/49f97dad_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:43:50,460 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:43:50,461 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:43:50,768 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/25f61bb2_O2IU5G77LYNTYE0RP1TI.pdf
