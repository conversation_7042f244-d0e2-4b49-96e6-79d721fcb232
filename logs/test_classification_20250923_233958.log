2025-09-23 23:39:58,159 - INFO - Logging initialized. Log file: logs/test_classification_20250923_233958.log
2025-09-23 23:39:58,160 - INFO - 📁 Found 8 files to process
2025-09-23 23:39:58,160 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:39:58,160 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:39:58,160 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:39:58,160 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:39:58,160 - INFO - ⬆️ [23:39:58] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:39:59,896 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/b11c779c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:39:59,897 - INFO - 🔍 [23:39:59] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:39:59,897 - INFO - ⬆️ [23:39:59] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:39:59,900 - INFO - Initializing TextractProcessor...
2025-09-23 23:39:59,923 - INFO - Initializing BedrockProcessor...
2025-09-23 23:39:59,933 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b11c779c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:39:59,933 - INFO - Processing PDF from S3...
2025-09-23 23:39:59,934 - INFO - Downloading PDF from S3 to /tmp/tmptfbmjv96/b11c779c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:40:01,351 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:40:01,351 - INFO - Splitting PDF into individual pages...
2025-09-23 23:40:01,352 - INFO - Splitting PDF b11c779c_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:40:01,356 - INFO - Split PDF into 1 pages
2025-09-23 23:40:01,357 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:40:01,357 - INFO - Expected pages: [1]
2025-09-23 23:40:04,184 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/e2b393de_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:40:04,184 - INFO - 🔍 [23:40:04] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:40:04,185 - INFO - ⬆️ [23:40:04] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:40:04,185 - INFO - Initializing TextractProcessor...
2025-09-23 23:40:04,200 - INFO - Initializing BedrockProcessor...
2025-09-23 23:40:04,204 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e2b393de_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:40:04,204 - INFO - Processing image from S3...
2025-09-23 23:40:05,116 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/081e9706_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:40:05,117 - INFO - 🔍 [23:40:05] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:40:05,118 - INFO - ⬆️ [23:40:05] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:05,124 - INFO - Initializing TextractProcessor...
2025-09-23 23:40:05,138 - INFO - Initializing BedrockProcessor...
2025-09-23 23:40:05,142 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/081e9706_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:40:05,142 - INFO - Processing PDF from S3...
2025-09-23 23:40:05,142 - INFO - Downloading PDF from S3 to /tmp/tmpat3h8_mw/081e9706_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:40:05,730 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/b6339d8c_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:05,731 - INFO - 🔍 [23:40:05] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:05,731 - INFO - ⬆️ [23:40:05] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:40:05,732 - INFO - Initializing TextractProcessor...
2025-09-23 23:40:05,751 - INFO - Initializing BedrockProcessor...
2025-09-23 23:40:05,755 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b6339d8c_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:05,756 - INFO - Processing PDF from S3...
2025-09-23 23:40:05,756 - INFO - Downloading PDF from S3 to /tmp/tmp0aet7bzf/b6339d8c_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:05,944 - INFO - Page 1: Extracted 1260 characters, 84 lines from b11c779c_BQJUG5URFR2GH9ECWFV4_43f3b2b3_page_001.pdf
2025-09-23 23:40:05,944 - INFO - Successfully processed page 1
2025-09-23 23:40:05,945 - INFO - Combined 1 pages into final text
2025-09-23 23:40:05,945 - INFO - Text validation for b11c779c_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:40:05,945 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:40:05,945 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:40:07,284 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:40:07,284 - INFO - Splitting PDF into individual pages...
2025-09-23 23:40:07,285 - INFO - Splitting PDF 081e9706_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:40:07,290 - INFO - Split PDF into 1 pages
2025-09-23 23:40:07,290 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:40:07,290 - INFO - Expected pages: [1]
2025-09-23 23:40:07,711 - INFO - S3 Image temp/e2b393de_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:40:07,712 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:40:07,712 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:40:07,874 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:40:07,874 - INFO - Splitting PDF into individual pages...
2025-09-23 23:40:07,877 - INFO - Splitting PDF b6339d8c_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:40:07,887 - INFO - Split PDF into 7 pages
2025-09-23 23:40:07,887 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:40:07,887 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:40:07,957 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b11c779c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:40:10,001 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/6e0196c6_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:40:10,001 - INFO - 🔍 [23:40:10] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:40:10,002 - INFO - ⬆️ [23:40:10] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:40:10,002 - INFO - Initializing TextractProcessor...
2025-09-23 23:40:10,020 - INFO - Initializing BedrockProcessor...
2025-09-23 23:40:10,024 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6e0196c6_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:40:10,024 - INFO - Processing image from S3...
2025-09-23 23:40:10,848 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e2b393de_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:40:12,028 - INFO - Page 1: Extracted 1731 characters, 110 lines from b6339d8c_O2IU5G77LYNTYE0RP1TI_8e667ae7_page_001.pdf
2025-09-23 23:40:12,028 - INFO - Successfully processed page 1
2025-09-23 23:40:12,105 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/ca353e41_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:40:12,105 - INFO - 🔍 [23:40:12] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:40:12,105 - INFO - ⬆️ [23:40:12] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:40:12,106 - INFO - Initializing TextractProcessor...
2025-09-23 23:40:12,113 - INFO - Initializing BedrockProcessor...
2025-09-23 23:40:12,115 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ca353e41_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:40:12,115 - INFO - Processing image from S3...
2025-09-23 23:40:12,346 - INFO - Page 2: Extracted 1821 characters, 105 lines from b6339d8c_O2IU5G77LYNTYE0RP1TI_8e667ae7_page_002.pdf
2025-09-23 23:40:12,346 - INFO - Successfully processed page 2
2025-09-23 23:40:12,373 - INFO - Page 1: Extracted 519 characters, 34 lines from 081e9706_KE7TCH9TPQZFVA5CZ3HT_6ea94f86_page_001.pdf
2025-09-23 23:40:12,373 - INFO - Successfully processed page 1
2025-09-23 23:40:12,374 - INFO - Combined 1 pages into final text
2025-09-23 23:40:12,374 - INFO - Text validation for 081e9706_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:40:12,374 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:40:12,374 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:40:12,740 - INFO - Page 6: Extracted 1973 characters, 129 lines from b6339d8c_O2IU5G77LYNTYE0RP1TI_8e667ae7_page_006.pdf
2025-09-23 23:40:12,740 - INFO - Successfully processed page 6
2025-09-23 23:40:12,792 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/90a09e9d_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:40:12,792 - INFO - 🔍 [23:40:12] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:40:12,792 - INFO - ⬆️ [23:40:12] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:40:12,794 - INFO - Initializing TextractProcessor...
2025-09-23 23:40:12,810 - INFO - Initializing BedrockProcessor...
2025-09-23 23:40:12,813 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/90a09e9d_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:40:12,819 - INFO - Processing image from S3...
2025-09-23 23:40:12,838 - INFO - Page 3: Extracted 2265 characters, 147 lines from b6339d8c_O2IU5G77LYNTYE0RP1TI_8e667ae7_page_003.pdf
2025-09-23 23:40:12,845 - INFO - Page 5: Extracted 2059 characters, 131 lines from b6339d8c_O2IU5G77LYNTYE0RP1TI_8e667ae7_page_005.pdf
2025-09-23 23:40:12,845 - INFO - Successfully processed page 3
2025-09-23 23:40:12,846 - INFO - Successfully processed page 5
2025-09-23 23:40:12,900 - INFO - Page 4: Extracted 2242 characters, 148 lines from b6339d8c_O2IU5G77LYNTYE0RP1TI_8e667ae7_page_004.pdf
2025-09-23 23:40:12,900 - INFO - Successfully processed page 4
2025-09-23 23:40:14,083 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/db9d25a5_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:40:14,083 - INFO - 🔍 [23:40:14] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:40:14,085 - INFO - Initializing TextractProcessor...
2025-09-23 23:40:14,096 - INFO - Initializing BedrockProcessor...
2025-09-23 23:40:14,102 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/db9d25a5_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:40:14,103 - INFO - Processing image from S3...
2025-09-23 23:40:14,125 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:40:14,125 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:40:14,433 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b11c779c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:40:14,445 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:40:14,445 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:40:14,653 - INFO - Page 7: Extracted 417 characters, 27 lines from b6339d8c_O2IU5G77LYNTYE0RP1TI_8e667ae7_page_007.pdf
2025-09-23 23:40:14,653 - INFO - Successfully processed page 7
2025-09-23 23:40:14,654 - INFO - Combined 7 pages into final text
2025-09-23 23:40:14,655 - INFO - Text validation for b6339d8c_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:40:14,655 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:40:14,655 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:40:14,737 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e2b393de_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:40:14,816 - INFO - S3 Image temp/90a09e9d_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:40:14,817 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:40:14,817 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:40:15,865 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/081e9706_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:40:15,873 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:40:15,873 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:40:16,006 - INFO - S3 Image temp/6e0196c6_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:40:16,006 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:40:16,006 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:40:16,134 - INFO - S3 Image temp/db9d25a5_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:40:16,135 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:40:16,135 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:40:16,168 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/081e9706_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:40:16,195 - INFO - S3 Image temp/ca353e41_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:40:16,195 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:40:16,196 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:40:16,920 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/90a09e9d_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:40:16,926 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:40:16,927 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:40:17,192 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b6339d8c_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:17,218 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/90a09e9d_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:40:17,346 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:40:17,346 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:17,639 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b6339d8c_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:18,159 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6e0196c6_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:40:18,202 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:40:18,202 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:40:18,462 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/db9d25a5_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:40:18,514 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6e0196c6_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:40:18,519 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:40:18,519 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:40:18,834 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/db9d25a5_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:40:18,919 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ca353e41_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:40:18,940 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:40:18,940 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:40:19,244 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ca353e41_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:40:19,245 - INFO - 
📊 Processing Summary:
2025-09-23 23:40:19,245 - INFO -    Total files: 8
2025-09-23 23:40:19,245 - INFO -    Successful: 8
2025-09-23 23:40:19,245 - INFO -    Failed: 0
2025-09-23 23:40:19,245 - INFO -    Duration: 21.08 seconds
2025-09-23 23:40:19,245 - INFO -    Output directory: output
2025-09-23 23:40:19,245 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:40:19,245 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:40:19,245 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:40:19,245 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:40:19,245 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:40:19,245 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:40:19,245 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:40:19,245 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:40:19,245 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:40:19,246 - INFO - 
============================================================================================================================================
2025-09-23 23:40:19,246 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:40:19,246 - INFO - ============================================================================================================================================
2025-09-23 23:40:19,246 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:40:19,246 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:40:19,246 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:40:19,246 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:40:19,246 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:40:19,246 - INFO - 
2025-09-23 23:40:19,246 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      other                run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:40:19,246 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:40:19,247 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:40:19,247 - INFO - 
2025-09-23 23:40:19,247 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      other                run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:40:19,247 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:40:19,247 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:40:19,247 - INFO - 
2025-09-23 23:40:19,247 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:40:19,247 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:19,247 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,247 - INFO - 
2025-09-23 23:40:19,247 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:40:19,247 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:19,247 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,247 - INFO - 
2025-09-23 23:40:19,247 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:40:19,247 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:19,247 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,247 - INFO - 
2025-09-23 23:40:19,247 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:40:19,247 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:19,247 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,248 - INFO - 
2025-09-23 23:40:19,248 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:40:19,248 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:19,248 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,248 - INFO - 
2025-09-23 23:40:19,248 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:40:19,248 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:19,248 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,248 - INFO - 
2025-09-23 23:40:19,248 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:40:19,248 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:40:19,248 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,248 - INFO - 
2025-09-23 23:40:19,248 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:40:19,248 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:40:19,248 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:40:19,248 - INFO - 
2025-09-23 23:40:19,248 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:40:19,248 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:40:19,249 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:40:19,249 - INFO - 
2025-09-23 23:40:19,249 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:40:19,249 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:40:19,249 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:40:19,249 - INFO - 
2025-09-23 23:40:19,249 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:40:19,249 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:40:19,249 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:40:19,249 - INFO - 
2025-09-23 23:40:19,249 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:40:19,249 - INFO - Total entries: 14
2025-09-23 23:40:19,249 - INFO - ============================================================================================================================================
2025-09-23 23:40:19,249 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:40:19,249 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:40:19,249 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:40:19,249 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → other           | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:40:19,249 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → other           | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:40:19,249 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,250 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,250 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,250 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,250 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,250 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,250 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:40:19,250 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:40:19,250 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:40:19,250 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:40:19,250 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:40:19,250 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:40:19,250 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 21.084623, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
