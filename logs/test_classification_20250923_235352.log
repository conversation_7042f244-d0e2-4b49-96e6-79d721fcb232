2025-09-23 23:53:52,670 - INFO - Logging initialized. Log file: logs/test_classification_20250923_235352.log
2025-09-23 23:53:52,671 - INFO - 📁 Found 8 files to process
2025-09-23 23:53:52,671 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:53:52,671 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:53:52,671 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:53:52,671 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:53:52,671 - INFO - ⬆️ [23:53:52] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:53:54,217 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/809f2a76_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:53:54,218 - INFO - 🔍 [23:53:54] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:53:54,219 - INFO - ⬆️ [23:53:54] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:53:54,221 - INFO - Initializing TextractProcessor...
2025-09-23 23:53:54,241 - INFO - Initializing BedrockProcessor...
2025-09-23 23:53:54,247 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/809f2a76_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:53:54,247 - INFO - Processing PDF from S3...
2025-09-23 23:53:54,247 - INFO - Downloading PDF from S3 to /tmp/tmpfxwxfh2q/809f2a76_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:53:55,551 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:53:55,551 - INFO - Splitting PDF into individual pages...
2025-09-23 23:53:55,552 - INFO - Splitting PDF 809f2a76_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:53:55,556 - INFO - Split PDF into 1 pages
2025-09-23 23:53:55,556 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:53:55,556 - INFO - Expected pages: [1]
2025-09-23 23:53:59,904 - INFO - Page 1: Extracted 1260 characters, 84 lines from 809f2a76_BQJUG5URFR2GH9ECWFV4_e62676f5_page_001.pdf
2025-09-23 23:53:59,904 - INFO - Successfully processed page 1
2025-09-23 23:53:59,904 - INFO - Combined 1 pages into final text
2025-09-23 23:53:59,904 - INFO - Text validation for 809f2a76_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:53:59,905 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:53:59,905 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:00,536 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/5ef61fa9_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:00,536 - INFO - 🔍 [23:54:00] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:00,537 - INFO - ⬆️ [23:54:00] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:00,538 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:00,555 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:00,560 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5ef61fa9_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:00,561 - INFO - Processing image from S3...
2025-09-23 23:54:01,507 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/88b1582e_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:01,507 - INFO - 🔍 [23:54:01] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:01,508 - INFO - ⬆️ [23:54:01] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:01,510 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:01,527 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:01,532 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/88b1582e_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:01,532 - INFO - Processing PDF from S3...
2025-09-23 23:54:01,532 - INFO - Downloading PDF from S3 to /tmp/tmp6bnkh_ty/88b1582e_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:01,897 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/809f2a76_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:02,255 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/e94a2043_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:02,255 - INFO - 🔍 [23:54:02] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:02,256 - INFO - ⬆️ [23:54:02] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:02,258 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:02,273 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:02,279 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e94a2043_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:02,280 - INFO - Processing PDF from S3...
2025-09-23 23:54:02,280 - INFO - Downloading PDF from S3 to /tmp/tmpyp5e6zci/e94a2043_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:03,608 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:54:03,608 - INFO - Splitting PDF into individual pages...
2025-09-23 23:54:03,609 - INFO - Splitting PDF 88b1582e_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:54:03,613 - INFO - Split PDF into 1 pages
2025-09-23 23:54:03,613 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:54:03,613 - INFO - Expected pages: [1]
2025-09-23 23:54:04,155 - INFO - S3 Image temp/5ef61fa9_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:54:04,155 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:04,155 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:04,159 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:54:04,159 - INFO - Splitting PDF into individual pages...
2025-09-23 23:54:04,161 - INFO - Splitting PDF e94a2043_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:54:04,172 - INFO - Split PDF into 7 pages
2025-09-23 23:54:04,172 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:54:04,172 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:54:06,475 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/b7700d9b_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:06,475 - INFO - 🔍 [23:54:06] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:06,476 - INFO - ⬆️ [23:54:06] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:06,481 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:06,496 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:06,500 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b7700d9b_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:06,500 - INFO - Processing image from S3...
2025-09-23 23:54:06,872 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5ef61fa9_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:08,157 - INFO - Page 2: Extracted 1821 characters, 105 lines from e94a2043_O2IU5G77LYNTYE0RP1TI_ad7dbdcc_page_002.pdf
2025-09-23 23:54:08,158 - INFO - Successfully processed page 2
2025-09-23 23:54:08,317 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/001450d3_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:08,317 - INFO - 🔍 [23:54:08] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:08,318 - INFO - ⬆️ [23:54:08] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:08,320 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:08,336 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:08,338 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/001450d3_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:08,338 - INFO - Processing image from S3...
2025-09-23 23:54:08,673 - INFO - Page 1: Extracted 1731 characters, 110 lines from e94a2043_O2IU5G77LYNTYE0RP1TI_ad7dbdcc_page_001.pdf
2025-09-23 23:54:08,673 - INFO - Successfully processed page 1
2025-09-23 23:54:08,687 - INFO - Page 1: Extracted 519 characters, 34 lines from 88b1582e_KE7TCH9TPQZFVA5CZ3HT_b9b030ca_page_001.pdf
2025-09-23 23:54:08,687 - INFO - Successfully processed page 1
2025-09-23 23:54:08,687 - INFO - Combined 1 pages into final text
2025-09-23 23:54:08,687 - INFO - Text validation for 88b1582e_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:54:08,687 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:08,687 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:08,865 - INFO - Page 3: Extracted 2265 characters, 147 lines from e94a2043_O2IU5G77LYNTYE0RP1TI_ad7dbdcc_page_003.pdf
2025-09-23 23:54:08,865 - INFO - Successfully processed page 3
2025-09-23 23:54:08,885 - INFO - Page 5: Extracted 2059 characters, 131 lines from e94a2043_O2IU5G77LYNTYE0RP1TI_ad7dbdcc_page_005.pdf
2025-09-23 23:54:08,885 - INFO - Successfully processed page 5
2025-09-23 23:54:08,924 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/78aabb15_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:08,924 - INFO - 🔍 [23:54:08] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:08,925 - INFO - ⬆️ [23:54:08] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:08,927 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:08,947 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:08,954 - INFO - Page 4: Extracted 2242 characters, 148 lines from e94a2043_O2IU5G77LYNTYE0RP1TI_ad7dbdcc_page_004.pdf
2025-09-23 23:54:08,955 - INFO - Successfully processed page 4
2025-09-23 23:54:08,955 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/78aabb15_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:08,956 - INFO - Processing image from S3...
2025-09-23 23:54:09,010 - INFO - Page 6: Extracted 1973 characters, 129 lines from e94a2043_O2IU5G77LYNTYE0RP1TI_ad7dbdcc_page_006.pdf
2025-09-23 23:54:09,010 - INFO - Successfully processed page 6
2025-09-23 23:54:09,921 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/bc7fc82a_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:09,921 - INFO - 🔍 [23:54:09] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:09,923 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:09,934 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:09,941 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bc7fc82a_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:09,942 - INFO - Processing image from S3...
2025-09-23 23:54:09,966 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:09,966 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:54:10,263 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/809f2a76_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:10,275 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:10,275 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:54:10,564 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5ef61fa9_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:10,812 - INFO - Page 7: Extracted 417 characters, 27 lines from e94a2043_O2IU5G77LYNTYE0RP1TI_ad7dbdcc_page_007.pdf
2025-09-23 23:54:10,812 - INFO - Successfully processed page 7
2025-09-23 23:54:10,813 - INFO - Combined 7 pages into final text
2025-09-23 23:54:10,814 - INFO - Text validation for e94a2043_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:54:10,814 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:10,814 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:10,871 - INFO - S3 Image temp/78aabb15_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:54:10,871 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:10,871 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:11,113 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/88b1582e_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:11,126 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-23 23:54:11,126 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:54:11,426 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/88b1582e_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:11,635 - INFO - S3 Image temp/b7700d9b_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:54:11,635 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:11,635 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:11,958 - INFO - S3 Image temp/bc7fc82a_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:54:11,959 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:11,959 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:12,278 - INFO - S3 Image temp/001450d3_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:54:12,278 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:12,279 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:12,798 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/78aabb15_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:12,806 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:12,807 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:54:13,097 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/78aabb15_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:13,372 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b7700d9b_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:13,409 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:13,409 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:54:13,572 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e94a2043_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:13,587 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bc7fc82a_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:13,744 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b7700d9b_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:13,856 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:13,857 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:14,150 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e94a2043_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:14,153 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:54:14,154 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:54:14,461 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bc7fc82a_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:15,942 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/001450d3_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:15,966 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:15,966 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:54:16,253 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/001450d3_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:16,254 - INFO - 
📊 Processing Summary:
2025-09-23 23:54:16,254 - INFO -    Total files: 8
2025-09-23 23:54:16,254 - INFO -    Successful: 8
2025-09-23 23:54:16,254 - INFO -    Failed: 0
2025-09-23 23:54:16,254 - INFO -    Duration: 23.58 seconds
2025-09-23 23:54:16,254 - INFO -    Output directory: output
2025-09-23 23:54:16,254 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:54:16,255 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:54:16,255 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:54:16,255 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-23 23:54:16,255 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:54:16,255 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:54:16,255 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:54:16,255 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:54:16,255 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:54:16,256 - INFO - 
============================================================================================================================================
2025-09-23 23:54:16,256 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:54:16,256 - INFO - ============================================================================================================================================
2025-09-23 23:54:16,256 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:54:16,256 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:54:16,256 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:54:16,256 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:16,256 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:54:16,256 - INFO - 
2025-09-23 23:54:16,256 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:54:16,256 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:16,257 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:54:16,257 - INFO - 
2025-09-23 23:54:16,257 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      coa                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:54:16,257 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:16,257 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:54:16,257 - INFO - 
2025-09-23 23:54:16,257 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:16,257 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:16,257 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,257 - INFO - 
2025-09-23 23:54:16,257 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:16,257 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:16,257 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,257 - INFO - 
2025-09-23 23:54:16,257 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:16,257 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:16,257 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,258 - INFO - 
2025-09-23 23:54:16,258 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:16,258 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:16,258 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,258 - INFO - 
2025-09-23 23:54:16,258 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:16,258 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:16,258 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,258 - INFO - 
2025-09-23 23:54:16,258 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:16,258 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:16,258 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,258 - INFO - 
2025-09-23 23:54:16,258 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:16,258 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:16,258 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,258 - INFO - 
2025-09-23 23:54:16,258 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:54:16,259 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:16,259 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:54:16,259 - INFO - 
2025-09-23 23:54:16,259 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:54:16,259 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:16,259 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:54:16,259 - INFO - 
2025-09-23 23:54:16,259 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:54:16,259 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:16,259 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:54:16,259 - INFO - 
2025-09-23 23:54:16,259 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:54:16,259 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:16,259 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:54:16,259 - INFO - 
2025-09-23 23:54:16,259 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:54:16,259 - INFO - Total entries: 14
2025-09-23 23:54:16,259 - INFO - ============================================================================================================================================
2025-09-23 23:54:16,259 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:54:16,259 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:54:16,260 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:54:16,260 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:54:16,260 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → coa             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:54:16,260 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,260 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,260 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,260 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,260 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,260 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,260 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:16,260 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:54:16,260 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:54:16,260 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:54:16,260 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:54:16,260 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:54:16,260 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 23.582977, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
