2025-09-24 00:06:08,229 - INFO - Logging initialized. Log file: logs/test_classification_20250924_000608.log
2025-09-24 00:06:08,230 - INFO - 📁 Found 8 files to process
2025-09-24 00:06:08,230 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 00:06:08,230 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-24 00:06:08,230 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-24 00:06:08,230 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-24 00:06:08,230 - INFO - ⬆️ [00:06:08] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:06:09,853 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/7f715db4_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:06:09,853 - INFO - 🔍 [00:06:09] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:06:09,854 - INFO - Initializing TextractProcessor...
2025-09-24 00:06:09,854 - INFO - ⬆️ [00:06:09] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:06:09,875 - INFO - Initializing BedrockProcessor...
2025-09-24 00:06:09,879 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7f715db4_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:06:09,880 - INFO - Processing PDF from S3...
2025-09-24 00:06:09,880 - INFO - Downloading PDF from S3 to /tmp/tmpnz7wpgma/7f715db4_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:06:11,317 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:06:11,317 - INFO - Splitting PDF into individual pages...
2025-09-24 00:06:11,319 - INFO - Splitting PDF 7f715db4_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-24 00:06:11,325 - INFO - Split PDF into 1 pages
2025-09-24 00:06:11,325 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:06:11,325 - INFO - Expected pages: [1]
2025-09-24 00:06:13,087 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/0d3ab58c_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:06:13,087 - INFO - 🔍 [00:06:13] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:06:13,088 - INFO - ⬆️ [00:06:13] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:06:13,094 - INFO - Initializing TextractProcessor...
2025-09-24 00:06:13,105 - INFO - Initializing BedrockProcessor...
2025-09-24 00:06:13,110 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0d3ab58c_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:06:13,110 - INFO - Processing image from S3...
2025-09-24 00:06:13,763 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/c7deae61_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:06:13,763 - INFO - 🔍 [00:06:13] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:06:13,764 - INFO - ⬆️ [00:06:13] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:13,766 - INFO - Initializing TextractProcessor...
2025-09-24 00:06:13,786 - INFO - Initializing BedrockProcessor...
2025-09-24 00:06:13,790 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c7deae61_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:06:13,790 - INFO - Processing PDF from S3...
2025-09-24 00:06:13,790 - INFO - Downloading PDF from S3 to /tmp/tmpp7_s33sf/c7deae61_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:06:14,418 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/ebb4b8b5_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:14,418 - INFO - 🔍 [00:06:14] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:14,419 - INFO - ⬆️ [00:06:14] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:06:14,421 - INFO - Initializing TextractProcessor...
2025-09-24 00:06:14,439 - INFO - Initializing BedrockProcessor...
2025-09-24 00:06:14,443 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ebb4b8b5_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:14,444 - INFO - Processing PDF from S3...
2025-09-24 00:06:14,444 - INFO - Downloading PDF from S3 to /tmp/tmpgdk6dcac/ebb4b8b5_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:15,680 - INFO - Page 1: Extracted 1260 characters, 84 lines from 7f715db4_BQJUG5URFR2GH9ECWFV4_1235ad8e_page_001.pdf
2025-09-24 00:06:15,681 - INFO - Successfully processed page 1
2025-09-24 00:06:15,681 - INFO - Combined 1 pages into final text
2025-09-24 00:06:15,681 - INFO - Text validation for 7f715db4_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-24 00:06:15,681 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:06:15,681 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:06:15,882 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 00:06:15,882 - INFO - Splitting PDF into individual pages...
2025-09-24 00:06:15,883 - INFO - Splitting PDF c7deae61_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-24 00:06:15,888 - INFO - Split PDF into 1 pages
2025-09-24 00:06:15,888 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:06:15,888 - INFO - Expected pages: [1]
2025-09-24 00:06:16,398 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:06:16,398 - INFO - Splitting PDF into individual pages...
2025-09-24 00:06:16,400 - INFO - Splitting PDF ebb4b8b5_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-24 00:06:16,406 - INFO - Split PDF into 7 pages
2025-09-24 00:06:16,406 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:06:16,406 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 00:06:16,659 - INFO - S3 Image temp/0d3ab58c_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-24 00:06:16,659 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:06:16,659 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:06:17,316 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/dedecc28_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:06:17,317 - INFO - 🔍 [00:06:17] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:06:17,319 - INFO - ⬆️ [00:06:17] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:06:17,322 - INFO - Initializing TextractProcessor...
2025-09-24 00:06:17,339 - INFO - Initializing BedrockProcessor...
2025-09-24 00:06:17,344 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dedecc28_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:06:17,344 - INFO - Processing image from S3...
2025-09-24 00:06:17,584 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7f715db4_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:06:19,128 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/4d38a3f5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:06:19,129 - INFO - 🔍 [00:06:19] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:06:19,130 - INFO - ⬆️ [00:06:19] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:06:19,131 - INFO - Initializing TextractProcessor...
2025-09-24 00:06:19,149 - INFO - Initializing BedrockProcessor...
2025-09-24 00:06:19,183 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4d38a3f5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:06:19,184 - INFO - Processing image from S3...
2025-09-24 00:06:19,796 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/a2ecc8bb_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:06:19,797 - INFO - 🔍 [00:06:19] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:06:19,798 - INFO - ⬆️ [00:06:19] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:06:19,799 - INFO - Initializing TextractProcessor...
2025-09-24 00:06:19,815 - INFO - Initializing BedrockProcessor...
2025-09-24 00:06:19,819 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a2ecc8bb_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:06:19,819 - INFO - Processing image from S3...
2025-09-24 00:06:20,310 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0d3ab58c_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:06:20,645 - INFO - Page 1: Extracted 1731 characters, 110 lines from ebb4b8b5_O2IU5G77LYNTYE0RP1TI_d323716f_page_001.pdf
2025-09-24 00:06:20,645 - INFO - Successfully processed page 1
2025-09-24 00:06:20,794 - INFO - Page 1: Extracted 519 characters, 34 lines from c7deae61_KE7TCH9TPQZFVA5CZ3HT_66d6eaa0_page_001.pdf
2025-09-24 00:06:20,795 - INFO - Successfully processed page 1
2025-09-24 00:06:20,796 - INFO - Combined 1 pages into final text
2025-09-24 00:06:20,796 - INFO - Text validation for c7deae61_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-24 00:06:20,796 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:06:20,796 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:06:20,890 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/2d710aaf_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:06:20,890 - INFO - 🔍 [00:06:20] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:06:20,891 - INFO - Initializing TextractProcessor...
2025-09-24 00:06:20,896 - INFO - Initializing BedrockProcessor...
2025-09-24 00:06:20,898 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2d710aaf_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:06:20,899 - INFO - Processing image from S3...
2025-09-24 00:06:20,913 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:06:20,913 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:06:21,199 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7f715db4_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:06:21,211 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 00:06:21,212 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:06:21,464 - INFO - Page 2: Extracted 1821 characters, 105 lines from ebb4b8b5_O2IU5G77LYNTYE0RP1TI_d323716f_page_002.pdf
2025-09-24 00:06:21,464 - INFO - Successfully processed page 2
2025-09-24 00:06:21,499 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0d3ab58c_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:06:21,608 - INFO - Page 5: Extracted 2059 characters, 131 lines from ebb4b8b5_O2IU5G77LYNTYE0RP1TI_d323716f_page_005.pdf
2025-09-24 00:06:21,608 - INFO - Successfully processed page 5
2025-09-24 00:06:21,671 - INFO - Page 3: Extracted 2265 characters, 147 lines from ebb4b8b5_O2IU5G77LYNTYE0RP1TI_d323716f_page_003.pdf
2025-09-24 00:06:21,671 - INFO - Successfully processed page 3
2025-09-24 00:06:21,861 - INFO - Page 6: Extracted 1973 characters, 129 lines from ebb4b8b5_O2IU5G77LYNTYE0RP1TI_d323716f_page_006.pdf
2025-09-24 00:06:21,862 - INFO - Successfully processed page 6
2025-09-24 00:06:22,028 - INFO - S3 Image temp/a2ecc8bb_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-24 00:06:22,028 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:06:22,028 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:06:22,171 - INFO - Page 4: Extracted 2242 characters, 148 lines from ebb4b8b5_O2IU5G77LYNTYE0RP1TI_d323716f_page_004.pdf
2025-09-24 00:06:22,171 - INFO - Successfully processed page 4
2025-09-24 00:06:22,791 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c7deae61_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:06:22,801 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:06:22,801 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:06:23,087 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c7deae61_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:06:23,134 - INFO - S3 Image temp/2d710aaf_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-24 00:06:23,134 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:06:23,134 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:06:23,371 - INFO - S3 Image temp/dedecc28_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-24 00:06:23,371 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:06:23,371 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:06:23,587 - INFO - S3 Image temp/4d38a3f5_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-24 00:06:23,588 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:06:23,588 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:06:23,601 - INFO - Page 7: Extracted 417 characters, 27 lines from ebb4b8b5_O2IU5G77LYNTYE0RP1TI_d323716f_page_007.pdf
2025-09-24 00:06:23,601 - INFO - Successfully processed page 7
2025-09-24 00:06:23,602 - INFO - Combined 7 pages into final text
2025-09-24 00:06:23,604 - INFO - Text validation for ebb4b8b5_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-24 00:06:23,604 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:06:23,604 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:06:24,091 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a2ecc8bb_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:06:24,096 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:06:24,096 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:06:24,381 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a2ecc8bb_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:06:25,309 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dedecc28_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:06:25,314 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2d710aaf_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:06:25,366 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:06:25,366 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:06:25,654 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dedecc28_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:06:25,658 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 00:06:25,658 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:06:25,946 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4d38a3f5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:06:25,946 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2d710aaf_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:06:25,966 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:06:25,966 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:06:26,254 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4d38a3f5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:06:26,296 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ebb4b8b5_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:26,425 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:06:26,425 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,721 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ebb4b8b5_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:26,723 - INFO - 
📊 Processing Summary:
2025-09-24 00:06:26,723 - INFO -    Total files: 8
2025-09-24 00:06:26,723 - INFO -    Successful: 8
2025-09-24 00:06:26,723 - INFO -    Failed: 0
2025-09-24 00:06:26,723 - INFO -    Duration: 18.49 seconds
2025-09-24 00:06:26,723 - INFO -    Output directory: output
2025-09-24 00:06:26,723 - INFO - 
📋 Successfully Processed Files:
2025-09-24 00:06:26,723 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:06:26,723 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 00:06:26,723 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:06:26,723 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-24 00:06:26,723 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:06:26,723 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:06:26,723 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:06:26,723 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 00:06:26,724 - INFO - 
============================================================================================================================================
2025-09-24 00:06:26,724 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 00:06:26,724 - INFO - ============================================================================================================================================
2025-09-24 00:06:26,724 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 00:06:26,724 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:06:26,724 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      ingate               run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      log                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:06:26,724 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:06:26,724 - INFO - 
2025-09-24 00:06:26,724 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-24 00:06:26,724 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:06:26,725 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:06:26,725 - INFO - 
2025-09-24 00:06:26,725 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-24 00:06:26,725 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:06:26,725 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:06:26,725 - INFO - 
2025-09-24 00:06:26,725 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-24 00:06:26,725 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:06:26,725 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:06:26,725 - INFO - 
2025-09-24 00:06:26,725 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:06:26,725 - INFO - Total entries: 14
2025-09-24 00:06:26,725 - INFO - ============================================================================================================================================
2025-09-24 00:06:26,725 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 00:06:26,725 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:06:26,725 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:06:26,725 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → ingate          | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:06:26,725 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → log             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:06:26,725 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,725 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,725 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,725 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,725 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,725 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,725 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:06:26,725 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:06:26,725 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:06:26,725 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:06:26,725 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:06:26,725 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:06:26,725 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 18.493183, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
