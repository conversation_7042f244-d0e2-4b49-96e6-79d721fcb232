2025-09-24 12:26:10,675 - INFO - Logging initialized. Log file: logs/test_classification_20250924_122610.log
2025-09-24 12:26:10,675 - INFO - 📁 Found 13 files to process
2025-09-24 12:26:10,675 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 12:26:10,676 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 12:26:10,676 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 12:26:10,676 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 12:26:10,676 - INFO - ⬆️ [12:26:10] Uploading: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:26:12,666 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/709bdf24_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:26:12,666 - INFO - 🔍 [12:26:12] Starting classification: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:26:12,667 - INFO - ⬆️ [12:26:12] Uploading: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:26:12,669 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:12,701 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:12,727 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/709bdf24_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:26:12,728 - INFO - Processing PDF from S3...
2025-09-24 12:26:12,729 - INFO - Downloading PDF from S3 to /tmp/tmpc_89yrcx/709bdf24_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:26:13,791 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/40453711_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:26:13,791 - INFO - 🔍 [12:26:13] Starting classification: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:26:13,792 - INFO - ⬆️ [12:26:13] Uploading: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:26:13,794 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:13,808 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:13,812 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/40453711_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:26:13,812 - INFO - Processing PDF from S3...
2025-09-24 12:26:13,812 - INFO - Downloading PDF from S3 to /tmp/tmpcu2ax196/40453711_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:26:14,215 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:26:14,215 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:14,216 - INFO - Splitting PDF 709bdf24_I_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 12:26:14,219 - INFO - Split PDF into 1 pages
2025-09-24 12:26:14,219 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:14,219 - INFO - Expected pages: [1]
2025-09-24 12:26:15,122 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/eb89413e_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:26:15,122 - INFO - 🔍 [12:26:15] Starting classification: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:26:15,123 - INFO - ⬆️ [12:26:15] Uploading: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:26:15,123 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:15,144 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:15,149 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eb89413e_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:26:15,150 - INFO - Processing PDF from S3...
2025-09-24 12:26:15,150 - INFO - Downloading PDF from S3 to /tmp/tmpfcpazf5g/eb89413e_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:26:15,500 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:26:15,501 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:15,502 - INFO - Splitting PDF 40453711_NMFC_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 12:26:15,505 - INFO - Split PDF into 2 pages
2025-09-24 12:26:15,505 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:15,505 - INFO - Expected pages: [1, 2]
2025-09-24 12:26:16,949 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/2aff92c6_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:26:16,949 - INFO - 🔍 [12:26:16] Starting classification: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:26:16,950 - INFO - ⬆️ [12:26:16] Uploading: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:26:16,951 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:16,968 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:16,972 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2aff92c6_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:26:16,972 - INFO - Processing PDF from S3...
2025-09-24 12:26:16,973 - INFO - Downloading PDF from S3 to /tmp/tmpdlkjemlq/2aff92c6_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:26:17,076 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:26:17,077 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:17,078 - INFO - Splitting PDF eb89413e_NMFC_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 12:26:17,084 - INFO - Split PDF into 3 pages
2025-09-24 12:26:17,084 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:17,084 - INFO - Expected pages: [1, 2, 3]
2025-09-24 12:26:17,534 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/0719a867_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:26:17,534 - INFO - 🔍 [12:26:17] Starting classification: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:26:17,535 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:17,535 - INFO - ⬆️ [12:26:17] Uploading: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:26:17,548 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:17,558 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0719a867_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:26:17,559 - INFO - Processing PDF from S3...
2025-09-24 12:26:17,559 - INFO - Downloading PDF from S3 to /tmp/tmpvpy45_d5/0719a867_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:26:18,130 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/494cce76_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:26:18,130 - INFO - 🔍 [12:26:18] Starting classification: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:26:18,131 - INFO - ⬆️ [12:26:18] Uploading: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:26:18,132 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:18,156 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:18,160 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/494cce76_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:26:18,161 - INFO - Processing PDF from S3...
2025-09-24 12:26:18,161 - INFO - Downloading PDF from S3 to /tmp/tmp28f7b75b/494cce76_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:26:18,378 - INFO - Page 1: Extracted 589 characters, 36 lines from 709bdf24_I_QHD3LC0DU6S8O2YVVS60_2dc782f8_page_001.pdf
2025-09-24 12:26:18,379 - INFO - Successfully processed page 1
2025-09-24 12:26:18,379 - INFO - Combined 1 pages into final text
2025-09-24 12:26:18,379 - INFO - Text validation for 709bdf24_I_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 12:26:18,380 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:18,380 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:18,798 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/3d2cf46a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:26:18,799 - INFO - 🔍 [12:26:18] Starting classification: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:26:18,800 - INFO - ⬆️ [12:26:18] Uploading: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:26:18,801 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:26:18,801 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:18,802 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:18,843 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:18,845 - INFO - Splitting PDF 0719a867_NMFC_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 12:26:18,845 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3d2cf46a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:26:18,847 - INFO - Processing PDF from S3...
2025-09-24 12:26:18,848 - INFO - Downloading PDF from S3 to /tmp/tmpo4dob3g6/3d2cf46a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:26:18,851 - INFO - Split PDF into 2 pages
2025-09-24 12:26:18,852 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:18,852 - INFO - Expected pages: [1, 2]
2025-09-24 12:26:19,395 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/5e1e2bce_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:26:19,395 - INFO - 🔍 [12:26:19] Starting classification: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:26:19,396 - INFO - ⬆️ [12:26:19] Uploading: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:26:19,397 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:19,419 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:19,424 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5e1e2bce_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:26:19,425 - INFO - Processing PDF from S3...
2025-09-24 12:26:19,425 - INFO - Downloading PDF from S3 to /tmp/tmpyhlmucxx/5e1e2bce_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:26:19,985 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/a60b3129_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:26:19,986 - INFO - 🔍 [12:26:19] Starting classification: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:26:19,987 - INFO - ⬆️ [12:26:19] Uploading: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:26:19,989 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:20,019 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:20,028 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a60b3129_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:26:20,029 - INFO - Processing PDF from S3...
2025-09-24 12:26:20,029 - INFO - Downloading PDF from S3 to /tmp/tmpu1ot611r/a60b3129_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:26:20,041 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:26:20,042 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:20,043 - INFO - Splitting PDF 494cce76_NMFC_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 12:26:20,047 - INFO - Split PDF into 1 pages
2025-09-24 12:26:20,047 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:20,047 - INFO - Expected pages: [1]
2025-09-24 12:26:20,378 - INFO - Page 2: Extracted 764 characters, 54 lines from 40453711_NMFC_DH0JZ2JWDGRHD26BX74C_1f7b4da1_page_002.pdf
2025-09-24 12:26:20,379 - INFO - Successfully processed page 2
2025-09-24 12:26:20,583 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/55d05540_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:26:20,583 - INFO - 🔍 [12:26:20] Starting classification: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:26:20,584 - INFO - ⬆️ [12:26:20] Uploading: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:26:20,585 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:20,592 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:20,594 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/55d05540_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:26:20,594 - INFO - Processing PDF from S3...
2025-09-24 12:26:20,594 - INFO - Downloading PDF from S3 to /tmp/tmpbyhwirct/55d05540_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:26:20,603 - INFO - Page 1: Extracted 854 characters, 69 lines from 40453711_NMFC_DH0JZ2JWDGRHD26BX74C_1f7b4da1_page_001.pdf
2025-09-24 12:26:20,603 - INFO - Successfully processed page 1
2025-09-24 12:26:20,603 - INFO - Combined 2 pages into final text
2025-09-24 12:26:20,603 - INFO - Text validation for 40453711_NMFC_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 12:26:20,604 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:20,604 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:21,117 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/709bdf24_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:26:21,146 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 12:26:21,147 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:21,147 - INFO - Splitting PDF 3d2cf46a_W_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 12:26:21,149 - INFO - Split PDF into 1 pages
2025-09-24 12:26:21,149 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:21,149 - INFO - Expected pages: [1]
2025-09-24 12:26:21,170 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/31836804_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:26:21,170 - INFO - 🔍 [12:26:21] Starting classification: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:26:21,171 - INFO - ⬆️ [12:26:21] Uploading: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:26:21,173 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:21,192 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:21,195 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/31836804_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:26:21,195 - INFO - Processing PDF from S3...
2025-09-24 12:26:21,195 - INFO - Downloading PDF from S3 to /tmp/tmp3jxsxdn1/31836804_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:26:21,212 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:26:21,213 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:21,213 - INFO - Splitting PDF 5e1e2bce_W_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 12:26:21,214 - INFO - Split PDF into 1 pages
2025-09-24 12:26:21,214 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:21,215 - INFO - Expected pages: [1]
2025-09-24 12:26:21,586 - INFO - Page 3: Extracted 850 characters, 59 lines from eb89413e_NMFC_NJ4WSZ8BUQAW48V6403N_086c157a_page_003.pdf
2025-09-24 12:26:21,586 - INFO - Successfully processed page 3
2025-09-24 12:26:21,587 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:26:21,587 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:21,589 - INFO - Splitting PDF a60b3129_W_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 12:26:21,593 - INFO - Split PDF into 1 pages
2025-09-24 12:26:21,593 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:21,593 - INFO - Expected pages: [1]
2025-09-24 12:26:21,743 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/2afd952e_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:26:21,743 - INFO - 🔍 [12:26:21] Starting classification: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:26:21,744 - INFO - ⬆️ [12:26:21] Uploading: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:26:21,747 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:21,766 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:21,769 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2afd952e_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:26:21,769 - INFO - Processing PDF from S3...
2025-09-24 12:26:21,770 - INFO - Downloading PDF from S3 to /tmp/tmph6ztotab/2afd952e_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:26:22,032 - INFO - Page 1: Extracted 980 characters, 76 lines from eb89413e_NMFC_NJ4WSZ8BUQAW48V6403N_086c157a_page_001.pdf
2025-09-24 12:26:22,033 - INFO - Successfully processed page 1
2025-09-24 12:26:22,240 - INFO - Page 2: Extracted 850 characters, 59 lines from eb89413e_NMFC_NJ4WSZ8BUQAW48V6403N_086c157a_page_002.pdf
2025-09-24 12:26:22,241 - INFO - Successfully processed page 2
2025-09-24 12:26:22,242 - INFO - Combined 3 pages into final text
2025-09-24 12:26:22,243 - INFO - Text validation for eb89413e_NMFC_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 12:26:22,244 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:22,244 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:22,373 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/a8ce069d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:26:22,373 - INFO - 🔍 [12:26:22] Starting classification: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:26:22,374 - INFO - Initializing TextractProcessor...
2025-09-24 12:26:22,381 - INFO - Initializing BedrockProcessor...
2025-09-24 12:26:22,386 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a8ce069d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:26:22,387 - INFO - Processing PDF from S3...
2025-09-24 12:26:22,389 - INFO - Downloading PDF from S3 to /tmp/tmpym5cwfst/a8ce069d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:26:22,397 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:26:22,398 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:22,399 - INFO - 

I_QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "inspection_cert"
        }
    ]
}
2025-09-24 12:26:22,399 - INFO - 

✓ Saved result: output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 12:26:22,401 - INFO - Splitting PDF 55d05540_W_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 12:26:22,402 - INFO - Split PDF into 1 pages
2025-09-24 12:26:22,402 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:22,402 - INFO - Expected pages: [1]
2025-09-24 12:26:22,692 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/709bdf24_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:26:22,769 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:26:22,769 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:22,770 - INFO - Splitting PDF 31836804_W_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 12:26:22,771 - INFO - Split PDF into 1 pages
2025-09-24 12:26:22,772 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:22,772 - INFO - Expected pages: [1]
2025-09-24 12:26:22,808 - INFO - Page 2: Extracted 913 characters, 56 lines from 0719a867_NMFC_R1V0MO844PBLWNEAUETU_d8f2774d_page_002.pdf
2025-09-24 12:26:22,809 - INFO - Successfully processed page 2
2025-09-24 12:26:23,252 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:26:23,253 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:23,254 - INFO - Splitting PDF 2afd952e_W_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 12:26:23,255 - INFO - Split PDF into 1 pages
2025-09-24 12:26:23,256 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:23,256 - INFO - Expected pages: [1]
2025-09-24 12:26:23,663 - INFO - Page 1: Extracted 1511 characters, 86 lines from 0719a867_NMFC_R1V0MO844PBLWNEAUETU_d8f2774d_page_001.pdf
2025-09-24 12:26:23,664 - INFO - Successfully processed page 1
2025-09-24 12:26:23,664 - INFO - Combined 2 pages into final text
2025-09-24 12:26:23,664 - INFO - Text validation for 0719a867_NMFC_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 12:26:23,664 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:23,665 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:24,250 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:26:24,250 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:24,250 - INFO - Splitting PDF a8ce069d_W_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 12:26:24,254 - INFO - Split PDF into 1 pages
2025-09-24 12:26:24,254 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:24,254 - INFO - Expected pages: [1]
2025-09-24 12:26:24,939 - INFO - Page 1: Extracted 443 characters, 72 lines from 494cce76_NMFC_RUDVGETVRZO7XX6YNW7I_c6d45f9a_page_001.pdf
2025-09-24 12:26:24,939 - INFO - Successfully processed page 1
2025-09-24 12:26:24,939 - INFO - Combined 1 pages into final text
2025-09-24 12:26:24,940 - INFO - Text validation for 494cce76_NMFC_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 12:26:24,940 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:24,940 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:24,950 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 12:26:24,950 - INFO - Splitting PDF into individual pages...
2025-09-24 12:26:24,952 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 12:26:24,953 - INFO - Splitting PDF 2aff92c6_NMFC_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 12:26:24,985 - INFO - Split PDF into 2 pages
2025-09-24 12:26:24,985 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:26:24,985 - INFO - Expected pages: [1, 2]
2025-09-24 12:26:25,040 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/40453711_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:26:25,083 - INFO - 

NMFC_DH0JZ2JWDGRHD26BX74C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 2,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 12:26:25,083 - INFO - 

✓ Saved result: output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:26:25,369 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/40453711_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:26:25,466 - INFO - Page 1: Extracted 732 characters, 59 lines from 5e1e2bce_W_DCY7SLNMWUXIENOREHQF_848b44fc_page_001.pdf
2025-09-24 12:26:25,467 - INFO - Successfully processed page 1
2025-09-24 12:26:25,467 - INFO - Combined 1 pages into final text
2025-09-24 12:26:25,467 - INFO - Text validation for 5e1e2bce_W_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 12:26:25,468 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:25,468 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:25,767 - INFO - Page 1: Extracted 626 characters, 49 lines from a60b3129_W_DFY1VDZWR7NBDLJV02G2_1051bc90_page_001.pdf
2025-09-24 12:26:25,767 - INFO - Successfully processed page 1
2025-09-24 12:26:25,767 - INFO - Combined 1 pages into final text
2025-09-24 12:26:25,767 - INFO - Text validation for a60b3129_W_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 12:26:25,768 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:25,768 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:26,794 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/494cce76_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:26:26,816 - INFO - 

NMFC_RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 12:26:26,816 - INFO - 

✓ Saved result: output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 12:26:26,853 - INFO - Page 1: Extracted 802 characters, 30 lines from 31836804_W_K9VSARJOKAIZHNJ5RBDT_cbed7546_page_001.pdf
2025-09-24 12:26:26,853 - INFO - Successfully processed page 1
2025-09-24 12:26:26,853 - INFO - Combined 1 pages into final text
2025-09-24 12:26:26,854 - INFO - Text validation for 31836804_W_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 12:26:26,854 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:26,854 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:27,104 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/494cce76_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:26:27,510 - INFO - Page 1: Extracted 580 characters, 48 lines from 2afd952e_W_WRKSHW76B3QUG47QWR75_6cdd9830_page_001.pdf
2025-09-24 12:26:27,511 - INFO - Successfully processed page 1
2025-09-24 12:26:27,511 - INFO - Combined 1 pages into final text
2025-09-24 12:26:27,512 - INFO - Text validation for 2afd952e_W_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 12:26:27,512 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:27,513 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:27,539 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eb89413e_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:26:27,582 - INFO - 

NMFC_NJ4WSZ8BUQAW48V6403N.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 3,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 12:26:27,582 - INFO - 

✓ Saved result: output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:26:27,858 - INFO - Page 1: Extracted 939 characters, 64 lines from 3d2cf46a_W_A34CDFDJ66EDOZEKZWJL_478c93fd_page_001.pdf
2025-09-24 12:26:27,858 - INFO - Successfully processed page 1
2025-09-24 12:26:27,858 - INFO - Combined 1 pages into final text
2025-09-24 12:26:27,858 - INFO - Text validation for 3d2cf46a_W_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 12:26:27,858 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:27,858 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:27,866 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eb89413e_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:26:28,028 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a60b3129_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:26:28,055 - INFO - 

W_DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:26:28,055 - INFO - 

✓ Saved result: output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:26:28,209 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0719a867_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:26:28,345 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a60b3129_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:26:28,378 - INFO - 

NMFC_R1V0MO844PBLWNEAUETU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 12:26:28,378 - INFO - 

✓ Saved result: output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:26:28,663 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0719a867_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:26:29,505 - INFO - Page 1: Extracted 517 characters, 31 lines from 55d05540_W_HFPAXYL947DH59AB12FL_a2178ab9_page_001.pdf
2025-09-24 12:26:29,506 - INFO - Successfully processed page 1
2025-09-24 12:26:29,506 - INFO - Combined 1 pages into final text
2025-09-24 12:26:29,507 - INFO - Text validation for 55d05540_W_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 12:26:29,507 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:29,507 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:29,552 - INFO - Page 1: Extracted 528 characters, 31 lines from a8ce069d_W_XCJLXZK140FUS8020ZAG_56a07425_page_001.pdf
2025-09-24 12:26:29,552 - INFO - Successfully processed page 1
2025-09-24 12:26:29,553 - INFO - Combined 1 pages into final text
2025-09-24 12:26:29,553 - INFO - Text validation for a8ce069d_W_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 12:26:29,553 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:29,553 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:30,120 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5e1e2bce_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:26:30,144 - INFO - 

W_DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 12:26:30,144 - INFO - 

✓ Saved result: output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 12:26:30,168 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2afd952e_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:26:30,430 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5e1e2bce_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:26:30,440 - INFO - 

W_WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:26:30,440 - INFO - 

✓ Saved result: output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 12:26:30,526 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/31836804_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:26:30,717 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2afd952e_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:26:30,727 - INFO - 

W_K9VSARJOKAIZHNJ5RBDT.pdf

[
    {
        "page_no": 1,
        "doc_type": "scale_ticket"
    }
]
2025-09-24 12:26:30,727 - INFO - 

✓ Saved result: output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 12:26:31,018 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/31836804_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:26:31,238 - INFO - Page 1: Extracted 1120 characters, 87 lines from 2aff92c6_NMFC_OR9EL08KIKNQPZ3UV3HH_ee28cb48_page_001.pdf
2025-09-24 12:26:31,239 - INFO - Successfully processed page 1
2025-09-24 12:26:31,488 - INFO - Page 2: Extracted 540 characters, 29 lines from 2aff92c6_NMFC_OR9EL08KIKNQPZ3UV3HH_ee28cb48_page_002.pdf
2025-09-24 12:26:31,488 - INFO - Successfully processed page 2
2025-09-24 12:26:31,489 - INFO - Combined 2 pages into final text
2025-09-24 12:26:31,489 - INFO - Text validation for 2aff92c6_NMFC_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 12:26:31,490 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:26:31,490 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:26:31,621 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3d2cf46a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:26:31,643 - INFO - 

W_A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 12:26:31,643 - INFO - 

✓ Saved result: output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 12:26:31,931 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3d2cf46a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:26:33,073 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a8ce069d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:26:33,094 - INFO - 

W_XCJLXZK140FUS8020ZAG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:26:33,094 - INFO - 

✓ Saved result: output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 12:26:33,380 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a8ce069d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:26:34,825 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/55d05540_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:26:34,834 - INFO - 

W_HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 12:26:34,834 - INFO - 

✓ Saved result: output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 12:26:35,121 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/55d05540_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:26:35,137 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2aff92c6_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:26:35,181 - INFO - 

NMFC_OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:26:35,181 - INFO - 

✓ Saved result: output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:26:35,469 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2aff92c6_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:26:35,470 - INFO - 
📊 Processing Summary:
2025-09-24 12:26:35,470 - INFO -    Total files: 13
2025-09-24 12:26:35,471 - INFO -    Successful: 13
2025-09-24 12:26:35,471 - INFO -    Failed: 0
2025-09-24 12:26:35,471 - INFO -    Duration: 24.79 seconds
2025-09-24 12:26:35,471 - INFO -    Output directory: output
2025-09-24 12:26:35,471 - INFO - 
📋 Successfully Processed Files:
2025-09-24 12:26:35,471 - INFO -    📄 I_QHD3LC0DU6S8O2YVVS60.pdf: {"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}
2025-09-24 12:26:35,471 - INFO -    📄 NMFC_DH0JZ2JWDGRHD26BX74C.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"},{"page_no":2,"doc_type":"customs_doc"}]}
2025-09-24 12:26:35,471 - INFO -    📄 NMFC_NJ4WSZ8BUQAW48V6403N.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"},{"page_no":3,"doc_type":"nmfc_cert"}]}
2025-09-24 12:26:35,471 - INFO -    📄 NMFC_OR9EL08KIKNQPZ3UV3HH.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:26:35,471 - INFO -    📄 NMFC_R1V0MO844PBLWNEAUETU.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 12:26:35,471 - INFO -    📄 NMFC_RUDVGETVRZO7XX6YNW7I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 12:26:35,471 - INFO -    📄 W_A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 12:26:35,471 - INFO -    📄 W_DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 12:26:35,471 - INFO -    📄 W_DFY1VDZWR7NBDLJV02G2.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:26:35,471 - INFO -    📄 W_HFPAXYL947DH59AB12FL.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 12:26:35,472 - INFO -    📄 W_K9VSARJOKAIZHNJ5RBDT.pdf: [{"page_no":1,"doc_type":"scale_ticket"}]
2025-09-24 12:26:35,472 - INFO -    📄 W_WRKSHW76B3QUG47QWR75.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:26:35,472 - INFO -    📄 W_XCJLXZK140FUS8020ZAG.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
