2025-09-23 23:50:54,387 - INFO - Logging initialized. Log file: logs/test_classification_20250923_235054.log
2025-09-23 23:50:54,387 - INFO - 📁 Found 8 files to process
2025-09-23 23:50:54,388 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:50:54,388 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:50:54,388 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:50:54,388 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:50:54,388 - INFO - ⬆️ [23:50:54] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:50:55,696 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/8f0d8769_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:50:55,696 - INFO - 🔍 [23:50:55] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:50:55,696 - INFO - ⬆️ [23:50:55] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:50:55,697 - INFO - Initializing TextractProcessor...
2025-09-23 23:50:55,707 - INFO - Initializing BedrockProcessor...
2025-09-23 23:50:55,711 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8f0d8769_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:50:55,711 - INFO - Processing PDF from S3...
2025-09-23 23:50:55,712 - INFO - Downloading PDF from S3 to /tmp/tmp6jypitul/8f0d8769_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:50:57,016 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:50:57,016 - INFO - Splitting PDF into individual pages...
2025-09-23 23:50:57,017 - INFO - Splitting PDF 8f0d8769_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:50:57,018 - INFO - Split PDF into 1 pages
2025-09-23 23:50:57,018 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:50:57,019 - INFO - Expected pages: [1]
2025-09-23 23:50:58,270 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/ab272720_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:50:58,270 - INFO - 🔍 [23:50:58] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:50:58,270 - INFO - ⬆️ [23:50:58] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:50:58,273 - INFO - Initializing TextractProcessor...
2025-09-23 23:50:58,279 - INFO - Initializing BedrockProcessor...
2025-09-23 23:50:58,281 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ab272720_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:50:58,281 - INFO - Processing image from S3...
2025-09-23 23:50:58,884 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/f571b173_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:50:58,884 - INFO - 🔍 [23:50:58] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:50:58,885 - INFO - ⬆️ [23:50:58] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:50:58,885 - INFO - Initializing TextractProcessor...
2025-09-23 23:50:58,895 - INFO - Initializing BedrockProcessor...
2025-09-23 23:50:58,897 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f571b173_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:50:58,897 - INFO - Processing PDF from S3...
2025-09-23 23:50:58,897 - INFO - Downloading PDF from S3 to /tmp/tmptqtvt9_4/f571b173_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:50:59,507 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/d75c7330_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:50:59,507 - INFO - 🔍 [23:50:59] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:50:59,508 - INFO - Initializing TextractProcessor...
2025-09-23 23:50:59,509 - INFO - ⬆️ [23:50:59] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:50:59,514 - INFO - Initializing BedrockProcessor...
2025-09-23 23:50:59,518 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d75c7330_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:50:59,519 - INFO - Processing PDF from S3...
2025-09-23 23:50:59,519 - INFO - Downloading PDF from S3 to /tmp/tmpciovjpyc/d75c7330_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:00,673 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/9092c5ee_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:00,673 - INFO - 🔍 [23:51:00] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:00,674 - INFO - ⬆️ [23:51:00] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:00,676 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:00,692 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:00,696 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9092c5ee_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:00,696 - INFO - Processing image from S3...
2025-09-23 23:51:01,026 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:51:01,026 - INFO - Splitting PDF into individual pages...
2025-09-23 23:51:01,027 - INFO - Splitting PDF f571b173_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:51:01,030 - INFO - Split PDF into 1 pages
2025-09-23 23:51:01,030 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:51:01,031 - INFO - Expected pages: [1]
2025-09-23 23:51:01,281 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:51:01,282 - INFO - Splitting PDF into individual pages...
2025-09-23 23:51:01,285 - INFO - Splitting PDF d75c7330_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:51:01,296 - INFO - Split PDF into 7 pages
2025-09-23 23:51:01,297 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:51:01,297 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:51:01,347 - INFO - Page 1: Extracted 1260 characters, 84 lines from 8f0d8769_BQJUG5URFR2GH9ECWFV4_edf2154b_page_001.pdf
2025-09-23 23:51:01,347 - INFO - Successfully processed page 1
2025-09-23 23:51:01,347 - INFO - Combined 1 pages into final text
2025-09-23 23:51:01,347 - INFO - Text validation for 8f0d8769_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:51:01,347 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:01,348 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:01,855 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/fd6e995b_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:01,855 - INFO - 🔍 [23:51:01] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:01,857 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:01,857 - INFO - ⬆️ [23:51:01] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:01,871 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:01,879 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fd6e995b_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:01,879 - INFO - Processing image from S3...
2025-09-23 23:51:01,913 - INFO - S3 Image temp/ab272720_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:51:01,913 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:01,913 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:02,453 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/c11178f3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:02,453 - INFO - 🔍 [23:51:02] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:02,454 - INFO - ⬆️ [23:51:02] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:02,455 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:02,503 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:02,506 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c11178f3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:02,507 - INFO - Processing image from S3...
2025-09-23 23:51:03,136 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/83def14b_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:03,137 - INFO - 🔍 [23:51:03] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:03,138 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:03,147 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:03,150 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/83def14b_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:03,150 - INFO - Processing image from S3...
2025-09-23 23:51:03,293 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8f0d8769_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:03,328 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:03,328 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:51:03,620 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8f0d8769_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:04,210 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ab272720_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:51:04,222 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:04,222 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:51:04,412 - INFO - S3 Image temp/c11178f3_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:51:04,412 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:04,412 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:04,503 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ab272720_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:51:05,161 - INFO - S3 Image temp/83def14b_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:51:05,161 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:05,161 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:05,423 - INFO - Page 1: Extracted 1731 characters, 110 lines from d75c7330_O2IU5G77LYNTYE0RP1TI_0ed6071b_page_001.pdf
2025-09-23 23:51:05,423 - INFO - Successfully processed page 1
2025-09-23 23:51:05,663 - INFO - Page 1: Extracted 519 characters, 34 lines from f571b173_KE7TCH9TPQZFVA5CZ3HT_e3de2bdb_page_001.pdf
2025-09-23 23:51:05,664 - INFO - Successfully processed page 1
2025-09-23 23:51:05,664 - INFO - Combined 1 pages into final text
2025-09-23 23:51:05,664 - INFO - Text validation for f571b173_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:51:05,664 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:05,664 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:05,957 - INFO - S3 Image temp/fd6e995b_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:51:05,958 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:05,958 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:06,032 - INFO - Page 2: Extracted 1821 characters, 105 lines from d75c7330_O2IU5G77LYNTYE0RP1TI_0ed6071b_page_002.pdf
2025-09-23 23:51:06,041 - INFO - Page 5: Extracted 2059 characters, 131 lines from d75c7330_O2IU5G77LYNTYE0RP1TI_0ed6071b_page_005.pdf
2025-09-23 23:51:06,041 - INFO - Successfully processed page 2
2025-09-23 23:51:06,042 - INFO - Successfully processed page 5
2025-09-23 23:51:06,109 - INFO - Page 3: Extracted 2265 characters, 147 lines from d75c7330_O2IU5G77LYNTYE0RP1TI_0ed6071b_page_003.pdf
2025-09-23 23:51:06,110 - INFO - Successfully processed page 3
2025-09-23 23:51:06,216 - INFO - Page 4: Extracted 2242 characters, 148 lines from d75c7330_O2IU5G77LYNTYE0RP1TI_0ed6071b_page_004.pdf
2025-09-23 23:51:06,216 - INFO - Successfully processed page 4
2025-09-23 23:51:06,243 - INFO - Page 6: Extracted 1973 characters, 129 lines from d75c7330_O2IU5G77LYNTYE0RP1TI_0ed6071b_page_006.pdf
2025-09-23 23:51:06,243 - INFO - Successfully processed page 6
2025-09-23 23:51:06,295 - INFO - S3 Image temp/9092c5ee_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:51:06,295 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:06,295 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:06,872 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c11178f3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:06,878 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:06,878 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:51:07,151 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c11178f3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:07,384 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/83def14b_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:07,391 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:51:07,391 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:51:07,669 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/83def14b_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:08,105 - INFO - Page 7: Extracted 417 characters, 27 lines from d75c7330_O2IU5G77LYNTYE0RP1TI_0ed6071b_page_007.pdf
2025-09-23 23:51:08,105 - INFO - Successfully processed page 7
2025-09-23 23:51:08,106 - INFO - Combined 7 pages into final text
2025-09-23 23:51:08,107 - INFO - Text validation for d75c7330_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:51:08,107 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:08,107 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:08,243 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9092c5ee_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:08,293 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:08,294 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:51:08,449 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fd6e995b_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:08,572 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9092c5ee_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:08,591 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:08,591 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:51:08,868 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fd6e995b_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:09,146 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f571b173_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:09,155 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-23 23:51:09,155 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:51:09,443 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f571b173_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:11,698 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d75c7330_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:11,816 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:11,816 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,092 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d75c7330_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:12,094 - INFO - 
📊 Processing Summary:
2025-09-23 23:51:12,094 - INFO -    Total files: 8
2025-09-23 23:51:12,094 - INFO -    Successful: 8
2025-09-23 23:51:12,094 - INFO -    Failed: 0
2025-09-23 23:51:12,094 - INFO -    Duration: 17.71 seconds
2025-09-23 23:51:12,094 - INFO -    Output directory: output
2025-09-23 23:51:12,094 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:51:12,094 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:12,094 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:12,094 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-23 23:51:12,094 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:51:12,094 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:12,094 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:12,094 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:12,094 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:51:12,094 - INFO - 
============================================================================================================================================
2025-09-23 23:51:12,094 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:51:12,095 - INFO - ============================================================================================================================================
2025-09-23 23:51:12,095 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:51:12,095 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:51:12,095 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      coa                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:51:12,095 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:12,095 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:51:12,095 - INFO - 
2025-09-23 23:51:12,095 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:51:12,096 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:12,096 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:51:12,096 - INFO - 
2025-09-23 23:51:12,096 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:51:12,096 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:12,096 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:51:12,096 - INFO - 
2025-09-23 23:51:12,096 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:51:12,096 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:12,096 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:51:12,096 - INFO - 
2025-09-23 23:51:12,096 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:51:12,096 - INFO - Total entries: 14
2025-09-23 23:51:12,096 - INFO - ============================================================================================================================================
2025-09-23 23:51:12,096 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:51:12,096 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:51:12,096 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:51:12,096 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:51:12,096 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → coa             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:51:12,096 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,096 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,096 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,096 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,096 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,096 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,096 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:12,096 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:51:12,096 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:51:12,096 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:51:12,096 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:51:12,096 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:51:12,096 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 17.706214, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
