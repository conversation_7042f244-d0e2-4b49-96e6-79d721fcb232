2025-09-24 12:49:23,330 - INFO - Logging initialized. Log file: logs/test_classification_20250924_124923.log
2025-09-24 12:49:23,331 - INFO - 📁 Found 13 files to process
2025-09-24 12:49:23,331 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 12:49:23,331 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 12:49:23,331 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 12:49:23,331 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 12:49:23,332 - INFO - ⬆️ [12:49:23] Uploading: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:49:25,425 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/599cebce_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:49:25,426 - INFO - 🔍 [12:49:25] Starting classification: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:49:25,428 - INFO - ⬆️ [12:49:25] Uploading: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:49:25,433 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:25,483 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:25,490 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/599cebce_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:49:25,490 - INFO - Processing PDF from S3...
2025-09-24 12:49:25,491 - INFO - Downloading PDF from S3 to /tmp/tmph7lpuwe7/599cebce_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:49:26,472 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/b3282af6_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:49:26,473 - INFO - 🔍 [12:49:26] Starting classification: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:49:26,475 - INFO - ⬆️ [12:49:26] Uploading: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:49:26,479 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:26,556 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:26,561 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b3282af6_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:49:26,561 - INFO - Processing PDF from S3...
2025-09-24 12:49:26,561 - INFO - Downloading PDF from S3 to /tmp/tmp0072f367/b3282af6_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:49:27,175 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:49:27,175 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:27,180 - INFO - Splitting PDF 599cebce_I_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 12:49:27,185 - INFO - Split PDF into 1 pages
2025-09-24 12:49:27,186 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:27,186 - INFO - Expected pages: [1]
2025-09-24 12:49:27,885 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/9bb3f883_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:49:27,886 - INFO - 🔍 [12:49:27] Starting classification: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:49:27,889 - INFO - ⬆️ [12:49:27] Uploading: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:49:27,894 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:27,995 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:28,007 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9bb3f883_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:49:28,007 - INFO - Processing PDF from S3...
2025-09-24 12:49:28,007 - INFO - Downloading PDF from S3 to /tmp/tmpfp7gy00p/9bb3f883_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:49:28,414 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:49:28,414 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:28,415 - INFO - Splitting PDF b3282af6_NMFC_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 12:49:28,419 - INFO - Split PDF into 2 pages
2025-09-24 12:49:28,419 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:28,419 - INFO - Expected pages: [1, 2]
2025-09-24 12:49:29,329 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/9aec3c0c_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:49:29,330 - INFO - 🔍 [12:49:29] Starting classification: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:49:29,332 - INFO - ⬆️ [12:49:29] Uploading: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:49:29,337 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:29,382 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:29,387 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9aec3c0c_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:49:29,388 - INFO - Processing PDF from S3...
2025-09-24 12:49:29,388 - INFO - Downloading PDF from S3 to /tmp/tmpbhjmy_4x/9aec3c0c_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:49:30,004 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/1a736026_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:49:30,005 - INFO - 🔍 [12:49:30] Starting classification: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:49:30,008 - INFO - ⬆️ [12:49:30] Uploading: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:49:30,009 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:30,044 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:30,053 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1a736026_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:49:30,053 - INFO - Processing PDF from S3...
2025-09-24 12:49:30,053 - INFO - Downloading PDF from S3 to /tmp/tmpbhf2_z9w/1a736026_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:49:30,066 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:49:30,066 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:30,068 - INFO - Splitting PDF 9bb3f883_NMFC_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 12:49:30,073 - INFO - Split PDF into 3 pages
2025-09-24 12:49:30,073 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:30,073 - INFO - Expected pages: [1, 2, 3]
2025-09-24 12:49:30,746 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/3feb5760_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:49:30,746 - INFO - 🔍 [12:49:30] Starting classification: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:49:30,749 - INFO - ⬆️ [12:49:30] Uploading: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:49:30,750 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:30,781 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:30,793 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3feb5760_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:49:30,793 - INFO - Processing PDF from S3...
2025-09-24 12:49:30,805 - INFO - Downloading PDF from S3 to /tmp/tmp9zuryq26/3feb5760_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:49:31,526 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:49:31,527 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:31,533 - INFO - Splitting PDF 1a736026_NMFC_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 12:49:31,549 - INFO - Split PDF into 2 pages
2025-09-24 12:49:31,549 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:31,549 - INFO - Expected pages: [1, 2]
2025-09-24 12:49:31,618 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/90fc5a3a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:49:31,619 - INFO - 🔍 [12:49:31] Starting classification: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:49:31,621 - INFO - ⬆️ [12:49:31] Uploading: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:49:31,623 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:31,648 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:31,659 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/90fc5a3a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:49:31,663 - INFO - Processing PDF from S3...
2025-09-24 12:49:31,663 - INFO - Downloading PDF from S3 to /tmp/tmpiaaqestf/90fc5a3a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:49:32,014 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 12:49:32,014 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:32,018 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 12:49:32,019 - INFO - Splitting PDF 9aec3c0c_NMFC_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 12:49:32,080 - INFO - Page 1: Extracted 589 characters, 36 lines from 599cebce_I_QHD3LC0DU6S8O2YVVS60_afdcf1d0_page_001.pdf
2025-09-24 12:49:32,081 - INFO - Successfully processed page 1
2025-09-24 12:49:32,083 - INFO - Combined 1 pages into final text
2025-09-24 12:49:32,083 - INFO - Text validation for 599cebce_I_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 12:49:32,103 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:32,104 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:32,112 - INFO - Split PDF into 2 pages
2025-09-24 12:49:32,112 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:32,112 - INFO - Expected pages: [1, 2]
2025-09-24 12:49:32,412 - INFO - Page 2: Extracted 764 characters, 54 lines from b3282af6_NMFC_DH0JZ2JWDGRHD26BX74C_7a4fd5fe_page_002.pdf
2025-09-24 12:49:32,413 - INFO - Successfully processed page 2
2025-09-24 12:49:32,414 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/79f553f7_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:49:32,414 - INFO - 🔍 [12:49:32] Starting classification: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:49:32,415 - INFO - ⬆️ [12:49:32] Uploading: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:49:32,416 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:32,436 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:32,452 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/79f553f7_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:49:32,453 - INFO - Processing PDF from S3...
2025-09-24 12:49:32,453 - INFO - Downloading PDF from S3 to /tmp/tmpmbikeonx/79f553f7_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:49:32,853 - INFO - Page 1: Extracted 854 characters, 69 lines from b3282af6_NMFC_DH0JZ2JWDGRHD26BX74C_7a4fd5fe_page_001.pdf
2025-09-24 12:49:32,855 - INFO - Successfully processed page 1
2025-09-24 12:49:32,858 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:49:32,859 - INFO - Combined 2 pages into final text
2025-09-24 12:49:32,859 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:32,860 - INFO - Text validation for b3282af6_NMFC_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 12:49:32,865 - INFO - Splitting PDF 3feb5760_NMFC_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 12:49:32,865 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:32,869 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:32,887 - INFO - Split PDF into 1 pages
2025-09-24 12:49:32,887 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:32,887 - INFO - Expected pages: [1]
2025-09-24 12:49:33,144 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/55d50227_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:49:33,146 - INFO - 🔍 [12:49:33] Starting classification: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:49:33,148 - INFO - ⬆️ [12:49:33] Uploading: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:49:33,150 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:33,199 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:33,212 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/55d50227_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:49:33,212 - INFO - Processing PDF from S3...
2025-09-24 12:49:33,212 - INFO - Downloading PDF from S3 to /tmp/tmpw6y5a4w9/55d50227_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:49:33,881 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/9ca69825_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:49:33,881 - INFO - 🔍 [12:49:33] Starting classification: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:49:33,883 - INFO - ⬆️ [12:49:33] Uploading: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:49:33,885 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:33,935 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:33,941 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9ca69825_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:49:33,941 - INFO - Processing PDF from S3...
2025-09-24 12:49:33,941 - INFO - Downloading PDF from S3 to /tmp/tmp8cba8t3g/9ca69825_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:49:34,577 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/3ac4cb1b_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:49:34,578 - INFO - 🔍 [12:49:34] Starting classification: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:49:34,580 - INFO - ⬆️ [12:49:34] Uploading: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:49:34,580 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:34,622 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:34,634 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3ac4cb1b_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:49:34,635 - INFO - Processing PDF from S3...
2025-09-24 12:49:34,635 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:49:34,635 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:34,636 - INFO - Downloading PDF from S3 to /tmp/tmpmz6u2zfn/3ac4cb1b_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:49:34,653 - INFO - Splitting PDF 79f553f7_W_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 12:49:34,661 - INFO - Split PDF into 1 pages
2025-09-24 12:49:34,661 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:34,661 - INFO - Expected pages: [1]
2025-09-24 12:49:34,967 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:49:34,968 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:34,973 - INFO - Splitting PDF 55d50227_W_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 12:49:34,985 - INFO - Split PDF into 1 pages
2025-09-24 12:49:34,985 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:34,985 - INFO - Expected pages: [1]
2025-09-24 12:49:35,128 - INFO - Page 2: Extracted 850 characters, 59 lines from 9bb3f883_NMFC_NJ4WSZ8BUQAW48V6403N_de880927_page_002.pdf
2025-09-24 12:49:35,128 - INFO - Successfully processed page 2
2025-09-24 12:49:35,169 - INFO - Page 3: Extracted 850 characters, 59 lines from 9bb3f883_NMFC_NJ4WSZ8BUQAW48V6403N_de880927_page_003.pdf
2025-09-24 12:49:35,169 - INFO - Successfully processed page 3
2025-09-24 12:49:35,186 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 12:49:35,186 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:35,188 - INFO - Splitting PDF 90fc5a3a_W_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 12:49:35,191 - INFO - Split PDF into 1 pages
2025-09-24 12:49:35,191 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:35,191 - INFO - Expected pages: [1]
2025-09-24 12:49:35,260 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/4a2725a2_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:49:35,261 - INFO - 🔍 [12:49:35] Starting classification: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:49:35,263 - INFO - ⬆️ [12:49:35] Uploading: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:49:35,263 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:35,303 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:35,310 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4a2725a2_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:49:35,310 - INFO - Processing PDF from S3...
2025-09-24 12:49:35,310 - INFO - Downloading PDF from S3 to /tmp/tmpve4a9a8y/4a2725a2_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:49:35,434 - INFO - Page 2: Extracted 913 characters, 56 lines from 1a736026_NMFC_R1V0MO844PBLWNEAUETU_9636c4ac_page_002.pdf
2025-09-24 12:49:35,435 - INFO - Successfully processed page 2
2025-09-24 12:49:35,742 - INFO - Page 1: Extracted 980 characters, 76 lines from 9bb3f883_NMFC_NJ4WSZ8BUQAW48V6403N_de880927_page_001.pdf
2025-09-24 12:49:35,744 - INFO - Successfully processed page 1
2025-09-24 12:49:35,751 - INFO - Combined 3 pages into final text
2025-09-24 12:49:35,752 - INFO - Text validation for 9bb3f883_NMFC_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 12:49:35,752 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:35,753 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:35,955 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/aabb68b2_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:49:35,955 - INFO - 🔍 [12:49:35] Starting classification: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:49:35,958 - INFO - Initializing TextractProcessor...
2025-09-24 12:49:35,980 - INFO - Initializing BedrockProcessor...
2025-09-24 12:49:35,984 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/aabb68b2_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:49:35,984 - INFO - Processing PDF from S3...
2025-09-24 12:49:35,985 - INFO - Downloading PDF from S3 to /tmp/tmpnpqc9lc9/aabb68b2_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:49:36,338 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:49:36,338 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:36,341 - INFO - Splitting PDF 9ca69825_W_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 12:49:36,345 - INFO - Split PDF into 1 pages
2025-09-24 12:49:36,345 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:36,346 - INFO - Expected pages: [1]
2025-09-24 12:49:36,382 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:49:36,383 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:36,388 - INFO - Splitting PDF 3ac4cb1b_W_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 12:49:36,391 - INFO - Split PDF into 1 pages
2025-09-24 12:49:36,391 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:36,391 - INFO - Expected pages: [1]
2025-09-24 12:49:36,439 - INFO - Page 1: Extracted 1511 characters, 86 lines from 1a736026_NMFC_R1V0MO844PBLWNEAUETU_9636c4ac_page_001.pdf
2025-09-24 12:49:36,439 - INFO - Successfully processed page 1
2025-09-24 12:49:36,439 - INFO - Combined 2 pages into final text
2025-09-24 12:49:36,440 - INFO - Text validation for 1a736026_NMFC_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 12:49:36,440 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:36,440 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:36,447 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/599cebce_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:49:36,637 - INFO - 

I_QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "inspection_cert"
        }
    ]
}
2025-09-24 12:49:36,637 - INFO - 

✓ Saved result: output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 12:49:36,704 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:49:36,704 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:36,706 - INFO - Splitting PDF 4a2725a2_W_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 12:49:36,708 - INFO - Split PDF into 1 pages
2025-09-24 12:49:36,709 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:36,709 - INFO - Expected pages: [1]
2025-09-24 12:49:36,983 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/599cebce_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:49:37,678 - INFO - Page 1: Extracted 443 characters, 72 lines from 3feb5760_NMFC_RUDVGETVRZO7XX6YNW7I_bcd8314f_page_001.pdf
2025-09-24 12:49:37,678 - INFO - Successfully processed page 1
2025-09-24 12:49:37,679 - INFO - Combined 1 pages into final text
2025-09-24 12:49:37,679 - INFO - Text validation for 3feb5760_NMFC_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 12:49:37,679 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:37,679 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:38,051 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-24 12:49:38,053 - ERROR - Processing failed for s3://document-extraction-logistically/temp/b3282af6_NMFC_DH0JZ2JWDGRHD26BX74C.pdf: Unexpected tool response format from Bedrock
2025-09-24 12:49:38,053 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/b3282af6_NMFC_DH0JZ2JWDGRHD26BX74C.pdf: Unexpected tool response format from Bedrock
2025-09-24 12:49:38,058 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 227, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 142, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 12:49:38,060 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf: Unexpected tool response format from Bedrock
2025-09-24 12:49:38,061 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 269, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 227, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 142, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 12:49:38,071 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:49:38,071 - INFO - Splitting PDF into individual pages...
2025-09-24 12:49:38,074 - INFO - Splitting PDF aabb68b2_W_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 12:49:38,085 - INFO - Split PDF into 1 pages
2025-09-24 12:49:38,085 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:49:38,085 - INFO - Expected pages: [1]
2025-09-24 12:49:38,204 - INFO - Page 1: Extracted 1120 characters, 87 lines from 9aec3c0c_NMFC_OR9EL08KIKNQPZ3UV3HH_3baee0aa_page_001.pdf
2025-09-24 12:49:38,204 - INFO - Successfully processed page 1
2025-09-24 12:49:38,211 - INFO - Page 2: Extracted 540 characters, 29 lines from 9aec3c0c_NMFC_OR9EL08KIKNQPZ3UV3HH_3baee0aa_page_002.pdf
2025-09-24 12:49:38,212 - INFO - Successfully processed page 2
2025-09-24 12:49:38,212 - INFO - Combined 2 pages into final text
2025-09-24 12:49:38,212 - INFO - Text validation for 9aec3c0c_NMFC_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 12:49:38,213 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:38,213 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:38,402 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b3282af6_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:49:39,081 - INFO - Page 1: Extracted 626 characters, 49 lines from 55d50227_W_DFY1VDZWR7NBDLJV02G2_686f4f13_page_001.pdf
2025-09-24 12:49:39,081 - INFO - Successfully processed page 1
2025-09-24 12:49:39,082 - INFO - Combined 1 pages into final text
2025-09-24 12:49:39,082 - INFO - Text validation for 55d50227_W_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 12:49:39,082 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:39,082 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:39,162 - INFO - Page 1: Extracted 732 characters, 59 lines from 79f553f7_W_DCY7SLNMWUXIENOREHQF_d5487b52_page_001.pdf
2025-09-24 12:49:39,162 - INFO - Successfully processed page 1
2025-09-24 12:49:39,163 - INFO - Combined 1 pages into final text
2025-09-24 12:49:39,163 - INFO - Text validation for 79f553f7_W_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 12:49:39,163 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:39,163 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:39,222 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9bb3f883_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:49:39,600 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1a736026_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:49:39,813 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3feb5760_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:49:40,089 - INFO - 

NMFC_NJ4WSZ8BUQAW48V6403N.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 3,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:49:40,089 - INFO - 

✓ Saved result: output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:49:40,422 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9bb3f883_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:49:40,490 - INFO - Page 1: Extracted 802 characters, 30 lines from 3ac4cb1b_W_K9VSARJOKAIZHNJ5RBDT_a637e63b_page_001.pdf
2025-09-24 12:49:40,515 - INFO - Page 1: Extracted 580 characters, 48 lines from 4a2725a2_W_WRKSHW76B3QUG47QWR75_f55ce14e_page_001.pdf
2025-09-24 12:49:40,515 - INFO - Successfully processed page 1
2025-09-24 12:49:40,515 - INFO - Successfully processed page 1
2025-09-24 12:49:40,525 - INFO - Combined 1 pages into final text
2025-09-24 12:49:40,538 - INFO - Text validation for 3ac4cb1b_W_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 12:49:40,538 - INFO - Combined 1 pages into final text
2025-09-24 12:49:40,574 - INFO - Text validation for 4a2725a2_W_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 12:49:40,579 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:40,587 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:40,632 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:40,646 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:40,907 - INFO - Page 1: Extracted 517 characters, 31 lines from 9ca69825_W_HFPAXYL947DH59AB12FL_f5bc666b_page_001.pdf
2025-09-24 12:49:40,912 - INFO - Successfully processed page 1
2025-09-24 12:49:40,916 - INFO - Combined 1 pages into final text
2025-09-24 12:49:40,922 - INFO - Text validation for 9ca69825_W_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 12:49:40,940 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:40,946 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:40,995 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/55d50227_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:49:41,287 - INFO - 

NMFC_R1V0MO844PBLWNEAUETU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:49:41,287 - INFO - 

✓ Saved result: output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:49:41,595 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9aec3c0c_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:49:41,615 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1a736026_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:49:41,863 - INFO - 

NMFC_RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 12:49:41,863 - INFO - 

✓ Saved result: output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 12:49:42,008 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/79f553f7_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:49:42,159 - INFO - Page 1: Extracted 939 characters, 64 lines from 90fc5a3a_W_A34CDFDJ66EDOZEKZWJL_8359f04d_page_001.pdf
2025-09-24 12:49:42,159 - INFO - Successfully processed page 1
2025-09-24 12:49:42,160 - INFO - Combined 1 pages into final text
2025-09-24 12:49:42,160 - INFO - Text validation for 90fc5a3a_W_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 12:49:42,161 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:42,161 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:42,200 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3feb5760_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:49:42,431 - INFO - 

W_DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:49:42,431 - INFO - 

✓ Saved result: output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:49:42,772 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/55d50227_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:49:42,886 - INFO - Page 1: Extracted 528 characters, 31 lines from aabb68b2_W_XCJLXZK140FUS8020ZAG_e3a4884c_page_001.pdf
2025-09-24 12:49:42,894 - INFO - Successfully processed page 1
2025-09-24 12:49:42,910 - INFO - Combined 1 pages into final text
2025-09-24 12:49:42,915 - INFO - Text validation for aabb68b2_W_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 12:49:42,939 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:49:42,940 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:49:43,160 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9ca69825_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:49:43,359 - INFO - 

NMFC_OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:49:43,359 - INFO - 

✓ Saved result: output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:49:43,674 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4a2725a2_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:49:43,693 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9aec3c0c_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:49:43,742 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3ac4cb1b_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:49:43,966 - INFO - 

W_DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:49:43,966 - INFO - 

✓ Saved result: output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 12:49:44,073 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/90fc5a3a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:49:44,381 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/79f553f7_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:49:44,560 - INFO - 

W_HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:49:44,560 - INFO - 

✓ Saved result: output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 12:49:44,899 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9ca69825_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:49:45,125 - INFO - 

W_WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:49:45,125 - INFO - 

✓ Saved result: output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 12:49:45,414 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/aabb68b2_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:49:45,523 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4a2725a2_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:49:45,741 - INFO - 

W_K9VSARJOKAIZHNJ5RBDT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:49:45,741 - INFO - 

✓ Saved result: output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 12:49:46,136 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3ac4cb1b_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:49:46,451 - INFO - 

W_A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:49:46,451 - INFO - 

✓ Saved result: output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 12:49:46,786 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/90fc5a3a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:49:46,963 - INFO - 

W_XCJLXZK140FUS8020ZAG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:49:46,963 - INFO - 

✓ Saved result: output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 12:49:47,308 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/aabb68b2_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:49:47,309 - INFO - 
📊 Processing Summary:
2025-09-24 12:49:47,309 - INFO -    Total files: 13
2025-09-24 12:49:47,309 - INFO -    Successful: 12
2025-09-24 12:49:47,309 - INFO -    Failed: 1
2025-09-24 12:49:47,309 - INFO -    Duration: 23.98 seconds
2025-09-24 12:49:47,309 - INFO -    Output directory: output
2025-09-24 12:49:47,309 - INFO - 
📋 Successfully Processed Files:
2025-09-24 12:49:47,309 - INFO -    📄 I_QHD3LC0DU6S8O2YVVS60.pdf: {"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}
2025-09-24 12:49:47,310 - INFO -    📄 NMFC_NJ4WSZ8BUQAW48V6403N.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"},{"page_no":3,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:49:47,310 - INFO -    📄 NMFC_OR9EL08KIKNQPZ3UV3HH.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:49:47,310 - INFO -    📄 NMFC_R1V0MO844PBLWNEAUETU.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:49:47,310 - INFO -    📄 NMFC_RUDVGETVRZO7XX6YNW7I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 12:49:47,310 - INFO -    📄 W_A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:49:47,310 - INFO -    📄 W_DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:49:47,310 - INFO -    📄 W_DFY1VDZWR7NBDLJV02G2.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:49:47,310 - INFO -    📄 W_HFPAXYL947DH59AB12FL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:49:47,310 - INFO -    📄 W_K9VSARJOKAIZHNJ5RBDT.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:49:47,311 - INFO -    📄 W_WRKSHW76B3QUG47QWR75.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:49:47,311 - INFO -    📄 W_XCJLXZK140FUS8020ZAG.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:49:47,311 - ERROR - 
❌ Errors:
2025-09-24 12:49:47,311 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf: Unexpected tool response format from Bedrock
2025-09-24 12:49:47,312 - INFO - 
============================================================================================================================================
2025-09-24 12:49:47,312 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 12:49:47,312 - INFO - ============================================================================================================================================
2025-09-24 12:49:47,312 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 12:49:47,312 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 12:49:47,313 - INFO - I_QHD3LC0DU6S8O2YVVS60.pdf                         1      inspection_cert      run1_I_QHD3LC0DU6S8O2YVVS60.json                  
2025-09-24 12:49:47,313 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:49:47,313 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 12:49:47,313 - INFO - 
2025-09-24 12:49:47,313 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      1      weight_and_inspect... run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 12:49:47,313 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:49:47,313 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:49:47,313 - INFO - 
2025-09-24 12:49:47,313 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      2      weight_and_inspect... run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 12:49:47,313 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:49:47,313 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:49:47,313 - INFO - 
2025-09-24 12:49:47,314 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      3      weight_and_inspect... run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 12:49:47,314 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:49:47,314 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:49:47,314 - INFO - 
2025-09-24 12:49:47,314 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      1      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 12:49:47,314 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:49:47,314 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:49:47,314 - INFO - 
2025-09-24 12:49:47,314 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      2      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 12:49:47,314 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:49:47,314 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:49:47,314 - INFO - 
2025-09-24 12:49:47,314 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      1      weight_and_inspect... run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 12:49:47,314 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:49:47,314 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:49:47,314 - INFO - 
2025-09-24 12:49:47,314 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      2      weight_and_inspect... run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 12:49:47,314 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:49:47,314 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:49:47,314 - INFO - 
2025-09-24 12:49:47,314 - INFO - NMFC_RUDVGETVRZO7XX6YNW7I.pdf                      1      nmfc_cert            run1_NMFC_RUDVGETVRZO7XX6YNW7I.json               
2025-09-24 12:49:47,314 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:49:47,315 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 12:49:47,315 - INFO - 
2025-09-24 12:49:47,315 - INFO - W_A34CDFDJ66EDOZEKZWJL.pdf                         1      weight_and_inspect... run1_W_A34CDFDJ66EDOZEKZWJL.json                  
2025-09-24 12:49:47,315 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:49:47,315 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 12:49:47,315 - INFO - 
2025-09-24 12:49:47,315 - INFO - W_DCY7SLNMWUXIENOREHQF.pdf                         1      weight_and_inspect... run1_W_DCY7SLNMWUXIENOREHQF.json                  
2025-09-24 12:49:47,315 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:49:47,315 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 12:49:47,315 - INFO - 
2025-09-24 12:49:47,315 - INFO - W_DFY1VDZWR7NBDLJV02G2.pdf                         1      weight_and_inspect... run1_W_DFY1VDZWR7NBDLJV02G2.json                  
2025-09-24 12:49:47,315 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:49:47,315 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:49:47,315 - INFO - 
2025-09-24 12:49:47,315 - INFO - W_HFPAXYL947DH59AB12FL.pdf                         1      weight_and_inspect... run1_W_HFPAXYL947DH59AB12FL.json                  
2025-09-24 12:49:47,315 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:49:47,315 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 12:49:47,315 - INFO - 
2025-09-24 12:49:47,315 - INFO - W_K9VSARJOKAIZHNJ5RBDT.pdf                         1      weight_and_inspect... run1_W_K9VSARJOKAIZHNJ5RBDT.json                  
2025-09-24 12:49:47,315 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:49:47,315 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 12:49:47,315 - INFO - 
2025-09-24 12:49:47,315 - INFO - W_WRKSHW76B3QUG47QWR75.pdf                         1      weight_and_inspect... run1_W_WRKSHW76B3QUG47QWR75.json                  
2025-09-24 12:49:47,316 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:49:47,316 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 12:49:47,316 - INFO - 
2025-09-24 12:49:47,316 - INFO - W_XCJLXZK140FUS8020ZAG.pdf                         1      weight_and_inspect... run1_W_XCJLXZK140FUS8020ZAG.json                  
2025-09-24 12:49:47,316 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:49:47,316 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 12:49:47,316 - INFO - 
2025-09-24 12:49:47,316 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 12:49:47,316 - INFO - Total entries: 16
2025-09-24 12:49:47,316 - INFO - ============================================================================================================================================
2025-09-24 12:49:47,316 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 12:49:47,316 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 12:49:47,316 - INFO -   1. I_QHD3LC0DU6S8O2YVVS60.pdf          Page 1   → inspection_cert | run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 12:49:47,316 - INFO -   2. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:49:47,316 - INFO -   3. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:49:47,316 - INFO -   4. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 3   → weight_and_inspection_cert | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:49:47,316 - INFO -   5. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:49:47,316 - INFO -   6. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:49:47,316 - INFO -   7. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:49:47,316 - INFO -   8. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:49:47,317 - INFO -   9. NMFC_RUDVGETVRZO7XX6YNW7I.pdf       Page 1   → nmfc_cert       | run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 12:49:47,317 - INFO -  10. W_A34CDFDJ66EDOZEKZWJL.pdf          Page 1   → weight_and_inspection_cert | run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 12:49:47,317 - INFO -  11. W_DCY7SLNMWUXIENOREHQF.pdf          Page 1   → weight_and_inspection_cert | run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 12:49:47,317 - INFO -  12. W_DFY1VDZWR7NBDLJV02G2.pdf          Page 1   → weight_and_inspection_cert | run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:49:47,317 - INFO -  13. W_HFPAXYL947DH59AB12FL.pdf          Page 1   → weight_and_inspection_cert | run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 12:49:47,317 - INFO -  14. W_K9VSARJOKAIZHNJ5RBDT.pdf          Page 1   → weight_and_inspection_cert | run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 12:49:47,317 - INFO -  15. W_WRKSHW76B3QUG47QWR75.pdf          Page 1   → weight_and_inspection_cert | run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 12:49:47,317 - INFO -  16. W_XCJLXZK140FUS8020ZAG.pdf          Page 1   → weight_and_inspection_cert | run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 12:49:47,317 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 12:49:47,317 - INFO - 
✅ Test completed: {'total_files': 13, 'processed': 12, 'failed': 1, 'errors': ['Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 23.977278, 'processed_files': [{'filename': 'I_QHD3LC0DU6S8O2YVVS60.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf'}, {'filename': 'NMFC_NJ4WSZ8BUQAW48V6403N.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 3, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf'}, {'filename': 'NMFC_OR9EL08KIKNQPZ3UV3HH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf'}, {'filename': 'NMFC_R1V0MO844PBLWNEAUETU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf'}, {'filename': 'NMFC_RUDVGETVRZO7XX6YNW7I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf'}, {'filename': 'W_A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf'}, {'filename': 'W_DCY7SLNMWUXIENOREHQF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf'}, {'filename': 'W_DFY1VDZWR7NBDLJV02G2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf'}, {'filename': 'W_HFPAXYL947DH59AB12FL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf'}, {'filename': 'W_K9VSARJOKAIZHNJ5RBDT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf'}, {'filename': 'W_WRKSHW76B3QUG47QWR75.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf'}, {'filename': 'W_XCJLXZK140FUS8020ZAG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf'}]}
