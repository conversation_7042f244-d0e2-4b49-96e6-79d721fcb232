2025-09-23 23:54:30,982 - INFO - Logging initialized. Log file: logs/test_classification_20250923_235430.log
2025-09-23 23:54:30,982 - INFO - 📁 Found 8 files to process
2025-09-23 23:54:30,982 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:54:30,982 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:54:30,982 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:54:30,982 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:54:30,982 - INFO - ⬆️ [23:54:30] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:32,454 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/e9430c18_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:32,455 - INFO - 🔍 [23:54:32] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:32,455 - INFO - ⬆️ [23:54:32] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:32,456 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:32,470 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:32,477 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e9430c18_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:32,478 - INFO - Processing PDF from S3...
2025-09-23 23:54:32,478 - INFO - Downloading PDF from S3 to /tmp/tmpkca4bkve/e9430c18_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:33,919 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:54:33,919 - INFO - Splitting PDF into individual pages...
2025-09-23 23:54:33,921 - INFO - Splitting PDF e9430c18_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:54:33,925 - INFO - Split PDF into 1 pages
2025-09-23 23:54:33,925 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:54:33,925 - INFO - Expected pages: [1]
2025-09-23 23:54:35,965 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/db93a55f_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:35,965 - INFO - 🔍 [23:54:35] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:35,966 - INFO - ⬆️ [23:54:35] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:35,968 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:35,982 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:35,987 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/db93a55f_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:35,987 - INFO - Processing image from S3...
2025-09-23 23:54:36,763 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/d636e2b7_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:36,763 - INFO - 🔍 [23:54:36] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:36,764 - INFO - ⬆️ [23:54:36] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:36,765 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:36,785 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:36,790 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d636e2b7_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:36,790 - INFO - Processing PDF from S3...
2025-09-23 23:54:36,791 - INFO - Downloading PDF from S3 to /tmp/tmpi_z6n5mk/d636e2b7_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:37,455 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/84f929b7_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:37,456 - INFO - 🔍 [23:54:37] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:37,457 - INFO - ⬆️ [23:54:37] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:37,458 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:37,475 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:37,479 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/84f929b7_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:37,479 - INFO - Processing PDF from S3...
2025-09-23 23:54:37,480 - INFO - Downloading PDF from S3 to /tmp/tmp3dfj1ecn/84f929b7_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:37,627 - INFO - Page 1: Extracted 1260 characters, 84 lines from e9430c18_BQJUG5URFR2GH9ECWFV4_9cba02d2_page_001.pdf
2025-09-23 23:54:37,627 - INFO - Successfully processed page 1
2025-09-23 23:54:37,627 - INFO - Combined 1 pages into final text
2025-09-23 23:54:37,627 - INFO - Text validation for e9430c18_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:54:37,627 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:37,627 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:38,957 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:54:38,958 - INFO - Splitting PDF into individual pages...
2025-09-23 23:54:38,959 - INFO - Splitting PDF d636e2b7_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:54:38,964 - INFO - Split PDF into 1 pages
2025-09-23 23:54:38,964 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:54:38,964 - INFO - Expected pages: [1]
2025-09-23 23:54:39,287 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:54:39,287 - INFO - Splitting PDF into individual pages...
2025-09-23 23:54:39,289 - INFO - Splitting PDF 84f929b7_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:54:39,301 - INFO - Split PDF into 7 pages
2025-09-23 23:54:39,301 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:54:39,301 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:54:39,375 - INFO - S3 Image temp/db93a55f_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:54:39,375 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:39,376 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:39,959 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e9430c18_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:41,288 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/a8f36adb_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:41,288 - INFO - 🔍 [23:54:41] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:41,289 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:41,290 - INFO - ⬆️ [23:54:41] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:41,310 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:41,315 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a8f36adb_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:41,316 - INFO - Processing image from S3...
2025-09-23 23:54:42,276 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/db93a55f_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:43,322 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/8e97ee7c_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:43,322 - INFO - 🔍 [23:54:43] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:43,322 - INFO - ⬆️ [23:54:43] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:43,323 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:43,334 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:43,339 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8e97ee7c_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:43,340 - INFO - Processing image from S3...
2025-09-23 23:54:43,623 - INFO - Page 2: Extracted 1821 characters, 105 lines from 84f929b7_O2IU5G77LYNTYE0RP1TI_31b1b9d6_page_002.pdf
2025-09-23 23:54:43,623 - INFO - Successfully processed page 2
2025-09-23 23:54:43,913 - INFO - Page 1: Extracted 519 characters, 34 lines from d636e2b7_KE7TCH9TPQZFVA5CZ3HT_f7ddb1a5_page_001.pdf
2025-09-23 23:54:43,914 - INFO - Successfully processed page 1
2025-09-23 23:54:43,914 - INFO - Combined 1 pages into final text
2025-09-23 23:54:43,914 - INFO - Text validation for d636e2b7_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:54:43,914 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:43,915 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:43,964 - INFO - Page 1: Extracted 1731 characters, 110 lines from 84f929b7_O2IU5G77LYNTYE0RP1TI_31b1b9d6_page_001.pdf
2025-09-23 23:54:43,964 - INFO - Successfully processed page 1
2025-09-23 23:54:43,975 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/55dda65e_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:43,975 - INFO - 🔍 [23:54:43] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:43,976 - INFO - ⬆️ [23:54:43] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:43,976 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:43,994 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:44,015 - INFO - Page 5: Extracted 2059 characters, 131 lines from 84f929b7_O2IU5G77LYNTYE0RP1TI_31b1b9d6_page_005.pdf
2025-09-23 23:54:44,016 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/55dda65e_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:44,016 - INFO - Successfully processed page 5
2025-09-23 23:54:44,017 - INFO - Processing image from S3...
2025-09-23 23:54:44,199 - INFO - Page 4: Extracted 2242 characters, 148 lines from 84f929b7_O2IU5G77LYNTYE0RP1TI_31b1b9d6_page_004.pdf
2025-09-23 23:54:44,199 - INFO - Successfully processed page 4
2025-09-23 23:54:44,594 - INFO - Page 6: Extracted 1973 characters, 129 lines from 84f929b7_O2IU5G77LYNTYE0RP1TI_31b1b9d6_page_006.pdf
2025-09-23 23:54:44,594 - INFO - Successfully processed page 6
2025-09-23 23:54:44,967 - INFO - Page 3: Extracted 2265 characters, 147 lines from 84f929b7_O2IU5G77LYNTYE0RP1TI_31b1b9d6_page_003.pdf
2025-09-23 23:54:44,967 - INFO - Successfully processed page 3
2025-09-23 23:54:45,186 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/d40e3b1b_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:45,186 - INFO - 🔍 [23:54:45] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:45,188 - INFO - Initializing TextractProcessor...
2025-09-23 23:54:45,199 - INFO - Initializing BedrockProcessor...
2025-09-23 23:54:45,201 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d40e3b1b_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:45,202 - INFO - Processing image from S3...
2025-09-23 23:54:45,214 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:45,214 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:54:45,513 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e9430c18_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:45,526 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-23 23:54:45,526 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:54:45,813 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/db93a55f_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:45,906 - INFO - S3 Image temp/55dda65e_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:54:45,906 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:45,906 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:46,226 - INFO - Page 7: Extracted 417 characters, 27 lines from 84f929b7_O2IU5G77LYNTYE0RP1TI_31b1b9d6_page_007.pdf
2025-09-23 23:54:46,227 - INFO - Successfully processed page 7
2025-09-23 23:54:46,228 - INFO - Combined 7 pages into final text
2025-09-23 23:54:46,228 - INFO - Text validation for 84f929b7_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:54:46,229 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:46,229 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:47,248 - INFO - S3 Image temp/d40e3b1b_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:54:47,248 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:47,248 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:47,340 - INFO - S3 Image temp/8e97ee7c_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:54:47,340 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:47,340 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:47,392 - INFO - S3 Image temp/a8f36adb_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:54:47,392 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:54:47,392 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:54:47,809 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/55dda65e_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:47,812 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:47,812 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:54:47,842 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d636e2b7_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:48,115 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/55dda65e_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:48,129 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-23 23:54:48,129 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:54:48,427 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d636e2b7_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:49,376 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a8f36adb_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:49,378 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/84f929b7_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:49,380 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d40e3b1b_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:49,436 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:49,437 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:54:49,787 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a8f36adb_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:49,791 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:54:49,792 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:54:50,089 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d40e3b1b_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:50,130 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8e97ee7c_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:50,216 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:50,217 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,534 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/84f929b7_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:50,553 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:54:50,553 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:54:50,926 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8e97ee7c_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:50,927 - INFO - 
📊 Processing Summary:
2025-09-23 23:54:50,927 - INFO -    Total files: 8
2025-09-23 23:54:50,927 - INFO -    Successful: 8
2025-09-23 23:54:50,927 - INFO -    Failed: 0
2025-09-23 23:54:50,927 - INFO -    Duration: 19.94 seconds
2025-09-23 23:54:50,927 - INFO -    Output directory: output
2025-09-23 23:54:50,927 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:54:50,927 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:54:50,927 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-23 23:54:50,927 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-23 23:54:50,928 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:54:50,928 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:54:50,928 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:54:50,928 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:54:50,928 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:54:50,928 - INFO - 
============================================================================================================================================
2025-09-23 23:54:50,928 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:54:50,928 - INFO - ============================================================================================================================================
2025-09-23 23:54:50,928 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:54:50,928 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:54:50,928 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:54:50,929 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:54:50,929 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:54:50,929 - INFO - 
2025-09-23 23:54:50,929 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      outgate              run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:54:50,929 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:54:50,929 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:54:50,929 - INFO - 
2025-09-23 23:54:50,929 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      coa                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:54:50,929 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:54:50,929 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:54:50,929 - INFO - 
2025-09-23 23:54:50,929 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:50,929 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:50,929 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,929 - INFO - 
2025-09-23 23:54:50,929 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:50,929 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:50,929 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,929 - INFO - 
2025-09-23 23:54:50,930 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:50,930 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:50,930 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,930 - INFO - 
2025-09-23 23:54:50,930 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:50,930 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:50,930 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,930 - INFO - 
2025-09-23 23:54:50,930 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:50,930 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:50,930 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,930 - INFO - 
2025-09-23 23:54:50,930 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:50,930 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:50,930 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,930 - INFO - 
2025-09-23 23:54:50,930 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:54:50,930 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:54:50,930 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,930 - INFO - 
2025-09-23 23:54:50,931 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:54:50,931 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:54:50,931 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:54:50,931 - INFO - 
2025-09-23 23:54:50,931 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:54:50,931 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:54:50,931 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:54:50,931 - INFO - 
2025-09-23 23:54:50,931 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:54:50,931 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:54:50,931 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:54:50,931 - INFO - 
2025-09-23 23:54:50,931 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:54:50,931 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:54:50,931 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:54:50,931 - INFO - 
2025-09-23 23:54:50,931 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:54:50,931 - INFO - Total entries: 14
2025-09-23 23:54:50,931 - INFO - ============================================================================================================================================
2025-09-23 23:54:50,931 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:54:50,932 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:54:50,932 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:54:50,932 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → outgate         | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:54:50,932 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → coa             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:54:50,932 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,932 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,932 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,932 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,932 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,932 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,932 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:54:50,932 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:54:50,932 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:54:50,932 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:54:50,932 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:54:50,932 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:54:50,933 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 19.944766, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
