2025-09-23 23:55:57,319 - INFO - Logging initialized. Log file: logs/test_classification_20250923_235557.log
2025-09-23 23:55:57,320 - INFO - 📁 Found 8 files to process
2025-09-23 23:55:57,320 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:55:57,320 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:55:57,320 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:55:57,320 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:55:57,320 - INFO - ⬆️ [23:55:57] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:58,601 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/2a95b7fd_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:58,601 - INFO - 🔍 [23:55:58] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:58,602 - INFO - ⬆️ [23:55:58] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:58,602 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:58,634 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:58,651 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2a95b7fd_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:58,652 - INFO - Processing PDF from S3...
2025-09-23 23:55:58,653 - INFO - Downloading PDF from S3 to /tmp/tmpnzjvzmg3/2a95b7fd_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:00,145 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:56:00,146 - INFO - Splitting PDF into individual pages...
2025-09-23 23:56:00,146 - INFO - Splitting PDF 2a95b7fd_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:56:00,150 - INFO - Split PDF into 1 pages
2025-09-23 23:56:00,150 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:56:00,150 - INFO - Expected pages: [1]
2025-09-23 23:56:04,602 - INFO - Page 1: Extracted 1260 characters, 84 lines from 2a95b7fd_BQJUG5URFR2GH9ECWFV4_d565aa10_page_001.pdf
2025-09-23 23:56:04,603 - INFO - Successfully processed page 1
2025-09-23 23:56:04,603 - INFO - Combined 1 pages into final text
2025-09-23 23:56:04,603 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/519e0763_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:04,603 - INFO - Text validation for 2a95b7fd_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:56:04,603 - INFO - 🔍 [23:56:04] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:04,603 - INFO - ⬆️ [23:56:04] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:04,604 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:04,606 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:04,608 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:04,617 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:04,620 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/519e0763_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:04,620 - INFO - Processing image from S3...
2025-09-23 23:56:05,570 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/d6307676_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:05,570 - INFO - 🔍 [23:56:05] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:05,570 - INFO - ⬆️ [23:56:05] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:05,572 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:05,583 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:05,586 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d6307676_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:05,586 - INFO - Processing PDF from S3...
2025-09-23 23:56:05,586 - INFO - Downloading PDF from S3 to /tmp/tmppapbpg5j/d6307676_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:06,249 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/c9e493bc_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:06,249 - INFO - 🔍 [23:56:06] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:06,250 - INFO - ⬆️ [23:56:06] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:06,252 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:06,267 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:06,270 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c9e493bc_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:06,270 - INFO - Processing PDF from S3...
2025-09-23 23:56:06,270 - INFO - Downloading PDF from S3 to /tmp/tmpwqw5gio2/c9e493bc_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:06,556 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2a95b7fd_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:07,669 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:56:07,669 - INFO - Splitting PDF into individual pages...
2025-09-23 23:56:07,670 - INFO - Splitting PDF d6307676_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:56:07,675 - INFO - Split PDF into 1 pages
2025-09-23 23:56:07,675 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:56:07,675 - INFO - Expected pages: [1]
2025-09-23 23:56:08,030 - INFO - S3 Image temp/519e0763_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:56:08,030 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:08,030 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:08,409 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:56:08,410 - INFO - Splitting PDF into individual pages...
2025-09-23 23:56:08,411 - INFO - Splitting PDF c9e493bc_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:56:08,415 - INFO - Split PDF into 7 pages
2025-09-23 23:56:08,415 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:56:08,415 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:56:09,925 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/519e0763_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:10,682 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/d7153abf_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:10,682 - INFO - 🔍 [23:56:10] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:10,683 - INFO - ⬆️ [23:56:10] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:10,683 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:10,702 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:10,706 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d7153abf_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:10,706 - INFO - Processing image from S3...
2025-09-23 23:56:12,459 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/d2b5fc5b_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:12,460 - INFO - 🔍 [23:56:12] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:12,460 - INFO - ⬆️ [23:56:12] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:12,462 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:12,475 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:12,533 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d2b5fc5b_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:12,534 - INFO - Page 1: Extracted 519 characters, 34 lines from d6307676_KE7TCH9TPQZFVA5CZ3HT_f73fbb78_page_001.pdf
2025-09-23 23:56:12,534 - INFO - Processing image from S3...
2025-09-23 23:56:12,537 - INFO - Successfully processed page 1
2025-09-23 23:56:12,537 - INFO - Combined 1 pages into final text
2025-09-23 23:56:12,537 - INFO - Text validation for d6307676_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:56:12,538 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:12,538 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:12,694 - INFO - Page 2: Extracted 1821 characters, 105 lines from c9e493bc_O2IU5G77LYNTYE0RP1TI_46f95bb1_page_002.pdf
2025-09-23 23:56:12,694 - INFO - Successfully processed page 2
2025-09-23 23:56:13,006 - INFO - Page 5: Extracted 2059 characters, 131 lines from c9e493bc_O2IU5G77LYNTYE0RP1TI_46f95bb1_page_005.pdf
2025-09-23 23:56:13,006 - INFO - Successfully processed page 5
2025-09-23 23:56:13,103 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/fe6faa20_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:13,116 - INFO - Page 4: Extracted 2242 characters, 148 lines from c9e493bc_O2IU5G77LYNTYE0RP1TI_46f95bb1_page_004.pdf
2025-09-23 23:56:13,116 - INFO - 🔍 [23:56:13] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:13,116 - INFO - Successfully processed page 4
2025-09-23 23:56:13,117 - INFO - ⬆️ [23:56:13] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:13,117 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:13,125 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:13,127 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fe6faa20_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:13,127 - INFO - Processing image from S3...
2025-09-23 23:56:13,143 - INFO - Page 3: Extracted 2265 characters, 147 lines from c9e493bc_O2IU5G77LYNTYE0RP1TI_46f95bb1_page_003.pdf
2025-09-23 23:56:13,144 - INFO - Successfully processed page 3
2025-09-23 23:56:13,457 - INFO - Page 6: Extracted 1973 characters, 129 lines from c9e493bc_O2IU5G77LYNTYE0RP1TI_46f95bb1_page_006.pdf
2025-09-23 23:56:13,457 - INFO - Successfully processed page 6
2025-09-23 23:56:13,949 - INFO - Page 1: Extracted 1731 characters, 110 lines from c9e493bc_O2IU5G77LYNTYE0RP1TI_46f95bb1_page_001.pdf
2025-09-23 23:56:13,950 - INFO - Successfully processed page 1
2025-09-23 23:56:14,075 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/183cca72_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:14,076 - INFO - 🔍 [23:56:14] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:14,077 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:14,094 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:14,102 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/183cca72_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:14,104 - INFO - Processing image from S3...
2025-09-23 23:56:14,125 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:14,126 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:56:14,437 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2a95b7fd_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:14,449 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:14,450 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:56:14,742 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/519e0763_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:14,983 - INFO - S3 Image temp/fe6faa20_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:56:14,984 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:14,984 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:15,447 - INFO - Page 7: Extracted 417 characters, 27 lines from c9e493bc_O2IU5G77LYNTYE0RP1TI_46f95bb1_page_007.pdf
2025-09-23 23:56:15,447 - INFO - Successfully processed page 7
2025-09-23 23:56:15,448 - INFO - Combined 7 pages into final text
2025-09-23 23:56:15,449 - INFO - Text validation for c9e493bc_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:56:15,449 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:15,449 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:15,616 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d6307676_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:15,634 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-23 23:56:15,634 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:56:15,925 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d6307676_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:16,215 - INFO - S3 Image temp/183cca72_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:56:16,216 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:16,216 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:16,277 - INFO - S3 Image temp/d7153abf_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:56:16,277 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:16,277 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:16,998 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fe6faa20_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:17,004 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:17,004 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:56:17,295 - INFO - S3 Image temp/d2b5fc5b_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:56:17,295 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:17,295 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:17,327 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fe6faa20_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:17,620 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c9e493bc_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:17,743 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:17,743 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:18,039 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c9e493bc_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:18,204 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d7153abf_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:18,263 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:18,263 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:56:18,553 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d7153abf_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:19,105 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/183cca72_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:19,111 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:19,111 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:56:19,453 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/183cca72_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:19,745 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d2b5fc5b_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:19,764 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:56:19,764 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:56:20,049 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d2b5fc5b_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:20,050 - INFO - 
📊 Processing Summary:
2025-09-23 23:56:20,050 - INFO -    Total files: 8
2025-09-23 23:56:20,050 - INFO -    Successful: 8
2025-09-23 23:56:20,050 - INFO -    Failed: 0
2025-09-23 23:56:20,050 - INFO -    Duration: 22.73 seconds
2025-09-23 23:56:20,050 - INFO -    Output directory: output
2025-09-23 23:56:20,050 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:56:20,050 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:56:20,050 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:56:20,050 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-23 23:56:20,050 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:56:20,050 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:56:20,051 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:56:20,051 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:56:20,051 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:56:20,051 - INFO - 
============================================================================================================================================
2025-09-23 23:56:20,051 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:56:20,051 - INFO - ============================================================================================================================================
2025-09-23 23:56:20,051 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:56:20,051 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:56:20,051 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:56:20,051 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:20,051 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:56:20,051 - INFO - 
2025-09-23 23:56:20,052 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:56:20,052 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:20,052 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:56:20,052 - INFO - 
2025-09-23 23:56:20,052 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      coa                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:56:20,052 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:20,052 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:56:20,052 - INFO - 
2025-09-23 23:56:20,052 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:20,052 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:20,052 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,052 - INFO - 
2025-09-23 23:56:20,052 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:20,052 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:20,052 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,052 - INFO - 
2025-09-23 23:56:20,052 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:20,052 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:20,052 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,052 - INFO - 
2025-09-23 23:56:20,053 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:20,053 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:20,053 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,053 - INFO - 
2025-09-23 23:56:20,053 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:20,053 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:20,053 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,053 - INFO - 
2025-09-23 23:56:20,053 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:20,053 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:20,053 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,053 - INFO - 
2025-09-23 23:56:20,053 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:20,053 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:20,053 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,053 - INFO - 
2025-09-23 23:56:20,053 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:56:20,053 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:20,053 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:56:20,054 - INFO - 
2025-09-23 23:56:20,054 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      other                run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:56:20,054 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:20,054 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:56:20,054 - INFO - 
2025-09-23 23:56:20,054 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:56:20,054 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:20,054 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:56:20,054 - INFO - 
2025-09-23 23:56:20,054 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      log                  run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:56:20,054 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:20,054 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:56:20,054 - INFO - 
2025-09-23 23:56:20,054 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:56:20,054 - INFO - Total entries: 14
2025-09-23 23:56:20,054 - INFO - ============================================================================================================================================
2025-09-23 23:56:20,054 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:56:20,054 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:56:20,054 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:56:20,054 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:56:20,054 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → coa             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:56:20,054 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,054 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,054 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,054 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,054 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,054 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,054 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:20,054 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:56:20,054 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → other           | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:56:20,054 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:56:20,054 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → log             | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:56:20,054 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:56:20,054 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 22.7301, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
