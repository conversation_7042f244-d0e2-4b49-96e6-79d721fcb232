2025-09-23 23:48:15,207 - INFO - Logging initialized. Log file: logs/test_classification_20250923_234815.log
2025-09-23 23:48:15,207 - INFO - 📁 Found 8 files to process
2025-09-23 23:48:15,207 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:48:15,207 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:48:15,207 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:48:15,207 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:48:15,207 - INFO - ⬆️ [23:48:15] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:48:16,767 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/7f4edb56_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:48:16,767 - INFO - 🔍 [23:48:16] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:48:16,767 - INFO - ⬆️ [23:48:16] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:48:16,768 - INFO - Initializing TextractProcessor...
2025-09-23 23:48:16,788 - INFO - Initializing BedrockProcessor...
2025-09-23 23:48:16,793 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7f4edb56_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:48:16,793 - INFO - Processing PDF from S3...
2025-09-23 23:48:16,793 - INFO - Downloading PDF from S3 to /tmp/tmp3rqxxhy5/7f4edb56_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:48:17,978 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:48:17,978 - INFO - Splitting PDF into individual pages...
2025-09-23 23:48:17,980 - INFO - Splitting PDF 7f4edb56_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:48:17,985 - INFO - Split PDF into 1 pages
2025-09-23 23:48:17,985 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:48:17,985 - INFO - Expected pages: [1]
2025-09-23 23:48:19,047 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/c7aaa17b_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:48:19,048 - INFO - 🔍 [23:48:19] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:48:19,049 - INFO - ⬆️ [23:48:19] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:48:19,055 - INFO - Initializing TextractProcessor...
2025-09-23 23:48:19,068 - INFO - Initializing BedrockProcessor...
2025-09-23 23:48:19,072 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c7aaa17b_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:48:19,073 - INFO - Processing image from S3...
2025-09-23 23:48:19,651 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/36dd2104_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:48:19,652 - INFO - 🔍 [23:48:19] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:48:19,653 - INFO - Initializing TextractProcessor...
2025-09-23 23:48:19,653 - INFO - ⬆️ [23:48:19] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:19,667 - INFO - Initializing BedrockProcessor...
2025-09-23 23:48:19,677 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/36dd2104_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:48:19,677 - INFO - Processing PDF from S3...
2025-09-23 23:48:19,677 - INFO - Downloading PDF from S3 to /tmp/tmp84d2c5bd/36dd2104_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:48:20,379 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/d330f6db_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:20,379 - INFO - 🔍 [23:48:20] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:20,380 - INFO - ⬆️ [23:48:20] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:48:20,382 - INFO - Initializing TextractProcessor...
2025-09-23 23:48:20,404 - INFO - Initializing BedrockProcessor...
2025-09-23 23:48:20,408 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d330f6db_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:20,408 - INFO - Processing PDF from S3...
2025-09-23 23:48:20,409 - INFO - Downloading PDF from S3 to /tmp/tmp8go0tbar/d330f6db_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:21,761 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:48:21,761 - INFO - Splitting PDF into individual pages...
2025-09-23 23:48:21,762 - INFO - Splitting PDF 36dd2104_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:48:21,767 - INFO - Split PDF into 1 pages
2025-09-23 23:48:21,767 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:48:21,767 - INFO - Expected pages: [1]
2025-09-23 23:48:21,900 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/31ef857d_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:48:21,900 - INFO - 🔍 [23:48:21] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:48:21,901 - INFO - ⬆️ [23:48:21] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:48:21,902 - INFO - Initializing TextractProcessor...
2025-09-23 23:48:21,921 - INFO - Initializing BedrockProcessor...
2025-09-23 23:48:21,926 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/31ef857d_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:48:21,926 - INFO - Processing image from S3...
2025-09-23 23:48:22,362 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:48:22,363 - INFO - Splitting PDF into individual pages...
2025-09-23 23:48:22,367 - INFO - Splitting PDF d330f6db_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:48:22,385 - INFO - Page 1: Extracted 1260 characters, 84 lines from 7f4edb56_BQJUG5URFR2GH9ECWFV4_9d11d633_page_001.pdf
2025-09-23 23:48:22,385 - INFO - Successfully processed page 1
2025-09-23 23:48:22,386 - INFO - Combined 1 pages into final text
2025-09-23 23:48:22,386 - INFO - Text validation for 7f4edb56_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:48:22,388 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:48:22,388 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:48:22,397 - INFO - Split PDF into 7 pages
2025-09-23 23:48:22,398 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:48:22,398 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:48:22,639 - INFO - S3 Image temp/c7aaa17b_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:48:22,639 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:48:22,639 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:48:22,919 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/8cb4a877_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:48:22,919 - INFO - 🔍 [23:48:22] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:48:22,920 - INFO - ⬆️ [23:48:22] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:48:22,921 - INFO - Initializing TextractProcessor...
2025-09-23 23:48:22,934 - INFO - Initializing BedrockProcessor...
2025-09-23 23:48:22,939 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8cb4a877_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:48:22,939 - INFO - Processing image from S3...
2025-09-23 23:48:23,600 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/bcb06462_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:48:23,600 - INFO - 🔍 [23:48:23] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:48:23,600 - INFO - Initializing TextractProcessor...
2025-09-23 23:48:23,600 - INFO - ⬆️ [23:48:23] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:48:23,609 - INFO - Initializing BedrockProcessor...
2025-09-23 23:48:23,611 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bcb06462_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:48:23,611 - INFO - Processing image from S3...
2025-09-23 23:48:24,347 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/d875a738_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:48:24,348 - INFO - 🔍 [23:48:24] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:48:24,349 - INFO - Initializing TextractProcessor...
2025-09-23 23:48:24,364 - INFO - Initializing BedrockProcessor...
2025-09-23 23:48:24,367 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d875a738_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:48:24,367 - INFO - Processing image from S3...
2025-09-23 23:48:25,066 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7f4edb56_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:48:25,095 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:48:25,095 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:48:25,396 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7f4edb56_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:48:25,890 - INFO - S3 Image temp/bcb06462_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:48:25,891 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:48:25,891 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:48:26,282 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c7aaa17b_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:48:26,299 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:48:26,299 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:48:26,420 - INFO - S3 Image temp/d875a738_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:48:26,420 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:48:26,420 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:48:26,595 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c7aaa17b_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:48:26,810 - INFO - Page 1: Extracted 1731 characters, 110 lines from d330f6db_O2IU5G77LYNTYE0RP1TI_3441a300_page_001.pdf
2025-09-23 23:48:26,810 - INFO - Successfully processed page 1
2025-09-23 23:48:27,398 - INFO - S3 Image temp/8cb4a877_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:48:27,398 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:48:27,399 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:48:27,607 - INFO - Page 4: Extracted 2242 characters, 148 lines from d330f6db_O2IU5G77LYNTYE0RP1TI_3441a300_page_004.pdf
2025-09-23 23:48:27,607 - INFO - Successfully processed page 4
2025-09-23 23:48:27,693 - INFO - Page 3: Extracted 2265 characters, 147 lines from d330f6db_O2IU5G77LYNTYE0RP1TI_3441a300_page_003.pdf
2025-09-23 23:48:27,694 - INFO - Successfully processed page 3
2025-09-23 23:48:27,793 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bcb06462_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:48:27,800 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:48:27,800 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:48:27,829 - INFO - Page 2: Extracted 1821 characters, 105 lines from d330f6db_O2IU5G77LYNTYE0RP1TI_3441a300_page_002.pdf
2025-09-23 23:48:27,829 - INFO - Successfully processed page 2
2025-09-23 23:48:27,877 - INFO - Page 6: Extracted 1973 characters, 129 lines from d330f6db_O2IU5G77LYNTYE0RP1TI_3441a300_page_006.pdf
2025-09-23 23:48:27,896 - INFO - S3 Image temp/31ef857d_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:48:27,896 - INFO - Successfully processed page 6
2025-09-23 23:48:27,896 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:48:27,896 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:48:28,105 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bcb06462_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:48:28,168 - INFO - Page 1: Extracted 519 characters, 34 lines from 36dd2104_KE7TCH9TPQZFVA5CZ3HT_30516e70_page_001.pdf
2025-09-23 23:48:28,169 - INFO - Successfully processed page 1
2025-09-23 23:48:28,169 - INFO - Combined 1 pages into final text
2025-09-23 23:48:28,170 - INFO - Text validation for 36dd2104_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:48:28,170 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:48:28,170 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:48:28,480 - INFO - Page 5: Extracted 2059 characters, 131 lines from d330f6db_O2IU5G77LYNTYE0RP1TI_3441a300_page_005.pdf
2025-09-23 23:48:28,481 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d875a738_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:48:28,481 - INFO - Successfully processed page 5
2025-09-23 23:48:28,484 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:48:28,484 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:48:28,773 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d875a738_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:48:29,348 - INFO - Page 7: Extracted 417 characters, 27 lines from d330f6db_O2IU5G77LYNTYE0RP1TI_3441a300_page_007.pdf
2025-09-23 23:48:29,349 - INFO - Successfully processed page 7
2025-09-23 23:48:29,349 - INFO - Combined 7 pages into final text
2025-09-23 23:48:29,351 - INFO - Text validation for d330f6db_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:48:29,351 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:48:29,351 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:48:30,107 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/31ef857d_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:48:30,149 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:48:30,149 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:48:30,501 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/31ef857d_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:48:31,438 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/36dd2104_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:48:31,452 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-23 23:48:31,452 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:48:31,651 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8cb4a877_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:48:31,738 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/36dd2104_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:48:31,756 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:48:31,756 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:48:32,046 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8cb4a877_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:48:32,087 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d330f6db_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:32,204 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:48:32,204 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,515 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d330f6db_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:32,519 - INFO - 
📊 Processing Summary:
2025-09-23 23:48:32,519 - INFO -    Total files: 8
2025-09-23 23:48:32,519 - INFO -    Successful: 8
2025-09-23 23:48:32,519 - INFO -    Failed: 0
2025-09-23 23:48:32,519 - INFO -    Duration: 17.31 seconds
2025-09-23 23:48:32,519 - INFO -    Output directory: output
2025-09-23 23:48:32,519 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:48:32,519 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:48:32,519 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:48:32,519 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-23 23:48:32,520 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:48:32,520 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:48:32,520 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:48:32,520 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:48:32,520 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:48:32,520 - INFO - 
============================================================================================================================================
2025-09-23 23:48:32,520 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:48:32,520 - INFO - ============================================================================================================================================
2025-09-23 23:48:32,520 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:48:32,521 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:48:32,521 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:48:32,521 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:48:32,521 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:48:32,521 - INFO - 
2025-09-23 23:48:32,521 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:48:32,521 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:48:32,521 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:48:32,521 - INFO - 
2025-09-23 23:48:32,521 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      coa                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:48:32,521 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:48:32,521 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:48:32,521 - INFO - 
2025-09-23 23:48:32,521 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:48:32,521 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:32,521 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,521 - INFO - 
2025-09-23 23:48:32,521 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:48:32,522 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:32,522 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,522 - INFO - 
2025-09-23 23:48:32,522 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:48:32,522 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:32,522 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,522 - INFO - 
2025-09-23 23:48:32,522 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:48:32,522 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:32,522 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,522 - INFO - 
2025-09-23 23:48:32,522 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:48:32,522 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:32,522 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,522 - INFO - 
2025-09-23 23:48:32,522 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:48:32,522 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:32,522 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,522 - INFO - 
2025-09-23 23:48:32,522 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:48:32,522 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:48:32,523 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,523 - INFO - 
2025-09-23 23:48:32,523 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:48:32,523 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:48:32,523 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:48:32,523 - INFO - 
2025-09-23 23:48:32,523 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:48:32,523 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:48:32,523 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:48:32,523 - INFO - 
2025-09-23 23:48:32,523 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:48:32,523 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:48:32,523 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:48:32,523 - INFO - 
2025-09-23 23:48:32,523 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:48:32,523 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:48:32,523 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:48:32,523 - INFO - 
2025-09-23 23:48:32,523 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:48:32,523 - INFO - Total entries: 14
2025-09-23 23:48:32,523 - INFO - ============================================================================================================================================
2025-09-23 23:48:32,523 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:48:32,523 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:48:32,523 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:48:32,524 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:48:32,524 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → coa             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:48:32,524 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,524 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,524 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,524 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,524 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,524 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,524 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:48:32,524 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:48:32,524 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:48:32,524 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:48:32,524 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:48:32,524 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:48:32,524 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 17.311464, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
