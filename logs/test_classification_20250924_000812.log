2025-09-24 00:08:12,668 - INFO - Logging initialized. Log file: logs/test_classification_20250924_000812.log
2025-09-24 00:08:12,668 - INFO - 📁 Found 8 files to process
2025-09-24 00:08:12,668 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 00:08:12,668 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-24 00:08:12,668 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-24 00:08:12,668 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-24 00:08:12,668 - INFO - ⬆️ [00:08:12] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:08:14,634 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/d624918c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:08:14,634 - INFO - 🔍 [00:08:14] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:08:14,635 - INFO - ⬆️ [00:08:14] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:08:14,636 - INFO - Initializing TextractProcessor...
2025-09-24 00:08:14,656 - INFO - Initializing BedrockProcessor...
2025-09-24 00:08:14,662 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d624918c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:08:14,663 - INFO - Processing PDF from S3...
2025-09-24 00:08:14,663 - INFO - Downloading PDF from S3 to /tmp/tmpl6j1prbo/d624918c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:08:15,978 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:08:15,978 - INFO - Splitting PDF into individual pages...
2025-09-24 00:08:15,980 - INFO - Splitting PDF d624918c_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-24 00:08:15,985 - INFO - Split PDF into 1 pages
2025-09-24 00:08:15,986 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:08:15,986 - INFO - Expected pages: [1]
2025-09-24 00:08:17,243 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/e58fa179_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:08:17,243 - INFO - 🔍 [00:08:17] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:08:17,244 - INFO - ⬆️ [00:08:17] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:08:17,245 - INFO - Initializing TextractProcessor...
2025-09-24 00:08:17,266 - INFO - Initializing BedrockProcessor...
2025-09-24 00:08:17,271 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e58fa179_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:08:17,271 - INFO - Processing image from S3...
2025-09-24 00:08:17,913 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/006df4e3_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:08:17,914 - INFO - 🔍 [00:08:17] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:08:17,914 - INFO - ⬆️ [00:08:17] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:08:17,915 - INFO - Initializing TextractProcessor...
2025-09-24 00:08:17,937 - INFO - Initializing BedrockProcessor...
2025-09-24 00:08:17,941 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/006df4e3_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:08:17,942 - INFO - Processing PDF from S3...
2025-09-24 00:08:17,942 - INFO - Downloading PDF from S3 to /tmp/tmp2fbm3xwb/006df4e3_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:08:18,519 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/ac022a5e_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:08:18,519 - INFO - 🔍 [00:08:18] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:08:18,520 - INFO - ⬆️ [00:08:18] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:08:18,521 - INFO - Initializing TextractProcessor...
2025-09-24 00:08:18,541 - INFO - Initializing BedrockProcessor...
2025-09-24 00:08:18,548 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ac022a5e_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:08:18,548 - INFO - Processing PDF from S3...
2025-09-24 00:08:18,548 - INFO - Downloading PDF from S3 to /tmp/tmprcj53xo0/ac022a5e_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:08:19,843 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/f90b6e1f_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:08:19,843 - INFO - 🔍 [00:08:19] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:08:19,844 - INFO - ⬆️ [00:08:19] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:08:19,845 - INFO - Initializing TextractProcessor...
2025-09-24 00:08:19,868 - INFO - Initializing BedrockProcessor...
2025-09-24 00:08:19,871 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f90b6e1f_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:08:19,872 - INFO - Processing image from S3...
2025-09-24 00:08:19,929 - INFO - Page 1: Extracted 1260 characters, 84 lines from d624918c_BQJUG5URFR2GH9ECWFV4_d8d48156_page_001.pdf
2025-09-24 00:08:19,929 - INFO - Successfully processed page 1
2025-09-24 00:08:19,929 - INFO - Combined 1 pages into final text
2025-09-24 00:08:19,930 - INFO - Text validation for d624918c_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-24 00:08:19,930 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:08:19,930 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:08:20,042 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 00:08:20,042 - INFO - Splitting PDF into individual pages...
2025-09-24 00:08:20,043 - INFO - Splitting PDF 006df4e3_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-24 00:08:20,048 - INFO - Split PDF into 1 pages
2025-09-24 00:08:20,048 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:08:20,048 - INFO - Expected pages: [1]
2025-09-24 00:08:20,398 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:08:20,398 - INFO - Splitting PDF into individual pages...
2025-09-24 00:08:20,399 - INFO - Splitting PDF ac022a5e_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-24 00:08:20,406 - INFO - Split PDF into 7 pages
2025-09-24 00:08:20,406 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:08:20,406 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 00:08:20,949 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/82cd53b7_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:08:20,949 - INFO - 🔍 [00:08:20] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:08:20,950 - INFO - ⬆️ [00:08:20] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:08:20,951 - INFO - Initializing TextractProcessor...
2025-09-24 00:08:20,964 - INFO - Initializing BedrockProcessor...
2025-09-24 00:08:20,979 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/82cd53b7_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:08:21,025 - INFO - S3 Image temp/e58fa179_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-24 00:08:21,025 - INFO - Processing image from S3...
2025-09-24 00:08:21,025 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:08:21,030 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:08:21,625 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/d275ef99_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:08:21,625 - INFO - 🔍 [00:08:21] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:08:21,626 - INFO - ⬆️ [00:08:21] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:08:21,628 - INFO - Initializing TextractProcessor...
2025-09-24 00:08:21,646 - INFO - Initializing BedrockProcessor...
2025-09-24 00:08:21,650 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d275ef99_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:08:21,650 - INFO - Processing image from S3...
2025-09-24 00:08:21,748 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d624918c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:08:22,512 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/1bfc9e69_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:08:22,512 - INFO - 🔍 [00:08:22] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:08:22,514 - INFO - Initializing TextractProcessor...
2025-09-24 00:08:22,534 - INFO - Initializing BedrockProcessor...
2025-09-24 00:08:22,547 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1bfc9e69_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:08:22,549 - INFO - Processing image from S3...
2025-09-24 00:08:22,575 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:08:22,576 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:08:22,887 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d624918c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:08:23,747 - INFO - S3 Image temp/d275ef99_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-24 00:08:23,747 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:08:23,747 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:08:24,579 - INFO - S3 Image temp/1bfc9e69_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-24 00:08:24,579 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:08:24,579 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:08:24,585 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e58fa179_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:08:24,595 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:08:24,596 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:08:24,763 - INFO - Page 2: Extracted 1821 characters, 105 lines from ac022a5e_O2IU5G77LYNTYE0RP1TI_3f68c4a9_page_002.pdf
2025-09-24 00:08:24,763 - INFO - Successfully processed page 2
2025-09-24 00:08:24,900 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e58fa179_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:08:24,934 - INFO - S3 Image temp/f90b6e1f_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-24 00:08:24,934 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:08:24,934 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:08:24,945 - INFO - Page 1: Extracted 1731 characters, 110 lines from ac022a5e_O2IU5G77LYNTYE0RP1TI_3f68c4a9_page_001.pdf
2025-09-24 00:08:24,945 - INFO - Successfully processed page 1
2025-09-24 00:08:25,209 - INFO - S3 Image temp/82cd53b7_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-24 00:08:25,210 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:08:25,210 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:08:25,225 - INFO - Page 5: Extracted 2059 characters, 131 lines from ac022a5e_O2IU5G77LYNTYE0RP1TI_3f68c4a9_page_005.pdf
2025-09-24 00:08:25,225 - INFO - Successfully processed page 5
2025-09-24 00:08:25,381 - INFO - Page 3: Extracted 2265 characters, 147 lines from ac022a5e_O2IU5G77LYNTYE0RP1TI_3f68c4a9_page_003.pdf
2025-09-24 00:08:25,381 - INFO - Successfully processed page 3
2025-09-24 00:08:25,696 - INFO - Page 6: Extracted 1973 characters, 129 lines from ac022a5e_O2IU5G77LYNTYE0RP1TI_3f68c4a9_page_006.pdf
2025-09-24 00:08:25,697 - INFO - Successfully processed page 6
2025-09-24 00:08:25,834 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d275ef99_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:08:25,838 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:08:25,838 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:08:25,878 - INFO - Page 4: Extracted 2242 characters, 148 lines from ac022a5e_O2IU5G77LYNTYE0RP1TI_3f68c4a9_page_004.pdf
2025-09-24 00:08:25,878 - INFO - Successfully processed page 4
2025-09-24 00:08:26,130 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d275ef99_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:08:26,148 - INFO - Page 1: Extracted 519 characters, 34 lines from 006df4e3_KE7TCH9TPQZFVA5CZ3HT_decf3432_page_001.pdf
2025-09-24 00:08:26,148 - INFO - Successfully processed page 1
2025-09-24 00:08:26,149 - INFO - Combined 1 pages into final text
2025-09-24 00:08:26,150 - INFO - Text validation for 006df4e3_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-24 00:08:26,151 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:08:26,151 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:08:26,921 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f90b6e1f_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:08:26,986 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:08:26,986 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:08:27,278 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1bfc9e69_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:08:27,279 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f90b6e1f_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:08:27,282 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:08:27,282 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:08:27,457 - INFO - Page 7: Extracted 417 characters, 27 lines from ac022a5e_O2IU5G77LYNTYE0RP1TI_3f68c4a9_page_007.pdf
2025-09-24 00:08:27,457 - INFO - Successfully processed page 7
2025-09-24 00:08:27,458 - INFO - Combined 7 pages into final text
2025-09-24 00:08:27,459 - INFO - Text validation for ac022a5e_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-24 00:08:27,460 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:08:27,460 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:08:27,576 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1bfc9e69_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:08:28,404 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/006df4e3_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:08:28,419 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:08:28,419 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:08:28,559 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/82cd53b7_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:08:28,725 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/006df4e3_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:08:28,749 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 00:08:28,749 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:08:29,048 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/82cd53b7_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:08:29,836 - ERROR - Failed to extract tool response: 'data'
2025-09-24 00:08:29,837 - ERROR - Processing failed for s3://document-extraction-logistically/temp/ac022a5e_O2IU5G77LYNTYE0RP1TI.pdf: Unexpected tool response format from Bedrock
2025-09-24 00:08:29,838 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf: Unexpected tool response format from Bedrock
2025-09-24 00:08:30,197 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ac022a5e_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:08:30,197 - INFO - 
📊 Processing Summary:
2025-09-24 00:08:30,197 - INFO -    Total files: 8
2025-09-24 00:08:30,198 - INFO -    Successful: 7
2025-09-24 00:08:30,198 - INFO -    Failed: 1
2025-09-24 00:08:30,198 - INFO -    Duration: 17.53 seconds
2025-09-24 00:08:30,198 - INFO -    Output directory: output
2025-09-24 00:08:30,198 - INFO - 
📋 Successfully Processed Files:
2025-09-24 00:08:30,198 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:08:30,198 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:08:30,198 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:08:30,198 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:08:30,198 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 00:08:30,198 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:08:30,199 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:08:30,199 - ERROR - 
❌ Errors:
2025-09-24 00:08:30,199 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf: Unexpected tool response format from Bedrock
2025-09-24 00:08:30,199 - INFO - 
============================================================================================================================================
2025-09-24 00:08:30,199 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 00:08:30,199 - INFO - ============================================================================================================================================
2025-09-24 00:08:30,199 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 00:08:30,200 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:08:30,200 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-24 00:08:30,200 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:08:30,200 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:08:30,200 - INFO - 
2025-09-24 00:08:30,200 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-24 00:08:30,200 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:08:30,200 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:08:30,200 - INFO - 
2025-09-24 00:08:30,200 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      log                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-24 00:08:30,200 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:08:30,200 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:08:30,200 - INFO - 
2025-09-24 00:08:30,200 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-24 00:08:30,200 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:08:30,200 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:08:30,200 - INFO - 
2025-09-24 00:08:30,201 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      other                run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-24 00:08:30,201 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:08:30,201 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:08:30,201 - INFO - 
2025-09-24 00:08:30,201 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-24 00:08:30,201 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:08:30,201 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:08:30,201 - INFO - 
2025-09-24 00:08:30,201 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      log                  run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-24 00:08:30,201 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:08:30,201 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:08:30,201 - INFO - 
2025-09-24 00:08:30,201 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:08:30,201 - INFO - Total entries: 7
2025-09-24 00:08:30,201 - INFO - ============================================================================================================================================
2025-09-24 00:08:30,201 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 00:08:30,201 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:08:30,201 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:08:30,202 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:08:30,202 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → log             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:08:30,202 - INFO -   4. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:08:30,202 - INFO -   5. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → other           | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:08:30,202 - INFO -   6. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:08:30,202 - INFO -   7. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → log             | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:08:30,202 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:08:30,202 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 7, 'failed': 1, 'errors': ['Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 17.529153, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
