2025-09-23 23:51:20,438 - INFO - Logging initialized. Log file: logs/test_classification_20250923_235120.log
2025-09-23 23:51:20,439 - INFO - 📁 Found 8 files to process
2025-09-23 23:51:20,439 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:51:20,439 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:51:20,439 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:51:20,439 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:51:20,439 - INFO - ⬆️ [23:51:20] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:22,149 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/0a3cf2b8_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:22,149 - INFO - 🔍 [23:51:22] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:22,150 - INFO - ⬆️ [23:51:22] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:51:22,154 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:22,171 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:22,177 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0a3cf2b8_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:22,177 - INFO - Processing PDF from S3...
2025-09-23 23:51:22,177 - INFO - Downloading PDF from S3 to /tmp/tmpwkcek_sn/0a3cf2b8_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:23,607 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:51:23,607 - INFO - Splitting PDF into individual pages...
2025-09-23 23:51:23,608 - INFO - Splitting PDF 0a3cf2b8_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:51:23,612 - INFO - Split PDF into 1 pages
2025-09-23 23:51:23,612 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:51:23,612 - INFO - Expected pages: [1]
2025-09-23 23:51:26,123 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/6ec15cc7_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:51:26,123 - INFO - 🔍 [23:51:26] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:51:26,124 - INFO - ⬆️ [23:51:26] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:26,125 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:26,141 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:26,144 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6ec15cc7_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:51:26,144 - INFO - Processing image from S3...
2025-09-23 23:51:27,111 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/c37835de_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:27,111 - INFO - 🔍 [23:51:27] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:27,111 - INFO - ⬆️ [23:51:27] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:27,112 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:27,121 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:27,123 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c37835de_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:27,123 - INFO - Processing PDF from S3...
2025-09-23 23:51:27,124 - INFO - Downloading PDF from S3 to /tmp/tmpithpvxb2/c37835de_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:27,849 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/ad316b99_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:27,850 - INFO - 🔍 [23:51:27] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:27,850 - INFO - ⬆️ [23:51:27] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:27,852 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:27,870 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:27,874 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ad316b99_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:27,875 - INFO - Processing PDF from S3...
2025-09-23 23:51:27,875 - INFO - Downloading PDF from S3 to /tmp/tmpmvnrpk_b/ad316b99_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:28,002 - INFO - Page 1: Extracted 1260 characters, 84 lines from 0a3cf2b8_BQJUG5URFR2GH9ECWFV4_e9e16ec3_page_001.pdf
2025-09-23 23:51:28,002 - INFO - Successfully processed page 1
2025-09-23 23:51:28,002 - INFO - Combined 1 pages into final text
2025-09-23 23:51:28,002 - INFO - Text validation for 0a3cf2b8_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:51:28,002 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:28,002 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:29,263 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:51:29,263 - INFO - Splitting PDF into individual pages...
2025-09-23 23:51:29,265 - INFO - Splitting PDF c37835de_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:51:29,269 - INFO - Split PDF into 1 pages
2025-09-23 23:51:29,269 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:51:29,269 - INFO - Expected pages: [1]
2025-09-23 23:51:29,710 - INFO - S3 Image temp/6ec15cc7_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:51:29,710 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:29,710 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:29,714 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:51:29,715 - INFO - Splitting PDF into individual pages...
2025-09-23 23:51:29,716 - INFO - Splitting PDF ad316b99_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:51:29,725 - INFO - Split PDF into 7 pages
2025-09-23 23:51:29,725 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:51:29,725 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:51:29,802 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0a3cf2b8_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:32,470 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6ec15cc7_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:51:32,667 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/0c3ad4f1_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:32,667 - INFO - 🔍 [23:51:32] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:32,668 - INFO - ⬆️ [23:51:32] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:32,673 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:32,687 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:32,691 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0c3ad4f1_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:32,691 - INFO - Processing image from S3...
2025-09-23 23:51:33,889 - INFO - Page 1: Extracted 1731 characters, 110 lines from ad316b99_O2IU5G77LYNTYE0RP1TI_390b1d3a_page_001.pdf
2025-09-23 23:51:33,889 - INFO - Successfully processed page 1
2025-09-23 23:51:34,053 - INFO - Page 1: Extracted 519 characters, 34 lines from c37835de_KE7TCH9TPQZFVA5CZ3HT_79e982de_page_001.pdf
2025-09-23 23:51:34,053 - INFO - Successfully processed page 1
2025-09-23 23:51:34,053 - INFO - Combined 1 pages into final text
2025-09-23 23:51:34,053 - INFO - Text validation for c37835de_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:51:34,053 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:34,053 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:34,341 - INFO - Page 3: Extracted 2265 characters, 147 lines from ad316b99_O2IU5G77LYNTYE0RP1TI_390b1d3a_page_003.pdf
2025-09-23 23:51:34,341 - INFO - Successfully processed page 3
2025-09-23 23:51:34,888 - INFO - Page 5: Extracted 2059 characters, 131 lines from ad316b99_O2IU5G77LYNTYE0RP1TI_390b1d3a_page_005.pdf
2025-09-23 23:51:34,888 - INFO - Successfully processed page 5
2025-09-23 23:51:34,927 - INFO - Page 2: Extracted 1821 characters, 105 lines from ad316b99_O2IU5G77LYNTYE0RP1TI_390b1d3a_page_002.pdf
2025-09-23 23:51:34,928 - INFO - Successfully processed page 2
2025-09-23 23:51:35,489 - INFO - Page 6: Extracted 1973 characters, 129 lines from ad316b99_O2IU5G77LYNTYE0RP1TI_390b1d3a_page_006.pdf
2025-09-23 23:51:35,489 - INFO - Successfully processed page 6
2025-09-23 23:51:35,679 - INFO - Page 4: Extracted 2242 characters, 148 lines from ad316b99_O2IU5G77LYNTYE0RP1TI_390b1d3a_page_004.pdf
2025-09-23 23:51:35,679 - INFO - Successfully processed page 4
2025-09-23 23:51:36,589 - INFO - Page 7: Extracted 417 characters, 27 lines from ad316b99_O2IU5G77LYNTYE0RP1TI_390b1d3a_page_007.pdf
2025-09-23 23:51:36,589 - INFO - Successfully processed page 7
2025-09-23 23:51:36,590 - INFO - Combined 7 pages into final text
2025-09-23 23:51:36,590 - INFO - Text validation for ad316b99_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:51:36,591 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:36,591 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:37,899 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/de6b8018_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:37,900 - INFO - 🔍 [23:51:37] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:37,901 - INFO - ⬆️ [23:51:37] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:37,901 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:37,919 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:37,922 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/de6b8018_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:37,922 - INFO - Processing image from S3...
2025-09-23 23:51:38,270 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c37835de_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:38,371 - INFO - S3 Image temp/0c3ad4f1_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:51:38,371 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:38,371 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:38,985 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ad316b99_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:39,216 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/8f814f87_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:39,217 - INFO - 🔍 [23:51:39] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:39,217 - INFO - ⬆️ [23:51:39] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:39,218 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:39,234 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:39,238 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8f814f87_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:39,239 - INFO - Processing image from S3...
2025-09-23 23:51:40,253 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0c3ad4f1_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:41,073 - INFO - S3 Image temp/8f814f87_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:51:41,074 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:41,074 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:42,107 - INFO - S3 Image temp/de6b8018_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:51:42,108 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:42,108 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:42,747 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/de7b8829_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:42,747 - INFO - 🔍 [23:51:42] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:42,748 - INFO - Initializing TextractProcessor...
2025-09-23 23:51:42,759 - INFO - Initializing BedrockProcessor...
2025-09-23 23:51:42,768 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/de7b8829_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:42,772 - INFO - Processing image from S3...
2025-09-23 23:51:42,809 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:42,809 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:51:43,120 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0a3cf2b8_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:43,132 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:43,132 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:51:43,223 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8f814f87_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:43,414 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6ec15cc7_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:51:43,427 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-23 23:51:43,428 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:51:43,812 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c37835de_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:43,935 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:43,935 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:44,246 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ad316b99_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:44,290 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:44,290 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:51:44,569 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/de6b8018_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:44,656 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0c3ad4f1_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:44,661 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:44,661 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:51:44,819 - INFO - S3 Image temp/de7b8829_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:51:44,819 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:51:44,820 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:51:44,963 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8f814f87_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:44,981 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:44,981 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:51:45,389 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/de6b8018_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:47,728 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/de7b8829_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:47,733 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:51:47,733 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:51:48,034 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/de7b8829_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:48,035 - INFO - 
📊 Processing Summary:
2025-09-23 23:51:48,035 - INFO -    Total files: 8
2025-09-23 23:51:48,035 - INFO -    Successful: 8
2025-09-23 23:51:48,035 - INFO -    Failed: 0
2025-09-23 23:51:48,035 - INFO -    Duration: 27.60 seconds
2025-09-23 23:51:48,035 - INFO -    Output directory: output
2025-09-23 23:51:48,036 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:51:48,036 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:48,036 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:48,036 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-23 23:51:48,036 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:51:48,036 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:48,036 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:48,036 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:48,036 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:51:48,037 - INFO - 
============================================================================================================================================
2025-09-23 23:51:48,037 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:51:48,037 - INFO - ============================================================================================================================================
2025-09-23 23:51:48,037 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:51:48,037 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:51:48,037 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:51:48,037 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:51:48,037 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:51:48,037 - INFO - 
2025-09-23 23:51:48,037 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:51:48,037 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:51:48,038 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:51:48,038 - INFO - 
2025-09-23 23:51:48,038 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      coa                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:51:48,038 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:51:48,038 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:51:48,038 - INFO - 
2025-09-23 23:51:48,038 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:48,038 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:48,038 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,038 - INFO - 
2025-09-23 23:51:48,038 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:48,038 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:48,038 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,038 - INFO - 
2025-09-23 23:51:48,038 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:48,038 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:48,038 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,038 - INFO - 
2025-09-23 23:51:48,039 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:48,039 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:48,039 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,039 - INFO - 
2025-09-23 23:51:48,039 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:48,039 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:48,039 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,039 - INFO - 
2025-09-23 23:51:48,039 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:48,039 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:48,039 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,039 - INFO - 
2025-09-23 23:51:48,039 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:51:48,039 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:51:48,039 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,039 - INFO - 
2025-09-23 23:51:48,039 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:51:48,039 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:51:48,039 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:51:48,039 - INFO - 
2025-09-23 23:51:48,040 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:51:48,040 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:51:48,040 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:51:48,040 - INFO - 
2025-09-23 23:51:48,040 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:51:48,040 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:51:48,040 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:51:48,040 - INFO - 
2025-09-23 23:51:48,040 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      log                  run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:51:48,040 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:51:48,040 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:51:48,040 - INFO - 
2025-09-23 23:51:48,040 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:51:48,040 - INFO - Total entries: 14
2025-09-23 23:51:48,040 - INFO - ============================================================================================================================================
2025-09-23 23:51:48,040 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:51:48,040 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:51:48,040 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:51:48,041 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:51:48,041 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → coa             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:51:48,041 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,041 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,041 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,041 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,041 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,041 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,041 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:51:48,041 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:51:48,041 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:51:48,041 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:51:48,041 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → log             | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:51:48,041 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:51:48,041 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 27.596096, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
