2025-09-24 12:27:20,492 - INFO - Logging initialized. Log file: logs/test_classification_20250924_122720.log
2025-09-24 12:27:20,493 - INFO - 📁 Found 13 files to process
2025-09-24 12:27:20,493 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 12:27:20,493 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 12:27:20,493 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 12:27:20,493 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 12:27:20,493 - INFO - ⬆️ [12:27:20] Uploading: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:27:22,334 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/cf416886_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:27:22,334 - INFO - 🔍 [12:27:22] Starting classification: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:27:22,336 - INFO - ⬆️ [12:27:22] Uploading: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:27:22,336 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:22,421 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:22,436 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cf416886_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:27:22,436 - INFO - Processing PDF from S3...
2025-09-24 12:27:22,437 - INFO - Downloading PDF from S3 to /tmp/tmpyh2gnoxz/cf416886_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:27:23,429 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/ddbd9863_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:27:23,431 - INFO - 🔍 [12:27:23] Starting classification: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:27:23,446 - INFO - ⬆️ [12:27:23] Uploading: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:27:23,458 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:23,543 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:23,558 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ddbd9863_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:27:23,559 - INFO - Processing PDF from S3...
2025-09-24 12:27:23,559 - INFO - Downloading PDF from S3 to /tmp/tmpsmyzosmg/ddbd9863_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:27:23,770 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:27:23,770 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:23,778 - INFO - Splitting PDF cf416886_I_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 12:27:23,788 - INFO - Split PDF into 1 pages
2025-09-24 12:27:23,789 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:23,789 - INFO - Expected pages: [1]
2025-09-24 12:27:24,665 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/2ac889ed_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:27:24,666 - INFO - 🔍 [12:27:24] Starting classification: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:27:24,670 - INFO - ⬆️ [12:27:24] Uploading: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:27:24,673 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:24,735 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:24,740 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2ac889ed_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:27:24,741 - INFO - Processing PDF from S3...
2025-09-24 12:27:24,742 - INFO - Downloading PDF from S3 to /tmp/tmp7zjrfogd/2ac889ed_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:27:26,401 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/f6f4a694_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:27:26,401 - INFO - 🔍 [12:27:26] Starting classification: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:27:26,403 - INFO - ⬆️ [12:27:26] Uploading: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:27:26,407 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:26,467 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:26,478 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f6f4a694_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:27:26,478 - INFO - Processing PDF from S3...
2025-09-24 12:27:26,478 - INFO - Downloading PDF from S3 to /tmp/tmpq4ezdwgi/f6f4a694_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:27:26,490 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:27:26,490 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:26,492 - INFO - Splitting PDF ddbd9863_NMFC_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 12:27:26,499 - INFO - Split PDF into 2 pages
2025-09-24 12:27:26,499 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:26,499 - INFO - Expected pages: [1, 2]
2025-09-24 12:27:26,813 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:27:26,814 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:26,817 - INFO - Splitting PDF 2ac889ed_NMFC_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 12:27:26,826 - INFO - Split PDF into 3 pages
2025-09-24 12:27:26,827 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:26,827 - INFO - Expected pages: [1, 2, 3]
2025-09-24 12:27:27,002 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/604b4630_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:27:27,002 - INFO - 🔍 [12:27:27] Starting classification: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:27:27,004 - INFO - ⬆️ [12:27:27] Uploading: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:27:27,009 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:27,048 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:27,055 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/604b4630_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:27:27,055 - INFO - Processing PDF from S3...
2025-09-24 12:27:27,055 - INFO - Downloading PDF from S3 to /tmp/tmp_w0qrhfz/604b4630_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:27:27,460 - INFO - Page 1: Extracted 589 characters, 36 lines from cf416886_I_QHD3LC0DU6S8O2YVVS60_9f7a82df_page_001.pdf
2025-09-24 12:27:27,460 - INFO - Successfully processed page 1
2025-09-24 12:27:27,461 - INFO - Combined 1 pages into final text
2025-09-24 12:27:27,461 - INFO - Text validation for cf416886_I_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 12:27:27,462 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:27,462 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:27,652 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/f3d671f4_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:27:27,652 - INFO - 🔍 [12:27:27] Starting classification: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:27:27,655 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:27,656 - INFO - ⬆️ [12:27:27] Uploading: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:27:27,719 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:27,726 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f3d671f4_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:27:27,726 - INFO - Processing PDF from S3...
2025-09-24 12:27:27,726 - INFO - Downloading PDF from S3 to /tmp/tmpseicvdk7/f3d671f4_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:27:28,299 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:27:28,299 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:28,301 - INFO - Splitting PDF 604b4630_NMFC_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 12:27:28,307 - INFO - Split PDF into 2 pages
2025-09-24 12:27:28,307 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:28,307 - INFO - Expected pages: [1, 2]
2025-09-24 12:27:28,333 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/f19b8a5a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:27:28,333 - INFO - 🔍 [12:27:28] Starting classification: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:27:28,334 - INFO - ⬆️ [12:27:28] Uploading: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:27:28,336 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:28,357 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:28,369 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f19b8a5a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:27:28,370 - INFO - Processing PDF from S3...
2025-09-24 12:27:28,370 - INFO - Downloading PDF from S3 to /tmp/tmpve98ah43/f19b8a5a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:27:28,960 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/fcb7d8da_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:27:28,960 - INFO - 🔍 [12:27:28] Starting classification: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:27:28,962 - INFO - ⬆️ [12:27:28] Uploading: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:27:28,964 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:28,979 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:28,989 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fcb7d8da_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:27:28,997 - INFO - Processing PDF from S3...
2025-09-24 12:27:28,997 - INFO - Downloading PDF from S3 to /tmp/tmp6ce7eak3/fcb7d8da_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:27:29,453 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 12:27:29,453 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:29,455 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 12:27:29,456 - INFO - Splitting PDF f6f4a694_NMFC_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 12:27:29,520 - INFO - Split PDF into 2 pages
2025-09-24 12:27:29,520 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:29,520 - INFO - Expected pages: [1, 2]
2025-09-24 12:27:29,597 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/4b7514d4_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:27:29,598 - INFO - 🔍 [12:27:29] Starting classification: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:27:29,600 - INFO - ⬆️ [12:27:29] Uploading: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:27:29,605 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:29,642 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:29,661 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4b7514d4_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:27:29,661 - INFO - Processing PDF from S3...
2025-09-24 12:27:29,661 - INFO - Downloading PDF from S3 to /tmp/tmpyfrfflt_/4b7514d4_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:27:29,738 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:27:29,738 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:29,740 - INFO - Splitting PDF f3d671f4_NMFC_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 12:27:29,744 - INFO - Split PDF into 1 pages
2025-09-24 12:27:29,745 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:29,745 - INFO - Expected pages: [1]
2025-09-24 12:27:29,758 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cf416886_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:27:30,221 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/a13a42aa_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:27:30,221 - INFO - 🔍 [12:27:30] Starting classification: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:27:30,222 - INFO - ⬆️ [12:27:30] Uploading: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:27:30,225 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:30,244 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:30,264 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a13a42aa_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:27:30,265 - INFO - Processing PDF from S3...
2025-09-24 12:27:30,265 - INFO - Downloading PDF from S3 to /tmp/tmp_2bo4l00/a13a42aa_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:27:30,339 - INFO - Page 2: Extracted 764 characters, 54 lines from ddbd9863_NMFC_DH0JZ2JWDGRHD26BX74C_06b5e13c_page_002.pdf
2025-09-24 12:27:30,340 - INFO - Successfully processed page 2
2025-09-24 12:27:30,759 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:27:30,760 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:30,764 - INFO - Splitting PDF fcb7d8da_W_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 12:27:30,768 - INFO - Split PDF into 1 pages
2025-09-24 12:27:30,769 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:30,769 - INFO - Expected pages: [1]
2025-09-24 12:27:30,819 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/55d247f5_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:27:30,820 - INFO - 🔍 [12:27:30] Starting classification: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:27:30,821 - INFO - ⬆️ [12:27:30] Uploading: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:27:30,826 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:30,861 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:30,872 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/55d247f5_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:27:30,872 - INFO - Processing PDF from S3...
2025-09-24 12:27:30,872 - INFO - Downloading PDF from S3 to /tmp/tmp53m5w3lh/55d247f5_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:27:31,002 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 12:27:31,002 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:31,004 - INFO - Splitting PDF f19b8a5a_W_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 12:27:31,007 - INFO - Split PDF into 1 pages
2025-09-24 12:27:31,007 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:31,008 - INFO - Expected pages: [1]
2025-09-24 12:27:31,144 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:27:31,144 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:31,150 - INFO - Splitting PDF 4b7514d4_W_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 12:27:31,160 - INFO - Split PDF into 1 pages
2025-09-24 12:27:31,160 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:31,161 - INFO - Expected pages: [1]
2025-09-24 12:27:31,430 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/b6a1e650_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:27:31,430 - INFO - 🔍 [12:27:31] Starting classification: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:27:31,431 - INFO - ⬆️ [12:27:31] Uploading: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:27:31,434 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:31,456 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:31,519 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b6a1e650_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:27:31,519 - INFO - Processing PDF from S3...
2025-09-24 12:27:31,519 - INFO - Downloading PDF from S3 to /tmp/tmpfq7h0i6z/b6a1e650_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:27:31,623 - INFO - Page 2: Extracted 850 characters, 59 lines from 2ac889ed_NMFC_NJ4WSZ8BUQAW48V6403N_3d3427c7_page_002.pdf
2025-09-24 12:27:31,623 - INFO - Successfully processed page 2
2025-09-24 12:27:31,674 - INFO - Page 3: Extracted 850 characters, 59 lines from 2ac889ed_NMFC_NJ4WSZ8BUQAW48V6403N_3d3427c7_page_003.pdf
2025-09-24 12:27:31,675 - INFO - Successfully processed page 3
2025-09-24 12:27:31,909 - INFO - Page 1: Extracted 854 characters, 69 lines from ddbd9863_NMFC_DH0JZ2JWDGRHD26BX74C_06b5e13c_page_001.pdf
2025-09-24 12:27:31,909 - INFO - Successfully processed page 1
2025-09-24 12:27:31,910 - INFO - Combined 2 pages into final text
2025-09-24 12:27:31,910 - INFO - Text validation for ddbd9863_NMFC_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 12:27:31,911 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:31,911 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:32,023 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/b170b02a_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:27:32,024 - INFO - 🔍 [12:27:32] Starting classification: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:27:32,031 - INFO - Initializing TextractProcessor...
2025-09-24 12:27:32,075 - INFO - Initializing BedrockProcessor...
2025-09-24 12:27:32,094 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b170b02a_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:27:32,094 - INFO - Processing PDF from S3...
2025-09-24 12:27:32,095 - INFO - Downloading PDF from S3 to /tmp/tmp4refsxex/b170b02a_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:27:32,176 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:27:32,177 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:32,183 - INFO - Splitting PDF a13a42aa_W_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 12:27:32,275 - INFO - Split PDF into 1 pages
2025-09-24 12:27:32,287 - INFO - Page 2: Extracted 913 characters, 56 lines from 604b4630_NMFC_R1V0MO844PBLWNEAUETU_a1f5f7ce_page_002.pdf
2025-09-24 12:27:32,293 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:32,302 - INFO - Successfully processed page 2
2025-09-24 12:27:32,302 - INFO - Expected pages: [1]
2025-09-24 12:27:32,310 - INFO - 

I_QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 12:27:32,310 - INFO - 

✓ Saved result: output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 12:27:32,378 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:27:32,378 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:32,386 - INFO - Splitting PDF 55d247f5_W_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 12:27:32,392 - INFO - Split PDF into 1 pages
2025-09-24 12:27:32,392 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:32,393 - INFO - Expected pages: [1]
2025-09-24 12:27:32,594 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cf416886_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:27:32,717 - INFO - Page 1: Extracted 980 characters, 76 lines from 2ac889ed_NMFC_NJ4WSZ8BUQAW48V6403N_3d3427c7_page_001.pdf
2025-09-24 12:27:32,717 - INFO - Successfully processed page 1
2025-09-24 12:27:32,718 - INFO - Combined 3 pages into final text
2025-09-24 12:27:32,719 - INFO - Text validation for 2ac889ed_NMFC_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 12:27:32,719 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:32,719 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:33,135 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 12:27:33,135 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:33,139 - INFO - Splitting PDF b6a1e650_W_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 12:27:33,142 - INFO - Split PDF into 1 pages
2025-09-24 12:27:33,143 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:33,143 - INFO - Expected pages: [1]
2025-09-24 12:27:33,322 - INFO - Page 1: Extracted 1511 characters, 86 lines from 604b4630_NMFC_R1V0MO844PBLWNEAUETU_a1f5f7ce_page_001.pdf
2025-09-24 12:27:33,323 - INFO - Successfully processed page 1
2025-09-24 12:27:33,323 - INFO - Combined 2 pages into final text
2025-09-24 12:27:33,323 - INFO - Text validation for 604b4630_NMFC_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 12:27:33,324 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:33,324 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:33,925 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 12:27:33,925 - INFO - Splitting PDF into individual pages...
2025-09-24 12:27:33,927 - INFO - Splitting PDF b170b02a_W_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 12:27:33,937 - INFO - Split PDF into 1 pages
2025-09-24 12:27:33,937 - INFO - Processing pages with Textract in parallel...
2025-09-24 12:27:33,937 - INFO - Expected pages: [1]
2025-09-24 12:27:34,229 - INFO - Page 1: Extracted 443 characters, 72 lines from f3d671f4_NMFC_RUDVGETVRZO7XX6YNW7I_7c77521b_page_001.pdf
2025-09-24 12:27:34,229 - INFO - Successfully processed page 1
2025-09-24 12:27:34,229 - INFO - Combined 1 pages into final text
2025-09-24 12:27:34,230 - INFO - Text validation for f3d671f4_NMFC_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 12:27:34,230 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:34,230 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:35,575 - INFO - Page 1: Extracted 626 characters, 49 lines from 4b7514d4_W_DFY1VDZWR7NBDLJV02G2_d4fc9973_page_001.pdf
2025-09-24 12:27:35,576 - INFO - Successfully processed page 1
2025-09-24 12:27:35,577 - INFO - Combined 1 pages into final text
2025-09-24 12:27:35,577 - INFO - Text validation for 4b7514d4_W_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 12:27:35,578 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:35,579 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:35,800 - INFO - Page 1: Extracted 732 characters, 59 lines from fcb7d8da_W_DCY7SLNMWUXIENOREHQF_851ed25d_page_001.pdf
2025-09-24 12:27:35,802 - INFO - Successfully processed page 1
2025-09-24 12:27:35,803 - INFO - Combined 1 pages into final text
2025-09-24 12:27:35,804 - INFO - Text validation for fcb7d8da_W_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 12:27:35,805 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:35,805 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:35,897 - INFO - Page 2: Extracted 540 characters, 29 lines from f6f4a694_NMFC_OR9EL08KIKNQPZ3UV3HH_6a76beff_page_002.pdf
2025-09-24 12:27:35,897 - INFO - Successfully processed page 2
2025-09-24 12:27:36,003 - INFO - Page 1: Extracted 1120 characters, 87 lines from f6f4a694_NMFC_OR9EL08KIKNQPZ3UV3HH_6a76beff_page_001.pdf
2025-09-24 12:27:36,003 - INFO - Successfully processed page 1
2025-09-24 12:27:36,004 - INFO - Combined 2 pages into final text
2025-09-24 12:27:36,004 - INFO - Text validation for f6f4a694_NMFC_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 12:27:36,004 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:36,005 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:36,158 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f3d671f4_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:27:36,303 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2ac889ed_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:27:36,437 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/604b4630_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:27:36,453 - INFO - Page 1: Extracted 802 characters, 30 lines from 55d247f5_W_K9VSARJOKAIZHNJ5RBDT_c5911795_page_001.pdf
2025-09-24 12:27:36,468 - INFO - 

NMFC_RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 12:27:36,471 - INFO - Successfully processed page 1
2025-09-24 12:27:36,471 - INFO - 

✓ Saved result: output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 12:27:36,476 - INFO - Combined 1 pages into final text
2025-09-24 12:27:36,476 - INFO - Text validation for 55d247f5_W_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 12:27:36,477 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:36,477 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:36,595 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ddbd9863_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:27:36,767 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f3d671f4_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:27:37,108 - INFO - Page 1: Extracted 517 characters, 31 lines from a13a42aa_W_HFPAXYL947DH59AB12FL_81e7df76_page_001.pdf
2025-09-24 12:27:37,118 - INFO - Successfully processed page 1
2025-09-24 12:27:37,128 - INFO - Combined 1 pages into final text
2025-09-24 12:27:37,138 - INFO - Text validation for a13a42aa_W_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 12:27:37,160 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:37,160 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:37,374 - INFO - Page 1: Extracted 580 characters, 48 lines from b6a1e650_W_WRKSHW76B3QUG47QWR75_f7ca748b_page_001.pdf
2025-09-24 12:27:37,387 - INFO - Successfully processed page 1
2025-09-24 12:27:37,405 - INFO - Combined 1 pages into final text
2025-09-24 12:27:37,411 - INFO - Text validation for b6a1e650_W_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 12:27:37,459 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:37,464 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:37,706 - INFO - 

NMFC_NJ4WSZ8BUQAW48V6403N.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 3,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:27:37,706 - INFO - 

✓ Saved result: output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:27:37,784 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fcb7d8da_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:27:37,991 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2ac889ed_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:27:38,341 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f6f4a694_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:27:38,628 - INFO - Page 1: Extracted 939 characters, 64 lines from f19b8a5a_W_A34CDFDJ66EDOZEKZWJL_6d7beaeb_page_001.pdf
2025-09-24 12:27:38,634 - INFO - Successfully processed page 1
2025-09-24 12:27:38,638 - INFO - Combined 1 pages into final text
2025-09-24 12:27:38,647 - INFO - Text validation for f19b8a5a_W_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 12:27:38,697 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:38,705 - INFO - Page 1: Extracted 528 characters, 31 lines from b170b02a_W_XCJLXZK140FUS8020ZAG_02b7df79_page_001.pdf
2025-09-24 12:27:38,710 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:38,720 - INFO - Successfully processed page 1
2025-09-24 12:27:38,751 - INFO - Combined 1 pages into final text
2025-09-24 12:27:38,765 - INFO - Text validation for b170b02a_W_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 12:27:38,830 - INFO - Analyzing document types with Bedrock...
2025-09-24 12:27:38,834 - INFO - 

NMFC_R1V0MO844PBLWNEAUETU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 12:27:38,834 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 12:27:38,834 - INFO - 

✓ Saved result: output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:27:39,123 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/604b4630_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:27:39,290 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/55d247f5_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:27:39,629 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b6a1e650_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:27:39,668 - INFO - 

NMFC_DH0JZ2JWDGRHD26BX74C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 12:27:39,668 - INFO - 

✓ Saved result: output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:27:39,992 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ddbd9863_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:27:39,996 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4b7514d4_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:27:40,275 - INFO - 

W_DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 12:27:40,275 - INFO - 

✓ Saved result: output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 12:27:40,369 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a13a42aa_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:27:40,571 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fcb7d8da_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:27:41,168 - INFO - 

NMFC_OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:27:41,168 - INFO - 

✓ Saved result: output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:27:41,459 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f6f4a694_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:27:41,513 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b170b02a_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:27:41,700 - INFO - 

W_K9VSARJOKAIZHNJ5RBDT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 12:27:41,701 - INFO - 

✓ Saved result: output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 12:27:41,996 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/55d247f5_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:27:42,045 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f19b8a5a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:27:42,234 - INFO - 

W_WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:27:42,234 - INFO - 

✓ Saved result: output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 12:27:42,524 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b6a1e650_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:27:42,828 - INFO - 

W_DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        },
        {
            "page_no": 2,
            "doc_type": "combined_carrier_documents"
        },
        {
            "page_no": 3,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-24 12:27:42,828 - INFO - 

✓ Saved result: output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:27:43,120 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4b7514d4_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:27:43,321 - INFO - 

W_HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:27:43,321 - INFO - 

✓ Saved result: output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 12:27:43,617 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a13a42aa_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:27:43,805 - INFO - 

W_XCJLXZK140FUS8020ZAG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 12:27:43,805 - INFO - 

✓ Saved result: output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 12:27:44,123 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b170b02a_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:27:44,483 - INFO - 

W_A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 12:27:44,484 - INFO - 

✓ Saved result: output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 12:27:44,808 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f19b8a5a_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:27:44,810 - INFO - 
📊 Processing Summary:
2025-09-24 12:27:44,810 - INFO -    Total files: 13
2025-09-24 12:27:44,810 - INFO -    Successful: 13
2025-09-24 12:27:44,810 - INFO -    Failed: 0
2025-09-24 12:27:44,811 - INFO -    Duration: 24.32 seconds
2025-09-24 12:27:44,811 - INFO -    Output directory: output
2025-09-24 12:27:44,811 - INFO - 
📋 Successfully Processed Files:
2025-09-24 12:27:44,811 - INFO -    📄 I_QHD3LC0DU6S8O2YVVS60.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 12:27:44,812 - INFO -    📄 NMFC_DH0JZ2JWDGRHD26BX74C.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 12:27:44,812 - INFO -    📄 NMFC_NJ4WSZ8BUQAW48V6403N.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"},{"page_no":3,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:27:44,812 - INFO -    📄 NMFC_OR9EL08KIKNQPZ3UV3HH.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:27:44,812 - INFO -    📄 NMFC_R1V0MO844PBLWNEAUETU.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 12:27:44,812 - INFO -    📄 NMFC_RUDVGETVRZO7XX6YNW7I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 12:27:44,813 - INFO -    📄 W_A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 12:27:44,813 - INFO -    📄 W_DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 12:27:44,813 - INFO -    📄 W_DFY1VDZWR7NBDLJV02G2.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"},{"page_no":2,"doc_type":"combined_carrier_documents"},{"page_no":3,"doc_type":"combined_carrier_documents"}]}
2025-09-24 12:27:44,813 - INFO -    📄 W_HFPAXYL947DH59AB12FL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:27:44,813 - INFO -    📄 W_K9VSARJOKAIZHNJ5RBDT.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 12:27:44,813 - INFO -    📄 W_WRKSHW76B3QUG47QWR75.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:27:44,814 - INFO -    📄 W_XCJLXZK140FUS8020ZAG.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 12:27:44,815 - INFO - 
============================================================================================================================================
2025-09-24 12:27:44,815 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 12:27:44,815 - INFO - ============================================================================================================================================
2025-09-24 12:27:44,815 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 12:27:44,815 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 12:27:44,816 - INFO - I_QHD3LC0DU6S8O2YVVS60.pdf                         1      other                run1_I_QHD3LC0DU6S8O2YVVS60.json                  
2025-09-24 12:27:44,816 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 12:27:44,816 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 12:27:44,816 - INFO - 
2025-09-24 12:27:44,816 - INFO - NMFC_DH0JZ2JWDGRHD26BX74C.pdf                      1      other                run1_NMFC_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 12:27:44,816 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:27:44,816 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:27:44,816 - INFO - 
2025-09-24 12:27:44,816 - INFO - NMFC_DH0JZ2JWDGRHD26BX74C.pdf                      2      other                run1_NMFC_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 12:27:44,817 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 12:27:44,817 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:27:44,817 - INFO - 
2025-09-24 12:27:44,817 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      1      weight_and_inspect... run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 12:27:44,817 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:27:44,817 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:27:44,817 - INFO - 
2025-09-24 12:27:44,817 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      2      weight_and_inspect... run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 12:27:44,817 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:27:44,817 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:27:44,818 - INFO - 
2025-09-24 12:27:44,818 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      3      weight_and_inspect... run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 12:27:44,818 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 12:27:44,818 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:27:44,818 - INFO - 
2025-09-24 12:27:44,818 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      1      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 12:27:44,818 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:27:44,818 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:27:44,818 - INFO - 
2025-09-24 12:27:44,818 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      2      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 12:27:44,819 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 12:27:44,819 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:27:44,819 - INFO - 
2025-09-24 12:27:44,819 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      1      other                run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 12:27:44,819 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:27:44,819 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:27:44,819 - INFO - 
2025-09-24 12:27:44,819 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      2      other                run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 12:27:44,819 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 12:27:44,819 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:27:44,820 - INFO - 
2025-09-24 12:27:44,820 - INFO - NMFC_RUDVGETVRZO7XX6YNW7I.pdf                      1      nmfc_cert            run1_NMFC_RUDVGETVRZO7XX6YNW7I.json               
2025-09-24 12:27:44,820 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 12:27:44,820 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 12:27:44,820 - INFO - 
2025-09-24 12:27:44,820 - INFO - W_A34CDFDJ66EDOZEKZWJL.pdf                         1      other                run1_W_A34CDFDJ66EDOZEKZWJL.json                  
2025-09-24 12:27:44,820 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 12:27:44,820 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 12:27:44,820 - INFO - 
2025-09-24 12:27:44,820 - INFO - W_DCY7SLNMWUXIENOREHQF.pdf                         1      scale_ticket         run1_W_DCY7SLNMWUXIENOREHQF.json                  
2025-09-24 12:27:44,821 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 12:27:44,821 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 12:27:44,821 - INFO - 
2025-09-24 12:27:44,821 - INFO - W_DFY1VDZWR7NBDLJV02G2.pdf                         1      combined_carrier_d... run1_W_DFY1VDZWR7NBDLJV02G2.json                  
2025-09-24 12:27:44,821 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:27:44,821 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:27:44,821 - INFO - 
2025-09-24 12:27:44,821 - INFO - W_DFY1VDZWR7NBDLJV02G2.pdf                         2      combined_carrier_d... run1_W_DFY1VDZWR7NBDLJV02G2.json                  
2025-09-24 12:27:44,821 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:27:44,821 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:27:44,822 - INFO - 
2025-09-24 12:27:44,822 - INFO - W_DFY1VDZWR7NBDLJV02G2.pdf                         3      combined_carrier_d... run1_W_DFY1VDZWR7NBDLJV02G2.json                  
2025-09-24 12:27:44,822 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 12:27:44,822 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:27:44,822 - INFO - 
2025-09-24 12:27:44,822 - INFO - W_HFPAXYL947DH59AB12FL.pdf                         1      weight_and_inspect... run1_W_HFPAXYL947DH59AB12FL.json                  
2025-09-24 12:27:44,822 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 12:27:44,822 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 12:27:44,822 - INFO - 
2025-09-24 12:27:44,822 - INFO - W_K9VSARJOKAIZHNJ5RBDT.pdf                         1      scale_ticket         run1_W_K9VSARJOKAIZHNJ5RBDT.json                  
2025-09-24 12:27:44,823 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 12:27:44,823 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 12:27:44,823 - INFO - 
2025-09-24 12:27:44,823 - INFO - W_WRKSHW76B3QUG47QWR75.pdf                         1      weight_and_inspect... run1_W_WRKSHW76B3QUG47QWR75.json                  
2025-09-24 12:27:44,823 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 12:27:44,823 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 12:27:44,823 - INFO - 
2025-09-24 12:27:44,823 - INFO - W_XCJLXZK140FUS8020ZAG.pdf                         1      weight_and_inspect... run1_W_XCJLXZK140FUS8020ZAG.json                  
2025-09-24 12:27:44,823 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 12:27:44,823 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 12:27:44,823 - INFO - 
2025-09-24 12:27:44,824 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 12:27:44,824 - INFO - Total entries: 20
2025-09-24 12:27:44,824 - INFO - ============================================================================================================================================
2025-09-24 12:27:44,824 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 12:27:44,824 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 12:27:44,824 - INFO -   1. I_QHD3LC0DU6S8O2YVVS60.pdf          Page 1   → other           | run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 12:27:44,824 - INFO -   2. NMFC_DH0JZ2JWDGRHD26BX74C.pdf       Page 1   → other           | run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:27:44,824 - INFO -   3. NMFC_DH0JZ2JWDGRHD26BX74C.pdf       Page 2   → other           | run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 12:27:44,825 - INFO -   4. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:27:44,825 - INFO -   5. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:27:44,825 - INFO -   6. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 3   → weight_and_inspection_cert | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 12:27:44,825 - INFO -   7. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:27:44,825 - INFO -   8. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 12:27:44,825 - INFO -   9. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 1   → other           | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:27:44,825 - INFO -  10. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 2   → other           | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 12:27:44,825 - INFO -  11. NMFC_RUDVGETVRZO7XX6YNW7I.pdf       Page 1   → nmfc_cert       | run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 12:27:44,826 - INFO -  12. W_A34CDFDJ66EDOZEKZWJL.pdf          Page 1   → other           | run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 12:27:44,826 - INFO -  13. W_DCY7SLNMWUXIENOREHQF.pdf          Page 1   → scale_ticket    | run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 12:27:44,827 - INFO -  14. W_DFY1VDZWR7NBDLJV02G2.pdf          Page 1   → combined_carrier_documents | run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:27:44,827 - INFO -  15. W_DFY1VDZWR7NBDLJV02G2.pdf          Page 2   → combined_carrier_documents | run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:27:44,827 - INFO -  16. W_DFY1VDZWR7NBDLJV02G2.pdf          Page 3   → combined_carrier_documents | run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 12:27:44,827 - INFO -  17. W_HFPAXYL947DH59AB12FL.pdf          Page 1   → weight_and_inspection_cert | run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 12:27:44,828 - INFO -  18. W_K9VSARJOKAIZHNJ5RBDT.pdf          Page 1   → scale_ticket    | run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 12:27:44,828 - INFO -  19. W_WRKSHW76B3QUG47QWR75.pdf          Page 1   → weight_and_inspection_cert | run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 12:27:44,828 - INFO -  20. W_XCJLXZK140FUS8020ZAG.pdf          Page 1   → weight_and_inspection_cert | run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 12:27:44,828 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 12:27:44,829 - INFO - 
✅ Test completed: {'total_files': 13, 'processed': 13, 'failed': 0, 'errors': [], 'duration_seconds': 24.316415, 'processed_files': [{'filename': 'I_QHD3LC0DU6S8O2YVVS60.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf'}, {'filename': 'NMFC_DH0JZ2JWDGRHD26BX74C.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf'}, {'filename': 'NMFC_NJ4WSZ8BUQAW48V6403N.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 3, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf'}, {'filename': 'NMFC_OR9EL08KIKNQPZ3UV3HH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf'}, {'filename': 'NMFC_R1V0MO844PBLWNEAUETU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf'}, {'filename': 'NMFC_RUDVGETVRZO7XX6YNW7I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf'}, {'filename': 'W_A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf'}, {'filename': 'W_DCY7SLNMWUXIENOREHQF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf'}, {'filename': 'W_DFY1VDZWR7NBDLJV02G2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}, {'page_no': 2, 'doc_type': 'combined_carrier_documents'}, {'page_no': 3, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf'}, {'filename': 'W_HFPAXYL947DH59AB12FL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf'}, {'filename': 'W_K9VSARJOKAIZHNJ5RBDT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf'}, {'filename': 'W_WRKSHW76B3QUG47QWR75.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf'}, {'filename': 'W_XCJLXZK140FUS8020ZAG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf'}]}
