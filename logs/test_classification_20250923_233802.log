2025-09-23 23:38:02,404 - INFO - Logging initialized. Log file: logs/test_classification_20250923_233802.log
2025-09-23 23:38:02,404 - INFO - 📁 Found 8 files to process
2025-09-23 23:38:02,405 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:38:02,405 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:38:02,405 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:38:02,405 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:38:02,405 - INFO - ⬆️ [23:38:02] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:38:04,098 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/bf8c2048_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:38:04,098 - INFO - 🔍 [23:38:04] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:38:04,099 - INFO - ⬆️ [23:38:04] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:38:04,101 - INFO - Initializing TextractProcessor...
2025-09-23 23:38:04,124 - INFO - Initializing BedrockProcessor...
2025-09-23 23:38:04,128 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bf8c2048_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:38:04,128 - INFO - Processing PDF from S3...
2025-09-23 23:38:04,129 - INFO - Downloading PDF from S3 to /tmp/tmpdiql90wh/bf8c2048_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:38:05,734 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:38:05,735 - INFO - Splitting PDF into individual pages...
2025-09-23 23:38:05,736 - INFO - Splitting PDF bf8c2048_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:38:05,743 - INFO - Split PDF into 1 pages
2025-09-23 23:38:05,743 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:38:05,744 - INFO - Expected pages: [1]
2025-09-23 23:38:09,115 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/36e92fbc_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:38:09,115 - INFO - 🔍 [23:38:09] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:38:09,116 - INFO - ⬆️ [23:38:09] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:38:09,118 - INFO - Initializing TextractProcessor...
2025-09-23 23:38:09,132 - INFO - Initializing BedrockProcessor...
2025-09-23 23:38:09,136 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/36e92fbc_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:38:09,136 - INFO - Processing image from S3...
2025-09-23 23:38:09,567 - INFO - Page 1: Extracted 1260 characters, 84 lines from bf8c2048_BQJUG5URFR2GH9ECWFV4_74f27a6e_page_001.pdf
2025-09-23 23:38:09,567 - INFO - Successfully processed page 1
2025-09-23 23:38:09,567 - INFO - Combined 1 pages into final text
2025-09-23 23:38:09,567 - INFO - Text validation for bf8c2048_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:38:09,568 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:38:09,568 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:38:10,139 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/d45e5b1d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:38:10,139 - INFO - 🔍 [23:38:10] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:38:10,140 - INFO - ⬆️ [23:38:10] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:38:10,142 - INFO - Initializing TextractProcessor...
2025-09-23 23:38:10,166 - INFO - Initializing BedrockProcessor...
2025-09-23 23:38:10,170 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d45e5b1d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:38:10,171 - INFO - Processing PDF from S3...
2025-09-23 23:38:10,171 - INFO - Downloading PDF from S3 to /tmp/tmpv8a74xvz/d45e5b1d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:38:10,830 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/cdedeb1f_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:38:10,831 - INFO - 🔍 [23:38:10] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:38:10,831 - INFO - ⬆️ [23:38:10] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:38:10,832 - INFO - Initializing TextractProcessor...
2025-09-23 23:38:10,849 - INFO - Initializing BedrockProcessor...
2025-09-23 23:38:10,852 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cdedeb1f_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:38:10,852 - INFO - Processing PDF from S3...
2025-09-23 23:38:10,852 - INFO - Downloading PDF from S3 to /tmp/tmpf7e_zgfy/cdedeb1f_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:38:11,542 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bf8c2048_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:38:12,616 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:38:12,617 - INFO - Splitting PDF into individual pages...
2025-09-23 23:38:12,619 - INFO - Splitting PDF d45e5b1d_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:38:12,624 - INFO - Split PDF into 1 pages
2025-09-23 23:38:12,624 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:38:12,624 - INFO - Expected pages: [1]
2025-09-23 23:38:12,665 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:38:12,667 - INFO - Splitting PDF into individual pages...
2025-09-23 23:38:12,670 - INFO - Splitting PDF cdedeb1f_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:38:12,689 - INFO - Split PDF into 7 pages
2025-09-23 23:38:12,690 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:38:12,690 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:38:12,840 - INFO - S3 Image temp/36e92fbc_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:38:12,840 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:38:12,840 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:38:15,769 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/78f19de4_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:38:15,770 - INFO - 🔍 [23:38:15] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:38:15,771 - INFO - ⬆️ [23:38:15] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:38:15,773 - INFO - Initializing TextractProcessor...
2025-09-23 23:38:15,789 - INFO - Initializing BedrockProcessor...
2025-09-23 23:38:15,793 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/78f19de4_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:38:15,793 - INFO - Processing image from S3...
2025-09-23 23:38:16,430 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/36e92fbc_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:38:16,896 - INFO - Page 2: Extracted 1821 characters, 105 lines from cdedeb1f_O2IU5G77LYNTYE0RP1TI_76f3bb70_page_002.pdf
2025-09-23 23:38:16,896 - INFO - Successfully processed page 2
2025-09-23 23:38:17,449 - INFO - Page 1: Extracted 1731 characters, 110 lines from cdedeb1f_O2IU5G77LYNTYE0RP1TI_76f3bb70_page_001.pdf
2025-09-23 23:38:17,460 - INFO - Successfully processed page 1
2025-09-23 23:38:17,468 - INFO - Page 4: Extracted 2242 characters, 148 lines from cdedeb1f_O2IU5G77LYNTYE0RP1TI_76f3bb70_page_004.pdf
2025-09-23 23:38:17,478 - INFO - Successfully processed page 4
2025-09-23 23:38:17,480 - INFO - Page 5: Extracted 2059 characters, 131 lines from cdedeb1f_O2IU5G77LYNTYE0RP1TI_76f3bb70_page_005.pdf
2025-09-23 23:38:17,480 - INFO - Successfully processed page 5
2025-09-23 23:38:17,507 - INFO - Page 6: Extracted 1973 characters, 129 lines from cdedeb1f_O2IU5G77LYNTYE0RP1TI_76f3bb70_page_006.pdf
2025-09-23 23:38:17,513 - INFO - Successfully processed page 6
2025-09-23 23:38:17,523 - INFO - Page 3: Extracted 2265 characters, 147 lines from cdedeb1f_O2IU5G77LYNTYE0RP1TI_76f3bb70_page_003.pdf
2025-09-23 23:38:17,523 - INFO - Successfully processed page 3
2025-09-23 23:38:17,531 - INFO - Page 1: Extracted 519 characters, 34 lines from d45e5b1d_KE7TCH9TPQZFVA5CZ3HT_ca7ed2f7_page_001.pdf
2025-09-23 23:38:17,531 - INFO - Successfully processed page 1
2025-09-23 23:38:17,532 - INFO - Combined 1 pages into final text
2025-09-23 23:38:17,532 - INFO - Text validation for d45e5b1d_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:38:17,533 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:38:17,534 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:38:17,915 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/9001da72_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:38:17,916 - INFO - 🔍 [23:38:17] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:38:17,917 - INFO - ⬆️ [23:38:17] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:38:17,918 - INFO - Initializing TextractProcessor...
2025-09-23 23:38:17,935 - INFO - Initializing BedrockProcessor...
2025-09-23 23:38:17,937 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9001da72_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:38:17,937 - INFO - Processing image from S3...
2025-09-23 23:38:18,545 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/abe7ac61_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:38:18,546 - INFO - 🔍 [23:38:18] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:38:18,547 - INFO - ⬆️ [23:38:18] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:38:18,548 - INFO - Initializing TextractProcessor...
2025-09-23 23:38:18,570 - INFO - Initializing BedrockProcessor...
2025-09-23 23:38:18,574 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/abe7ac61_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:38:18,575 - INFO - Processing image from S3...
2025-09-23 23:38:19,579 - INFO - Page 7: Extracted 417 characters, 27 lines from cdedeb1f_O2IU5G77LYNTYE0RP1TI_76f3bb70_page_007.pdf
2025-09-23 23:38:19,580 - INFO - Successfully processed page 7
2025-09-23 23:38:19,580 - INFO - Combined 7 pages into final text
2025-09-23 23:38:19,581 - INFO - Text validation for cdedeb1f_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:38:19,581 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:38:19,582 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:38:19,762 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/403d8f4f_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:38:19,763 - INFO - 🔍 [23:38:19] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:38:19,765 - INFO - Initializing TextractProcessor...
2025-09-23 23:38:19,778 - INFO - Initializing BedrockProcessor...
2025-09-23 23:38:19,785 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/403d8f4f_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:38:19,787 - INFO - Processing image from S3...
2025-09-23 23:38:19,812 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:38:19,812 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:38:20,124 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bf8c2048_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:38:20,128 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d45e5b1d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:38:20,138 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:38:20,138 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:38:20,445 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/36e92fbc_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:38:20,458 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:38:20,458 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:38:20,503 - INFO - S3 Image temp/abe7ac61_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:38:20,504 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:38:20,504 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:38:20,747 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d45e5b1d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:38:21,779 - INFO - S3 Image temp/9001da72_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:38:21,779 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:38:21,779 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:38:21,836 - INFO - S3 Image temp/403d8f4f_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:38:21,836 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:38:21,837 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:38:21,878 - INFO - S3 Image temp/78f19de4_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:38:21,878 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:38:21,878 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:38:22,444 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cdedeb1f_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:38:22,593 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:38:22,593 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:38:22,908 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cdedeb1f_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:38:23,399 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/abe7ac61_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:38:23,402 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:38:23,402 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:38:23,700 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/abe7ac61_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:38:23,816 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/78f19de4_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:38:23,870 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:38:23,870 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
