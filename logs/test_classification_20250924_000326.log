2025-09-24 00:03:26,196 - INFO - Logging initialized. Log file: logs/test_classification_20250924_000326.log
2025-09-24 00:03:26,196 - INFO - 📁 Found 8 files to process
2025-09-24 00:03:26,196 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 00:03:26,196 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-24 00:03:26,196 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-24 00:03:26,196 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-24 00:03:26,197 - INFO - ⬆️ [00:03:26] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:03:27,897 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/bf125d2e_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:03:27,897 - INFO - 🔍 [00:03:27] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:03:27,898 - INFO - ⬆️ [00:03:27] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:03:27,900 - INFO - Initializing TextractProcessor...
2025-09-24 00:03:27,924 - INFO - Initializing BedrockProcessor...
2025-09-24 00:03:27,936 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bf125d2e_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:03:27,936 - INFO - Processing PDF from S3...
2025-09-24 00:03:27,937 - INFO - Downloading PDF from S3 to /tmp/tmpeckivolr/bf125d2e_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:03:29,361 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:03:29,361 - INFO - Splitting PDF into individual pages...
2025-09-24 00:03:29,362 - INFO - Splitting PDF bf125d2e_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-24 00:03:29,366 - INFO - Split PDF into 1 pages
2025-09-24 00:03:29,366 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:03:29,366 - INFO - Expected pages: [1]
2025-09-24 00:03:32,409 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/20d41c24_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:03:32,409 - INFO - 🔍 [00:03:32] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:03:32,410 - INFO - ⬆️ [00:03:32] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:03:32,410 - INFO - Initializing TextractProcessor...
2025-09-24 00:03:32,426 - INFO - Initializing BedrockProcessor...
2025-09-24 00:03:32,431 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/20d41c24_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:03:32,432 - INFO - Processing image from S3...
2025-09-24 00:03:33,326 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/44e99cf7_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:03:33,326 - INFO - 🔍 [00:03:33] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:03:33,327 - INFO - ⬆️ [00:03:33] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:33,328 - INFO - Initializing TextractProcessor...
2025-09-24 00:03:33,338 - INFO - Initializing BedrockProcessor...
2025-09-24 00:03:33,342 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/44e99cf7_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:03:33,342 - INFO - Processing PDF from S3...
2025-09-24 00:03:33,342 - INFO - Downloading PDF from S3 to /tmp/tmp_aaieb6l/44e99cf7_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:03:34,061 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/ec1a505b_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:34,061 - INFO - 🔍 [00:03:34] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:34,062 - INFO - ⬆️ [00:03:34] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:03:34,062 - INFO - Initializing TextractProcessor...
2025-09-24 00:03:34,070 - INFO - Initializing BedrockProcessor...
2025-09-24 00:03:34,077 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ec1a505b_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:34,078 - INFO - Processing PDF from S3...
2025-09-24 00:03:34,078 - INFO - Downloading PDF from S3 to /tmp/tmpe81aajbz/ec1a505b_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:34,359 - INFO - Page 1: Extracted 1260 characters, 84 lines from bf125d2e_BQJUG5URFR2GH9ECWFV4_a999bcf2_page_001.pdf
2025-09-24 00:03:34,359 - INFO - Successfully processed page 1
2025-09-24 00:03:34,359 - INFO - Combined 1 pages into final text
2025-09-24 00:03:34,359 - INFO - Text validation for bf125d2e_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-24 00:03:34,364 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:03:34,365 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:03:35,558 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 00:03:35,558 - INFO - Splitting PDF into individual pages...
2025-09-24 00:03:35,560 - INFO - Splitting PDF 44e99cf7_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-24 00:03:35,564 - INFO - Split PDF into 1 pages
2025-09-24 00:03:35,564 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:03:35,564 - INFO - Expected pages: [1]
2025-09-24 00:03:35,819 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:03:35,820 - INFO - Splitting PDF into individual pages...
2025-09-24 00:03:35,822 - INFO - Splitting PDF ec1a505b_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-24 00:03:35,833 - INFO - Split PDF into 7 pages
2025-09-24 00:03:35,833 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:03:35,834 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 00:03:36,243 - INFO - S3 Image temp/20d41c24_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-24 00:03:36,243 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:03:36,243 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:03:36,347 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bf125d2e_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:03:38,261 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/be0630a7_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:03:38,262 - INFO - 🔍 [00:03:38] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:03:38,263 - INFO - ⬆️ [00:03:38] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:03:38,263 - INFO - Initializing TextractProcessor...
2025-09-24 00:03:38,285 - INFO - Initializing BedrockProcessor...
2025-09-24 00:03:38,289 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/be0630a7_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:03:38,289 - INFO - Processing image from S3...
2025-09-24 00:03:40,307 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/a9fee930_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:03:40,307 - INFO - 🔍 [00:03:40] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:03:40,308 - INFO - ⬆️ [00:03:40] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:03:40,309 - INFO - Initializing TextractProcessor...
2025-09-24 00:03:40,326 - INFO - Initializing BedrockProcessor...
2025-09-24 00:03:40,333 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a9fee930_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:03:40,334 - INFO - Processing image from S3...
2025-09-24 00:03:40,456 - INFO - Page 1: Extracted 1731 characters, 110 lines from ec1a505b_O2IU5G77LYNTYE0RP1TI_199f09bf_page_001.pdf
2025-09-24 00:03:40,457 - INFO - Successfully processed page 1
2025-09-24 00:03:40,679 - INFO - Page 3: Extracted 2265 characters, 147 lines from ec1a505b_O2IU5G77LYNTYE0RP1TI_199f09bf_page_003.pdf
2025-09-24 00:03:40,679 - INFO - Successfully processed page 3
2025-09-24 00:03:40,721 - INFO - Page 2: Extracted 1821 characters, 105 lines from ec1a505b_O2IU5G77LYNTYE0RP1TI_199f09bf_page_002.pdf
2025-09-24 00:03:40,721 - INFO - Successfully processed page 2
2025-09-24 00:03:40,825 - INFO - Page 1: Extracted 519 characters, 34 lines from 44e99cf7_KE7TCH9TPQZFVA5CZ3HT_bd3273a0_page_001.pdf
2025-09-24 00:03:40,825 - INFO - Successfully processed page 1
2025-09-24 00:03:40,826 - INFO - Combined 1 pages into final text
2025-09-24 00:03:40,826 - INFO - Text validation for 44e99cf7_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-24 00:03:40,826 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:03:40,826 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:03:40,850 - INFO - Page 5: Extracted 2059 characters, 131 lines from ec1a505b_O2IU5G77LYNTYE0RP1TI_199f09bf_page_005.pdf
2025-09-24 00:03:40,850 - INFO - Successfully processed page 5
2025-09-24 00:03:40,955 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/20d41c24_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:03:40,965 - INFO - Page 4: Extracted 2242 characters, 148 lines from ec1a505b_O2IU5G77LYNTYE0RP1TI_199f09bf_page_004.pdf
2025-09-24 00:03:40,966 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/790f1b56_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:03:40,966 - INFO - Successfully processed page 4
2025-09-24 00:03:40,966 - INFO - 🔍 [00:03:40] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:03:40,967 - INFO - ⬆️ [00:03:40] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:03:40,967 - INFO - Initializing TextractProcessor...
2025-09-24 00:03:40,982 - INFO - Initializing BedrockProcessor...
2025-09-24 00:03:40,986 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/790f1b56_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:03:40,986 - INFO - Processing image from S3...
2025-09-24 00:03:41,009 - INFO - Page 6: Extracted 1973 characters, 129 lines from ec1a505b_O2IU5G77LYNTYE0RP1TI_199f09bf_page_006.pdf
2025-09-24 00:03:41,009 - INFO - Successfully processed page 6
2025-09-24 00:03:42,152 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/94dc39ca_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:03:42,152 - INFO - 🔍 [00:03:42] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:03:42,154 - INFO - Initializing TextractProcessor...
2025-09-24 00:03:42,166 - INFO - Initializing BedrockProcessor...
2025-09-24 00:03:42,171 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/94dc39ca_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:03:42,174 - INFO - Processing image from S3...
2025-09-24 00:03:42,198 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:03:42,198 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:03:42,484 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bf125d2e_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:03:42,497 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 00:03:42,497 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:03:42,779 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/20d41c24_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:03:42,990 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/44e99cf7_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:03:43,008 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:03:43,008 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:03:43,066 - INFO - Page 7: Extracted 417 characters, 27 lines from ec1a505b_O2IU5G77LYNTYE0RP1TI_199f09bf_page_007.pdf
2025-09-24 00:03:43,067 - INFO - Successfully processed page 7
2025-09-24 00:03:43,067 - INFO - Combined 7 pages into final text
2025-09-24 00:03:43,068 - INFO - Text validation for ec1a505b_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-24 00:03:43,069 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:03:43,069 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:03:43,193 - INFO - S3 Image temp/790f1b56_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-24 00:03:43,193 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:03:43,193 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:03:43,304 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/44e99cf7_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:03:44,692 - INFO - S3 Image temp/be0630a7_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-24 00:03:44,692 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:03:44,692 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:03:44,838 - INFO - S3 Image temp/a9fee930_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-24 00:03:44,838 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:03:44,838 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:03:45,074 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/790f1b56_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:03:45,080 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:03:45,080 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:03:45,245 - INFO - S3 Image temp/94dc39ca_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-24 00:03:45,245 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:03:45,245 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:03:45,364 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/790f1b56_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:03:45,557 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ec1a505b_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:45,685 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:03:45,685 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:45,992 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ec1a505b_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:46,863 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/be0630a7_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:03:46,919 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:03:46,919 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:03:47,228 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/be0630a7_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:03:47,583 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a9fee930_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:03:47,602 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 00:03:47,602 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:03:47,890 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a9fee930_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:03:49,210 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/94dc39ca_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:03:49,216 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 00:03:49,217 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:03:49,523 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/94dc39ca_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:03:49,524 - INFO - 
📊 Processing Summary:
2025-09-24 00:03:49,524 - INFO -    Total files: 8
2025-09-24 00:03:49,524 - INFO -    Successful: 8
2025-09-24 00:03:49,524 - INFO -    Failed: 0
2025-09-24 00:03:49,524 - INFO -    Duration: 23.33 seconds
2025-09-24 00:03:49,524 - INFO -    Output directory: output
2025-09-24 00:03:49,524 - INFO - 
📋 Successfully Processed Files:
2025-09-24 00:03:49,524 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:03:49,524 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 00:03:49,525 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:03:49,525 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-24 00:03:49,525 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:03:49,525 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 00:03:49,525 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:03:49,525 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 00:03:49,526 - INFO - 
============================================================================================================================================
2025-09-24 00:03:49,526 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 00:03:49,526 - INFO - ============================================================================================================================================
2025-09-24 00:03:49,526 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 00:03:49,526 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:03:49,526 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-24 00:03:49,526 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:03:49,526 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:03:49,526 - INFO - 
2025-09-24 00:03:49,526 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      other                run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-24 00:03:49,527 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:03:49,527 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:03:49,527 - INFO - 
2025-09-24 00:03:49,527 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      log                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-24 00:03:49,527 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:03:49,527 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:03:49,527 - INFO - 
2025-09-24 00:03:49,527 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:03:49,527 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:49,527 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,527 - INFO - 
2025-09-24 00:03:49,527 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:03:49,527 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:49,527 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,528 - INFO - 
2025-09-24 00:03:49,528 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:03:49,528 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:49,528 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,528 - INFO - 
2025-09-24 00:03:49,528 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:03:49,528 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:49,528 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,528 - INFO - 
2025-09-24 00:03:49,528 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:03:49,528 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:49,528 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,528 - INFO - 
2025-09-24 00:03:49,528 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:03:49,529 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:49,529 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,529 - INFO - 
2025-09-24 00:03:49,529 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:03:49,529 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:03:49,529 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,529 - INFO - 
2025-09-24 00:03:49,529 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-24 00:03:49,530 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:03:49,530 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:03:49,530 - INFO - 
2025-09-24 00:03:49,530 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      other                run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-24 00:03:49,530 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:03:49,530 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:03:49,530 - INFO - 
2025-09-24 00:03:49,530 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-24 00:03:49,530 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:03:49,530 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:03:49,530 - INFO - 
2025-09-24 00:03:49,531 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-24 00:03:49,531 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:03:49,531 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:03:49,531 - INFO - 
2025-09-24 00:03:49,531 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:03:49,531 - INFO - Total entries: 14
2025-09-24 00:03:49,531 - INFO - ============================================================================================================================================
2025-09-24 00:03:49,531 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 00:03:49,531 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:03:49,531 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:03:49,531 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → other           | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:03:49,531 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → log             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:03:49,532 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,532 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,532 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,532 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,532 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,532 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,532 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:03:49,532 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:03:49,532 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → other           | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:03:49,532 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:03:49,533 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:03:49,533 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:03:49,533 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 23.327463, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
