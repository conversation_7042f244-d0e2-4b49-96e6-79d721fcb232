2025-09-23 23:55:02,284 - INFO - Logging initialized. Log file: logs/test_classification_20250923_235502.log
2025-09-23 23:55:02,284 - INFO - 📁 Found 8 files to process
2025-09-23 23:55:02,284 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:55:02,284 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:55:02,284 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:55:02,284 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:55:02,284 - INFO - ⬆️ [23:55:02] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:03,745 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/5038bbdb_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:03,745 - INFO - 🔍 [23:55:03] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:03,746 - INFO - ⬆️ [23:55:03] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:03,747 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:03,769 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:03,774 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5038bbdb_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:03,774 - INFO - Processing PDF from S3...
2025-09-23 23:55:03,775 - INFO - Downloading PDF from S3 to /tmp/tmp_6a9y1zb/5038bbdb_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:05,152 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:55:05,152 - INFO - Splitting PDF into individual pages...
2025-09-23 23:55:05,153 - INFO - Splitting PDF 5038bbdb_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:55:05,157 - INFO - Split PDF into 1 pages
2025-09-23 23:55:05,158 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:55:05,158 - INFO - Expected pages: [1]
2025-09-23 23:55:06,479 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/15a94906_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:06,480 - INFO - 🔍 [23:55:06] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:06,481 - INFO - ⬆️ [23:55:06] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:06,483 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:06,500 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:06,505 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/15a94906_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:06,505 - INFO - Processing image from S3...
2025-09-23 23:55:07,193 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/b44ea181_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:07,193 - INFO - 🔍 [23:55:07] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:07,194 - INFO - ⬆️ [23:55:07] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:07,195 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:07,213 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:07,218 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b44ea181_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:07,218 - INFO - Processing PDF from S3...
2025-09-23 23:55:07,219 - INFO - Downloading PDF from S3 to /tmp/tmps6fpbmey/b44ea181_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:07,809 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/6ffb2be6_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:07,810 - INFO - 🔍 [23:55:07] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:07,810 - INFO - ⬆️ [23:55:07] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:07,811 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:07,836 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:07,840 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6ffb2be6_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:07,841 - INFO - Processing PDF from S3...
2025-09-23 23:55:07,841 - INFO - Downloading PDF from S3 to /tmp/tmp5w93vqb4/6ffb2be6_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:09,075 - INFO - Page 1: Extracted 1260 characters, 84 lines from 5038bbdb_BQJUG5URFR2GH9ECWFV4_b65a1386_page_001.pdf
2025-09-23 23:55:09,075 - INFO - Successfully processed page 1
2025-09-23 23:55:09,078 - INFO - Combined 1 pages into final text
2025-09-23 23:55:09,078 - INFO - Text validation for 5038bbdb_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:55:09,078 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:09,078 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:09,364 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:55:09,365 - INFO - Splitting PDF into individual pages...
2025-09-23 23:55:09,366 - INFO - Splitting PDF b44ea181_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:55:09,370 - INFO - Split PDF into 1 pages
2025-09-23 23:55:09,370 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:55:09,370 - INFO - Expected pages: [1]
2025-09-23 23:55:09,713 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:55:09,713 - INFO - Splitting PDF into individual pages...
2025-09-23 23:55:09,716 - INFO - Splitting PDF 6ffb2be6_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:55:09,725 - INFO - Split PDF into 7 pages
2025-09-23 23:55:09,726 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:55:09,726 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:55:10,062 - INFO - S3 Image temp/15a94906_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:55:10,062 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:10,062 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:10,205 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/4695d493_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:10,205 - INFO - 🔍 [23:55:10] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:10,206 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:10,208 - INFO - ⬆️ [23:55:10] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:10,217 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:10,220 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4695d493_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:10,220 - INFO - Processing image from S3...
2025-09-23 23:55:11,468 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/a12f13b9_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:11,469 - INFO - 🔍 [23:55:11] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:11,470 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:11,470 - INFO - ⬆️ [23:55:11] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:11,482 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:11,490 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a12f13b9_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:11,490 - INFO - Processing image from S3...
2025-09-23 23:55:12,046 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5038bbdb_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:12,180 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/860d531c_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:12,181 - INFO - 🔍 [23:55:12] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:12,182 - INFO - ⬆️ [23:55:12] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:12,182 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:12,199 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:12,205 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/860d531c_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:12,206 - INFO - Processing image from S3...
2025-09-23 23:55:12,930 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/965f56c7_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:12,931 - INFO - 🔍 [23:55:12] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:12,933 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:12,953 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:12,960 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/965f56c7_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:12,962 - INFO - Processing image from S3...
2025-09-23 23:55:12,989 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:12,989 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:55:13,302 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5038bbdb_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:13,305 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/15a94906_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:13,315 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:13,315 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:55:13,624 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/15a94906_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:14,150 - INFO - Page 2: Extracted 1821 characters, 105 lines from 6ffb2be6_O2IU5G77LYNTYE0RP1TI_c859440b_page_002.pdf
2025-09-23 23:55:14,151 - INFO - Successfully processed page 2
2025-09-23 23:55:14,355 - INFO - S3 Image temp/860d531c_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:55:14,357 - INFO - Page 1: Extracted 519 characters, 34 lines from b44ea181_KE7TCH9TPQZFVA5CZ3HT_af589361_page_001.pdf
2025-09-23 23:55:14,357 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:14,357 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:14,359 - INFO - Successfully processed page 1
2025-09-23 23:55:14,361 - INFO - Combined 1 pages into final text
2025-09-23 23:55:14,361 - INFO - Text validation for b44ea181_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:55:14,362 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:14,362 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:14,523 - INFO - Page 5: Extracted 2059 characters, 131 lines from 6ffb2be6_O2IU5G77LYNTYE0RP1TI_c859440b_page_005.pdf
2025-09-23 23:55:14,523 - INFO - Successfully processed page 5
2025-09-23 23:55:14,565 - INFO - Page 3: Extracted 2265 characters, 147 lines from 6ffb2be6_O2IU5G77LYNTYE0RP1TI_c859440b_page_003.pdf
2025-09-23 23:55:14,565 - INFO - Successfully processed page 3
2025-09-23 23:55:14,642 - INFO - Page 1: Extracted 1731 characters, 110 lines from 6ffb2be6_O2IU5G77LYNTYE0RP1TI_c859440b_page_001.pdf
2025-09-23 23:55:14,643 - INFO - Successfully processed page 1
2025-09-23 23:55:14,740 - INFO - Page 4: Extracted 2242 characters, 148 lines from 6ffb2be6_O2IU5G77LYNTYE0RP1TI_c859440b_page_004.pdf
2025-09-23 23:55:14,740 - INFO - Successfully processed page 4
2025-09-23 23:55:14,909 - INFO - S3 Image temp/965f56c7_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:55:14,909 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:14,910 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:15,321 - INFO - S3 Image temp/a12f13b9_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:55:15,321 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:15,321 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:15,570 - INFO - S3 Image temp/4695d493_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:55:15,575 - INFO - Page 6: Extracted 1973 characters, 129 lines from 6ffb2be6_O2IU5G77LYNTYE0RP1TI_c859440b_page_006.pdf
2025-09-23 23:55:15,575 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:15,576 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:15,577 - INFO - Successfully processed page 6
2025-09-23 23:55:16,080 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/860d531c_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:16,087 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:16,087 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:55:16,383 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/860d531c_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:16,609 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b44ea181_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:16,626 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-23 23:55:16,626 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:55:16,917 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b44ea181_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:17,019 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/965f56c7_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:17,026 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:55:17,026 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:55:17,265 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4695d493_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:17,331 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/965f56c7_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:17,370 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:17,371 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:55:17,542 - INFO - Page 7: Extracted 417 characters, 27 lines from 6ffb2be6_O2IU5G77LYNTYE0RP1TI_c859440b_page_007.pdf
2025-09-23 23:55:17,542 - INFO - Successfully processed page 7
2025-09-23 23:55:17,543 - INFO - Combined 7 pages into final text
2025-09-23 23:55:17,543 - INFO - Text validation for 6ffb2be6_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:55:17,544 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:17,544 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:17,665 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4695d493_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:18,516 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a12f13b9_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:18,532 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:55:18,532 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:55:18,833 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a12f13b9_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:20,183 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6ffb2be6_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:20,302 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:20,302 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,609 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6ffb2be6_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:20,613 - INFO - 
📊 Processing Summary:
2025-09-23 23:55:20,613 - INFO -    Total files: 8
2025-09-23 23:55:20,613 - INFO -    Successful: 8
2025-09-23 23:55:20,613 - INFO -    Failed: 0
2025-09-23 23:55:20,613 - INFO -    Duration: 18.33 seconds
2025-09-23 23:55:20,613 - INFO -    Output directory: output
2025-09-23 23:55:20,613 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:55:20,614 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:55:20,614 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:55:20,614 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-23 23:55:20,614 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:55:20,614 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:55:20,614 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:55:20,614 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:55:20,614 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:55:20,614 - INFO - 
============================================================================================================================================
2025-09-23 23:55:20,615 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:55:20,615 - INFO - ============================================================================================================================================
2025-09-23 23:55:20,615 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:55:20,615 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:55:20,615 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:55:20,615 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:20,615 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:55:20,615 - INFO - 
2025-09-23 23:55:20,615 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:55:20,615 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:20,615 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:55:20,615 - INFO - 
2025-09-23 23:55:20,615 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      coa                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:55:20,615 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:20,615 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:55:20,615 - INFO - 
2025-09-23 23:55:20,615 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:20,615 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:20,615 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,616 - INFO - 
2025-09-23 23:55:20,616 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:20,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:20,616 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,616 - INFO - 
2025-09-23 23:55:20,616 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:20,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:20,616 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,616 - INFO - 
2025-09-23 23:55:20,616 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:20,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:20,616 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,616 - INFO - 
2025-09-23 23:55:20,616 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:20,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:20,616 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,616 - INFO - 
2025-09-23 23:55:20,616 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:20,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:20,616 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,616 - INFO - 
2025-09-23 23:55:20,616 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:20,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:20,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,617 - INFO - 
2025-09-23 23:55:20,617 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:55:20,617 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:20,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:55:20,617 - INFO - 
2025-09-23 23:55:20,617 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      other                run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:55:20,617 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:20,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:55:20,617 - INFO - 
2025-09-23 23:55:20,617 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:55:20,617 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:20,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:55:20,617 - INFO - 
2025-09-23 23:55:20,617 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:55:20,617 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:20,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:55:20,617 - INFO - 
2025-09-23 23:55:20,617 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:55:20,617 - INFO - Total entries: 14
2025-09-23 23:55:20,617 - INFO - ============================================================================================================================================
2025-09-23 23:55:20,617 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:55:20,617 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:55:20,617 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:55:20,618 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:55:20,618 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → coa             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:55:20,618 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,618 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,618 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,618 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,618 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,618 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,618 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:20,618 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:55:20,618 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → other           | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:55:20,618 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:55:20,618 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:55:20,618 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:55:20,618 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 18.328841, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
