2025-09-23 23:56:27,251 - INFO - Logging initialized. Log file: logs/test_classification_20250923_235627.log
2025-09-23 23:56:27,251 - INFO - 📁 Found 8 files to process
2025-09-23 23:56:27,251 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:56:27,251 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:56:27,251 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:56:27,251 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:56:27,251 - INFO - ⬆️ [23:56:27] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:28,634 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/4f716211_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:28,634 - INFO - 🔍 [23:56:28] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:28,635 - INFO - ⬆️ [23:56:28] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:28,637 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:28,657 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:28,663 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4f716211_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:28,663 - INFO - Processing PDF from S3...
2025-09-23 23:56:28,663 - INFO - Downloading PDF from S3 to /tmp/tmpyn13xd_y/4f716211_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:30,078 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:56:30,078 - INFO - Splitting PDF into individual pages...
2025-09-23 23:56:30,080 - INFO - Splitting PDF 4f716211_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:56:30,084 - INFO - Split PDF into 1 pages
2025-09-23 23:56:30,084 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:56:30,084 - INFO - Expected pages: [1]
2025-09-23 23:56:34,140 - INFO - Page 1: Extracted 1260 characters, 84 lines from 4f716211_BQJUG5URFR2GH9ECWFV4_78784bdb_page_001.pdf
2025-09-23 23:56:34,140 - INFO - Successfully processed page 1
2025-09-23 23:56:34,141 - INFO - Combined 1 pages into final text
2025-09-23 23:56:34,141 - INFO - Text validation for 4f716211_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:56:34,141 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:34,141 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:35,005 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/a814fb07_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:35,005 - INFO - 🔍 [23:56:35] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:35,006 - INFO - ⬆️ [23:56:35] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:35,007 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:35,024 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:35,028 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a814fb07_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:35,028 - INFO - Processing image from S3...
2025-09-23 23:56:35,961 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/6dd3bb0d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:35,961 - INFO - 🔍 [23:56:35] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:35,962 - INFO - ⬆️ [23:56:35] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:35,963 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:35,976 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4f716211_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:35,983 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:35,992 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6dd3bb0d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:35,994 - INFO - Processing PDF from S3...
2025-09-23 23:56:35,994 - INFO - Downloading PDF from S3 to /tmp/tmpvr31p1jm/6dd3bb0d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:36,680 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/d3c57291_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:36,680 - INFO - 🔍 [23:56:36] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:36,682 - INFO - ⬆️ [23:56:36] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:36,683 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:36,701 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:36,705 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d3c57291_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:36,706 - INFO - Processing PDF from S3...
2025-09-23 23:56:36,706 - INFO - Downloading PDF from S3 to /tmp/tmpirmlc90j/d3c57291_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:38,061 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:56:38,061 - INFO - Splitting PDF into individual pages...
2025-09-23 23:56:38,062 - INFO - Splitting PDF 6dd3bb0d_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:56:38,066 - INFO - Split PDF into 1 pages
2025-09-23 23:56:38,066 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:56:38,067 - INFO - Expected pages: [1]
2025-09-23 23:56:38,801 - INFO - S3 Image temp/a814fb07_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:56:38,801 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:38,801 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:39,116 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:56:39,116 - INFO - Splitting PDF into individual pages...
2025-09-23 23:56:39,118 - INFO - Splitting PDF d3c57291_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:56:39,124 - INFO - Split PDF into 7 pages
2025-09-23 23:56:39,124 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:56:39,124 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:56:40,854 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a814fb07_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:40,857 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/b1477bf3_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:40,857 - INFO - 🔍 [23:56:40] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:40,857 - INFO - ⬆️ [23:56:40] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:40,857 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:40,866 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:40,868 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b1477bf3_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:40,868 - INFO - Processing image from S3...
2025-09-23 23:56:42,566 - INFO - Page 1: Extracted 519 characters, 34 lines from 6dd3bb0d_KE7TCH9TPQZFVA5CZ3HT_bcd64504_page_001.pdf
2025-09-23 23:56:42,566 - INFO - Successfully processed page 1
2025-09-23 23:56:42,566 - INFO - Combined 1 pages into final text
2025-09-23 23:56:42,567 - INFO - Text validation for 6dd3bb0d_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:56:42,567 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:42,567 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:42,731 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/6d7a33a5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:42,731 - INFO - 🔍 [23:56:42] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:42,731 - INFO - ⬆️ [23:56:42] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:42,732 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:42,749 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:42,784 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6d7a33a5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:42,784 - INFO - Processing image from S3...
2025-09-23 23:56:43,333 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/da6679e3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:43,333 - INFO - 🔍 [23:56:43] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:43,333 - INFO - ⬆️ [23:56:43] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:43,334 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:43,341 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:43,343 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/da6679e3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:43,343 - INFO - Processing image from S3...
2025-09-23 23:56:43,410 - INFO - Page 2: Extracted 1821 characters, 105 lines from d3c57291_O2IU5G77LYNTYE0RP1TI_1a95b513_page_002.pdf
2025-09-23 23:56:43,410 - INFO - Successfully processed page 2
2025-09-23 23:56:43,675 - INFO - Page 5: Extracted 2059 characters, 131 lines from d3c57291_O2IU5G77LYNTYE0RP1TI_1a95b513_page_005.pdf
2025-09-23 23:56:43,675 - INFO - Successfully processed page 5
2025-09-23 23:56:43,922 - INFO - Page 3: Extracted 2265 characters, 147 lines from d3c57291_O2IU5G77LYNTYE0RP1TI_1a95b513_page_003.pdf
2025-09-23 23:56:43,922 - INFO - Successfully processed page 3
2025-09-23 23:56:43,933 - INFO - Page 1: Extracted 1731 characters, 110 lines from d3c57291_O2IU5G77LYNTYE0RP1TI_1a95b513_page_001.pdf
2025-09-23 23:56:43,934 - INFO - Successfully processed page 1
2025-09-23 23:56:44,370 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/97a45fc0_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:44,371 - INFO - 🔍 [23:56:44] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:44,372 - INFO - Initializing TextractProcessor...
2025-09-23 23:56:44,385 - INFO - Initializing BedrockProcessor...
2025-09-23 23:56:44,393 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/97a45fc0_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:44,395 - INFO - Processing image from S3...
2025-09-23 23:56:44,429 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:44,429 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:56:44,477 - INFO - Page 4: Extracted 2242 characters, 148 lines from d3c57291_O2IU5G77LYNTYE0RP1TI_1a95b513_page_004.pdf
2025-09-23 23:56:44,478 - INFO - Successfully processed page 4
2025-09-23 23:56:44,687 - INFO - Page 6: Extracted 1973 characters, 129 lines from d3c57291_O2IU5G77LYNTYE0RP1TI_1a95b513_page_006.pdf
2025-09-23 23:56:44,687 - INFO - Successfully processed page 6
2025-09-23 23:56:44,715 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4f716211_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:44,721 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:44,722 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:56:45,041 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a814fb07_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:45,068 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6dd3bb0d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:45,090 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-23 23:56:45,090 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:56:45,232 - INFO - S3 Image temp/da6679e3_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:56:45,232 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:45,232 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:45,392 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6dd3bb0d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:46,203 - INFO - S3 Image temp/b1477bf3_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:56:46,203 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:46,203 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:46,206 - INFO - Page 7: Extracted 417 characters, 27 lines from d3c57291_O2IU5G77LYNTYE0RP1TI_1a95b513_page_007.pdf
2025-09-23 23:56:46,206 - INFO - Successfully processed page 7
2025-09-23 23:56:46,207 - INFO - Combined 7 pages into final text
2025-09-23 23:56:46,207 - INFO - Text validation for d3c57291_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:56:46,207 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:46,207 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:46,341 - INFO - S3 Image temp/97a45fc0_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:56:46,341 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:46,341 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:46,885 - INFO - S3 Image temp/6d7a33a5_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:56:46,885 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:56:46,885 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:56:47,033 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/da6679e3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:47,038 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:47,038 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:56:47,336 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/da6679e3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:48,124 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b1477bf3_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:48,160 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:48,160 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:56:48,310 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/97a45fc0_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:48,446 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b1477bf3_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:48,450 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:56:48,450 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:56:48,567 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d3c57291_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:48,773 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/97a45fc0_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:48,894 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:56:48,894 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:49,181 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d3c57291_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:50,924 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6d7a33a5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:50,946 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:56:50,946 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:56:51,233 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6d7a33a5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:51,234 - INFO - 
📊 Processing Summary:
2025-09-23 23:56:51,234 - INFO -    Total files: 8
2025-09-23 23:56:51,234 - INFO -    Successful: 8
2025-09-23 23:56:51,234 - INFO -    Failed: 0
2025-09-23 23:56:51,234 - INFO -    Duration: 23.98 seconds
2025-09-23 23:56:51,234 - INFO -    Output directory: output
2025-09-23 23:56:51,234 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:56:51,234 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:56:51,235 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:56:51,235 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-23 23:56:51,235 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:56:51,235 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:56:51,235 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:56:51,235 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:56:51,235 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:56:51,235 - INFO - 
============================================================================================================================================
2025-09-23 23:56:51,235 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:56:51,235 - INFO - ============================================================================================================================================
2025-09-23 23:56:51,235 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:56:51,236 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:56:51,236 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:56:51,236 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:56:51,236 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:56:51,236 - INFO - 
2025-09-23 23:56:51,236 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:56:51,236 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:56:51,236 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:56:51,236 - INFO - 
2025-09-23 23:56:51,236 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      coa                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:56:51,236 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:56:51,236 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:56:51,236 - INFO - 
2025-09-23 23:56:51,236 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:51,236 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:51,236 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,236 - INFO - 
2025-09-23 23:56:51,236 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:51,236 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:51,236 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,236 - INFO - 
2025-09-23 23:56:51,236 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:51,237 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:51,237 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,237 - INFO - 
2025-09-23 23:56:51,237 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:51,237 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:51,237 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,237 - INFO - 
2025-09-23 23:56:51,237 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:51,237 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:51,237 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,237 - INFO - 
2025-09-23 23:56:51,237 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:51,237 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:51,237 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,237 - INFO - 
2025-09-23 23:56:51,237 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:56:51,237 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:56:51,237 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,237 - INFO - 
2025-09-23 23:56:51,237 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:56:51,237 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:56:51,237 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:56:51,237 - INFO - 
2025-09-23 23:56:51,237 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      other                run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:56:51,238 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:56:51,238 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:56:51,238 - INFO - 
2025-09-23 23:56:51,238 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:56:51,238 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:56:51,238 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:56:51,238 - INFO - 
2025-09-23 23:56:51,238 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:56:51,238 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:56:51,238 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:56:51,238 - INFO - 
2025-09-23 23:56:51,238 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:56:51,238 - INFO - Total entries: 14
2025-09-23 23:56:51,238 - INFO - ============================================================================================================================================
2025-09-23 23:56:51,238 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:56:51,238 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:56:51,238 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:56:51,238 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:56:51,238 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → coa             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:56:51,238 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,238 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,238 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,238 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,238 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,239 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,239 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:56:51,239 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:56:51,239 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → other           | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:56:51,239 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:56:51,239 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:56:51,239 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:56:51,239 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 23.982561, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
