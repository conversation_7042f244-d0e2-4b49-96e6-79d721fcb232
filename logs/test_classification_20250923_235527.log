2025-09-23 23:55:27,757 - INFO - Logging initialized. Log file: logs/test_classification_20250923_235527.log
2025-09-23 23:55:27,757 - INFO - 📁 Found 8 files to process
2025-09-23 23:55:27,757 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:55:27,758 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:55:27,758 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:55:27,758 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:55:27,758 - INFO - ⬆️ [23:55:27] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:29,425 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/23ecc7a2_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:29,426 - INFO - 🔍 [23:55:29] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:29,426 - INFO - ⬆️ [23:55:29] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:29,427 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:29,450 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:29,458 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/23ecc7a2_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:29,458 - INFO - Processing PDF from S3...
2025-09-23 23:55:29,459 - INFO - Downloading PDF from S3 to /tmp/tmpvojreetu/23ecc7a2_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:30,838 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:55:30,839 - INFO - Splitting PDF into individual pages...
2025-09-23 23:55:30,839 - INFO - Splitting PDF 23ecc7a2_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:55:30,841 - INFO - Split PDF into 1 pages
2025-09-23 23:55:30,841 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:55:30,841 - INFO - Expected pages: [1]
2025-09-23 23:55:31,874 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/da897942_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:31,874 - INFO - 🔍 [23:55:31] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:31,875 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:31,875 - INFO - ⬆️ [23:55:31] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:31,891 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:31,896 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/da897942_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:31,896 - INFO - Processing image from S3...
2025-09-23 23:55:32,511 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/76109177_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:32,512 - INFO - 🔍 [23:55:32] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:32,513 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:32,513 - INFO - ⬆️ [23:55:32] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:32,533 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:32,539 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/76109177_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:32,539 - INFO - Processing PDF from S3...
2025-09-23 23:55:32,539 - INFO - Downloading PDF from S3 to /tmp/tmp_zgdcz1r/76109177_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:33,178 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/d4fbe005_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:33,178 - INFO - 🔍 [23:55:33] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:33,178 - INFO - ⬆️ [23:55:33] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:33,178 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:33,185 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:33,192 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d4fbe005_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:33,193 - INFO - Processing PDF from S3...
2025-09-23 23:55:33,193 - INFO - Downloading PDF from S3 to /tmp/tmpg3633lpo/d4fbe005_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:34,704 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/3559173e_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:34,705 - INFO - 🔍 [23:55:34] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:34,706 - INFO - ⬆️ [23:55:34] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:34,706 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:34,724 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:34,727 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3559173e_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:34,728 - INFO - Processing image from S3...
2025-09-23 23:55:34,736 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:55:34,736 - INFO - Splitting PDF into individual pages...
2025-09-23 23:55:34,738 - INFO - Splitting PDF 76109177_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:55:34,744 - INFO - Split PDF into 1 pages
2025-09-23 23:55:34,745 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:55:34,745 - INFO - Expected pages: [1]
2025-09-23 23:55:35,036 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:55:35,036 - INFO - Splitting PDF into individual pages...
2025-09-23 23:55:35,039 - INFO - Splitting PDF d4fbe005_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:55:35,061 - INFO - Split PDF into 7 pages
2025-09-23 23:55:35,061 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:55:35,062 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:55:35,195 - INFO - Page 1: Extracted 1260 characters, 84 lines from 23ecc7a2_BQJUG5URFR2GH9ECWFV4_6d6e5ecc_page_001.pdf
2025-09-23 23:55:35,196 - INFO - Successfully processed page 1
2025-09-23 23:55:35,196 - INFO - Combined 1 pages into final text
2025-09-23 23:55:35,196 - INFO - Text validation for 23ecc7a2_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:55:35,197 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:35,197 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:35,443 - INFO - S3 Image temp/da897942_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:55:35,443 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:35,444 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:35,797 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/c2311838_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:35,798 - INFO - 🔍 [23:55:35] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:35,798 - INFO - ⬆️ [23:55:35] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:35,802 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:35,809 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:35,839 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c2311838_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:35,840 - INFO - Processing image from S3...
2025-09-23 23:55:36,406 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/52dadb8c_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:36,407 - INFO - 🔍 [23:55:36] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:36,408 - INFO - ⬆️ [23:55:36] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:36,410 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:36,428 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:36,433 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/52dadb8c_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:36,433 - INFO - Processing image from S3...
2025-09-23 23:55:37,087 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/23ecc7a2_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:37,192 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/8fb81be5_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:37,193 - INFO - 🔍 [23:55:37] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:37,194 - INFO - Initializing TextractProcessor...
2025-09-23 23:55:37,211 - INFO - Initializing BedrockProcessor...
2025-09-23 23:55:37,220 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8fb81be5_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:37,222 - INFO - Processing image from S3...
2025-09-23 23:55:37,249 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:37,249 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:55:37,606 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/23ecc7a2_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:38,600 - INFO - S3 Image temp/52dadb8c_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:55:38,600 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:38,600 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:39,351 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/da897942_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:39,365 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:39,366 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:55:39,370 - INFO - S3 Image temp/8fb81be5_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:55:39,370 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:39,370 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:39,671 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/da897942_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:39,680 - INFO - Page 3: Extracted 2265 characters, 147 lines from d4fbe005_O2IU5G77LYNTYE0RP1TI_41ec60d8_page_003.pdf
2025-09-23 23:55:39,680 - INFO - Successfully processed page 3
2025-09-23 23:55:39,838 - INFO - Page 2: Extracted 1821 characters, 105 lines from d4fbe005_O2IU5G77LYNTYE0RP1TI_41ec60d8_page_002.pdf
2025-09-23 23:55:39,838 - INFO - Successfully processed page 2
2025-09-23 23:55:39,843 - INFO - Page 1: Extracted 519 characters, 34 lines from 76109177_KE7TCH9TPQZFVA5CZ3HT_4b6073c1_page_001.pdf
2025-09-23 23:55:39,843 - INFO - Successfully processed page 1
2025-09-23 23:55:39,844 - INFO - Combined 1 pages into final text
2025-09-23 23:55:39,844 - INFO - Text validation for 76109177_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:55:39,844 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:39,844 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:39,877 - INFO - Page 1: Extracted 1731 characters, 110 lines from d4fbe005_O2IU5G77LYNTYE0RP1TI_41ec60d8_page_001.pdf
2025-09-23 23:55:39,877 - INFO - Successfully processed page 1
2025-09-23 23:55:39,999 - INFO - Page 5: Extracted 2059 characters, 131 lines from d4fbe005_O2IU5G77LYNTYE0RP1TI_41ec60d8_page_005.pdf
2025-09-23 23:55:39,999 - INFO - Successfully processed page 5
2025-09-23 23:55:40,045 - INFO - Page 6: Extracted 1973 characters, 129 lines from d4fbe005_O2IU5G77LYNTYE0RP1TI_41ec60d8_page_006.pdf
2025-09-23 23:55:40,046 - INFO - Successfully processed page 6
2025-09-23 23:55:40,155 - INFO - S3 Image temp/c2311838_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:55:40,155 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:40,155 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:40,209 - INFO - Page 4: Extracted 2242 characters, 148 lines from d4fbe005_O2IU5G77LYNTYE0RP1TI_41ec60d8_page_004.pdf
2025-09-23 23:55:40,209 - INFO - Successfully processed page 4
2025-09-23 23:55:40,233 - INFO - S3 Image temp/3559173e_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:55:40,233 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:40,233 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:40,273 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/52dadb8c_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:40,281 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:40,282 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:55:40,573 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/52dadb8c_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:41,910 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3559173e_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:41,958 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:41,958 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:55:42,181 - INFO - Page 7: Extracted 417 characters, 27 lines from d4fbe005_O2IU5G77LYNTYE0RP1TI_41ec60d8_page_007.pdf
2025-09-23 23:55:42,181 - INFO - Successfully processed page 7
2025-09-23 23:55:42,182 - INFO - Combined 7 pages into final text
2025-09-23 23:55:42,183 - INFO - Text validation for d4fbe005_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:55:42,183 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:55:42,183 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:55:42,316 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3559173e_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:42,491 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/76109177_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:42,509 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:42,510 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:55:42,803 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/76109177_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:43,035 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c2311838_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:43,057 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:55:43,057 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:55:43,191 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8fb81be5_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:43,351 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c2311838_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:43,352 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:55:43,352 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:55:43,648 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8fb81be5_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:44,775 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d4fbe005_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:44,889 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:55:44,889 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,181 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d4fbe005_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:45,185 - INFO - 
📊 Processing Summary:
2025-09-23 23:55:45,186 - INFO -    Total files: 8
2025-09-23 23:55:45,186 - INFO -    Successful: 8
2025-09-23 23:55:45,186 - INFO -    Failed: 0
2025-09-23 23:55:45,186 - INFO -    Duration: 17.43 seconds
2025-09-23 23:55:45,186 - INFO -    Output directory: output
2025-09-23 23:55:45,186 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:55:45,187 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:55:45,187 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:55:45,187 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:55:45,187 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:55:45,187 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:55:45,187 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:55:45,187 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:55:45,187 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:55:45,188 - INFO - 
============================================================================================================================================
2025-09-23 23:55:45,188 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:55:45,188 - INFO - ============================================================================================================================================
2025-09-23 23:55:45,188 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:55:45,188 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:55:45,188 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:55:45,188 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:55:45,188 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:55:45,189 - INFO - 
2025-09-23 23:55:45,189 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:55:45,189 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:55:45,189 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:55:45,189 - INFO - 
2025-09-23 23:55:45,189 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      log                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:55:45,189 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:55:45,189 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:55:45,189 - INFO - 
2025-09-23 23:55:45,189 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:45,189 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:45,189 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,189 - INFO - 
2025-09-23 23:55:45,189 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:45,189 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:45,189 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,189 - INFO - 
2025-09-23 23:55:45,189 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:45,189 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:45,189 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,189 - INFO - 
2025-09-23 23:55:45,190 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:45,190 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:45,190 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,190 - INFO - 
2025-09-23 23:55:45,190 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:45,190 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:45,190 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,190 - INFO - 
2025-09-23 23:55:45,190 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:45,190 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:45,190 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,190 - INFO - 
2025-09-23 23:55:45,190 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:55:45,190 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:55:45,190 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,190 - INFO - 
2025-09-23 23:55:45,190 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:55:45,190 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:55:45,190 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:55:45,190 - INFO - 
2025-09-23 23:55:45,191 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      other                run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:55:45,191 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:55:45,191 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:55:45,191 - INFO - 
2025-09-23 23:55:45,191 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:55:45,191 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:55:45,191 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:55:45,191 - INFO - 
2025-09-23 23:55:45,191 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:55:45,191 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:55:45,191 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:55:45,191 - INFO - 
2025-09-23 23:55:45,191 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:55:45,191 - INFO - Total entries: 14
2025-09-23 23:55:45,191 - INFO - ============================================================================================================================================
2025-09-23 23:55:45,191 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:55:45,191 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:55:45,191 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:55:45,191 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:55:45,191 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → log             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:55:45,192 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,192 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,192 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,192 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,192 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,192 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,192 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:55:45,192 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:55:45,192 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → other           | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:55:45,192 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:55:45,192 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:55:45,192 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:55:45,192 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 17.42789, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
