2025-09-23 23:52:23,450 - INFO - Logging initialized. Log file: logs/test_classification_20250923_235223.log
2025-09-23 23:52:23,451 - INFO - 📁 Found 8 files to process
2025-09-23 23:52:23,451 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:52:23,451 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:52:23,451 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:52:23,451 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:52:23,451 - INFO - ⬆️ [23:52:23] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:52:24,898 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/c68ddc2c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:52:24,899 - INFO - 🔍 [23:52:24] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:52:24,900 - INFO - Initializing TextractProcessor...
2025-09-23 23:52:24,900 - INFO - ⬆️ [23:52:24] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:52:24,922 - INFO - Initializing BedrockProcessor...
2025-09-23 23:52:24,927 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c68ddc2c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:52:24,927 - INFO - Processing PDF from S3...
2025-09-23 23:52:24,927 - INFO - Downloading PDF from S3 to /tmp/tmpdbvspzjc/c68ddc2c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:52:26,436 - INFO - Downloaded PDF size: 0.0 MB
2025-09-23 23:52:26,437 - INFO - Splitting PDF into individual pages...
2025-09-23 23:52:26,438 - INFO - Splitting PDF c68ddc2c_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:52:26,442 - INFO - Split PDF into 1 pages
2025-09-23 23:52:26,442 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:52:26,442 - INFO - Expected pages: [1]
2025-09-23 23:52:29,564 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/0bb70c51_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:52:29,565 - INFO - 🔍 [23:52:29] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:52:29,565 - INFO - ⬆️ [23:52:29] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:52:29,567 - INFO - Initializing TextractProcessor...
2025-09-23 23:52:29,580 - INFO - Initializing BedrockProcessor...
2025-09-23 23:52:29,582 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0bb70c51_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:52:29,582 - INFO - Processing image from S3...
2025-09-23 23:52:30,247 - INFO - Page 1: Extracted 1260 characters, 84 lines from c68ddc2c_BQJUG5URFR2GH9ECWFV4_f9d215e7_page_001.pdf
2025-09-23 23:52:30,247 - INFO - Successfully processed page 1
2025-09-23 23:52:30,248 - INFO - Combined 1 pages into final text
2025-09-23 23:52:30,248 - INFO - Text validation for c68ddc2c_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:52:30,248 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:52:30,248 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:52:30,569 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/9dec8356_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:52:30,570 - INFO - 🔍 [23:52:30] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:52:30,571 - INFO - ⬆️ [23:52:30] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:30,572 - INFO - Initializing TextractProcessor...
2025-09-23 23:52:30,590 - INFO - Initializing BedrockProcessor...
2025-09-23 23:52:30,593 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9dec8356_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:52:30,593 - INFO - Processing PDF from S3...
2025-09-23 23:52:30,593 - INFO - Downloading PDF from S3 to /tmp/tmp5omrc4cd/9dec8356_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:52:31,247 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/2f6ce2fe_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:31,247 - INFO - 🔍 [23:52:31] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:31,248 - INFO - ⬆️ [23:52:31] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:52:31,249 - INFO - Initializing TextractProcessor...
2025-09-23 23:52:31,263 - INFO - Initializing BedrockProcessor...
2025-09-23 23:52:31,267 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2f6ce2fe_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:31,267 - INFO - Processing PDF from S3...
2025-09-23 23:52:31,267 - INFO - Downloading PDF from S3 to /tmp/tmpzhiohg3_/2f6ce2fe_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:32,328 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c68ddc2c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:52:32,698 - INFO - Downloaded PDF size: 0.2 MB
2025-09-23 23:52:32,699 - INFO - Splitting PDF into individual pages...
2025-09-23 23:52:32,700 - INFO - Splitting PDF 9dec8356_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:52:32,704 - INFO - Split PDF into 1 pages
2025-09-23 23:52:32,704 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:52:32,704 - INFO - Expected pages: [1]
2025-09-23 23:52:33,055 - INFO - Downloaded PDF size: 0.1 MB
2025-09-23 23:52:33,056 - INFO - Splitting PDF into individual pages...
2025-09-23 23:52:33,057 - INFO - Splitting PDF 2f6ce2fe_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:52:33,061 - INFO - Split PDF into 7 pages
2025-09-23 23:52:33,061 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:52:33,061 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:52:33,881 - INFO - S3 Image temp/0bb70c51_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:52:33,881 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:52:33,881 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:52:35,853 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/d11adae8_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:52:35,853 - INFO - 🔍 [23:52:35] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:52:35,853 - INFO - ⬆️ [23:52:35] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:52:35,854 - INFO - Initializing TextractProcessor...
2025-09-23 23:52:35,865 - INFO - Initializing BedrockProcessor...
2025-09-23 23:52:35,867 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d11adae8_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:52:35,868 - INFO - Processing image from S3...
2025-09-23 23:52:37,040 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0bb70c51_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:52:37,550 - INFO - Page 1: Extracted 1731 characters, 110 lines from 2f6ce2fe_O2IU5G77LYNTYE0RP1TI_ae6ecf52_page_001.pdf
2025-09-23 23:52:37,551 - INFO - Successfully processed page 1
2025-09-23 23:52:37,707 - INFO - Page 1: Extracted 519 characters, 34 lines from 9dec8356_KE7TCH9TPQZFVA5CZ3HT_9f5a1618_page_001.pdf
2025-09-23 23:52:37,707 - INFO - Successfully processed page 1
2025-09-23 23:52:37,708 - INFO - Combined 1 pages into final text
2025-09-23 23:52:37,708 - INFO - Text validation for 9dec8356_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:52:37,708 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:52:37,708 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:52:38,048 - INFO - Page 2: Extracted 1821 characters, 105 lines from 2f6ce2fe_O2IU5G77LYNTYE0RP1TI_ae6ecf52_page_002.pdf
2025-09-23 23:52:38,048 - INFO - Successfully processed page 2
2025-09-23 23:52:38,081 - INFO - Page 6: Extracted 1973 characters, 129 lines from 2f6ce2fe_O2IU5G77LYNTYE0RP1TI_ae6ecf52_page_006.pdf
2025-09-23 23:52:38,081 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/b48f5a47_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:52:38,081 - INFO - 🔍 [23:52:38] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:52:38,081 - INFO - Successfully processed page 6
2025-09-23 23:52:38,082 - INFO - ⬆️ [23:52:38] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:52:38,083 - INFO - Initializing TextractProcessor...
2025-09-23 23:52:38,091 - INFO - Initializing BedrockProcessor...
2025-09-23 23:52:38,093 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b48f5a47_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:52:38,093 - INFO - Processing image from S3...
2025-09-23 23:52:38,296 - INFO - Page 4: Extracted 2242 characters, 148 lines from 2f6ce2fe_O2IU5G77LYNTYE0RP1TI_ae6ecf52_page_004.pdf
2025-09-23 23:52:38,307 - INFO - Page 5: Extracted 2059 characters, 131 lines from 2f6ce2fe_O2IU5G77LYNTYE0RP1TI_ae6ecf52_page_005.pdf
2025-09-23 23:52:38,307 - INFO - Successfully processed page 4
2025-09-23 23:52:38,308 - INFO - Successfully processed page 5
2025-09-23 23:52:38,348 - INFO - Page 3: Extracted 2265 characters, 147 lines from 2f6ce2fe_O2IU5G77LYNTYE0RP1TI_ae6ecf52_page_003.pdf
2025-09-23 23:52:38,349 - INFO - Successfully processed page 3
2025-09-23 23:52:38,721 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/9c6e6dfa_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:52:38,722 - INFO - 🔍 [23:52:38] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:52:38,723 - INFO - ⬆️ [23:52:38] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:52:38,725 - INFO - Initializing TextractProcessor...
2025-09-23 23:52:38,742 - INFO - Initializing BedrockProcessor...
2025-09-23 23:52:38,747 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9c6e6dfa_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:52:38,748 - INFO - Processing image from S3...
2025-09-23 23:52:39,976 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/13a3ecd9_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:52:39,976 - INFO - 🔍 [23:52:39] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:52:39,978 - INFO - Initializing TextractProcessor...
2025-09-23 23:52:39,988 - INFO - Initializing BedrockProcessor...
2025-09-23 23:52:39,996 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/13a3ecd9_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:52:39,998 - INFO - Processing image from S3...
2025-09-23 23:52:40,026 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:52:40,026 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:52:40,273 - INFO - Page 7: Extracted 417 characters, 27 lines from 2f6ce2fe_O2IU5G77LYNTYE0RP1TI_ae6ecf52_page_007.pdf
2025-09-23 23:52:40,273 - INFO - Successfully processed page 7
2025-09-23 23:52:40,273 - INFO - Combined 7 pages into final text
2025-09-23 23:52:40,274 - INFO - Text validation for 2f6ce2fe_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:52:40,274 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:52:40,274 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:52:40,311 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c68ddc2c_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:52:40,320 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:52:40,320 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:52:40,604 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0bb70c51_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:52:40,664 - INFO - S3 Image temp/9c6e6dfa_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:52:40,664 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:52:40,664 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:52:40,709 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9dec8356_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:52:40,728 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-23 23:52:40,728 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:52:41,032 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9dec8356_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:52:41,039 - INFO - S3 Image temp/d11adae8_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:52:41,039 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:52:41,039 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:52:41,958 - INFO - S3 Image temp/b48f5a47_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:52:41,958 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:52:41,958 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:52:41,967 - INFO - S3 Image temp/13a3ecd9_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:52:41,968 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:52:41,968 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:52:42,565 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2f6ce2fe_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:42,660 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9c6e6dfa_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:52:42,688 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:52:42,688 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:42,963 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d11adae8_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:52:43,021 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2f6ce2fe_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:43,028 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:52:43,028 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:52:43,320 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9c6e6dfa_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:52:43,360 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:52:43,361 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:52:43,737 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d11adae8_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:52:43,817 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/13a3ecd9_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:52:43,823 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:52:43,823 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:52:44,107 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/13a3ecd9_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:52:44,353 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b48f5a47_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:52:44,374 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:52:44,374 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:52:44,659 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b48f5a47_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:52:44,659 - INFO - 
📊 Processing Summary:
2025-09-23 23:52:44,659 - INFO -    Total files: 8
2025-09-23 23:52:44,659 - INFO -    Successful: 8
2025-09-23 23:52:44,659 - INFO -    Failed: 0
2025-09-23 23:52:44,659 - INFO -    Duration: 21.21 seconds
2025-09-23 23:52:44,659 - INFO -    Output directory: output
2025-09-23 23:52:44,659 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:52:44,659 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:52:44,659 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:52:44,659 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-23 23:52:44,659 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:52:44,659 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:52:44,659 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:52:44,659 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:52:44,659 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:52:44,659 - INFO - 
============================================================================================================================================
2025-09-23 23:52:44,660 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:52:44,660 - INFO - ============================================================================================================================================
2025-09-23 23:52:44,660 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:52:44,660 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:52:44,660 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      other                run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      coa                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:52:44,660 - INFO - 
2025-09-23 23:52:44,660 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:52:44,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:52:44,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:52:44,661 - INFO - 
2025-09-23 23:52:44,661 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:52:44,661 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:52:44,661 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:52:44,661 - INFO - 
2025-09-23 23:52:44,661 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:52:44,661 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:52:44,661 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:52:44,661 - INFO - 
2025-09-23 23:52:44,661 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:52:44,661 - INFO - Total entries: 14
2025-09-23 23:52:44,661 - INFO - ============================================================================================================================================
2025-09-23 23:52:44,661 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:52:44,661 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:52:44,661 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:52:44,661 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → other           | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:52:44,661 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → coa             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:52:44,661 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,661 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,661 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,661 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,661 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,661 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,661 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:52:44,661 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:52:44,661 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:52:44,661 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:52:44,661 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:52:44,661 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:52:44,661 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 21.208123, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
