{"classification_result": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 6.21251743382345e-08, "Y": 1.3097367457248765e-07}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "9cb719fa-a1b4-4005-b661-7d41f79bceb1", "Relationships": [{"Type": "CHILD", "Ids": ["eed252d2-ad3f-42b4-b7fa-da1b08f2c687", "255ba674-beeb-48b3-a612-8010007eb94e", "7b93b824-2f68-4eea-a2cf-47968da9840b", "d1fbecfb-6fa2-48a1-92eb-bde45b0fb877", "63adb11f-b101-4424-96c7-f3798a9f900b", "15eed7b7-2665-4911-b993-9f6a6d6cf5a9", "46851495-ac57-4632-a76e-b16967d58699", "90b40525-0c68-45d0-b578-86a55fff7910", "51f83343-7111-43b8-9e21-2134c9127b80", "1af92174-47cf-433e-b193-b4766658ae2d", "0da206e1-de28-4c45-8a53-15e12c28b905", "eb516fa9-5c0b-4917-9297-fbd56b38390f", "3c8881b5-1d34-4851-97c4-9f34a88b99ca", "43828e19-a1d7-4574-ba9b-d54919ca4b67", "88aff123-b833-45fb-b51f-7c03ab2e81e9", "1b0e3fe7-66f8-45d8-b519-4f89bbd68c00", "9b0d298d-c5f0-4a95-9d5f-28ecb5eddfc7", "990a8638-052c-4f91-8005-c7c2c323e570", "b633bdd0-96b9-4c94-97fd-34950431fac3", "e294a0b8-9700-4663-b065-bb362ab255a9", "ae0a56ba-4c7e-4ec6-912f-f0e85489f8b5", "e1393900-8eea-4ceb-8a5c-f49a25303123", "148be307-d547-4a0e-859f-28fa01bf9efb", "9161f6b5-ebc3-4a05-8c1a-bda4c68a99fb", "d5ac6c30-3148-4e3c-91b3-09154ccb2814", "6d6944cf-8b96-4ea5-8b4b-177332d50841", "ee5ee496-bb0d-429b-9a04-a8ee237407af", "fb694827-7696-4b3e-94c5-1e866f01149e", "418d900d-97fb-4dc1-9766-86fb762732c5", "ab111f2d-ed00-41e0-8183-f06f116a240a", "793c2598-b0f8-415f-aed1-8b1bf34da92a", "fd37b166-0c67-4ccd-9989-50ff4e98ebe4", "91b14752-e157-4b10-8f83-95f034be449e", "33f8ee14-5539-4397-82fc-fd49cf1ac311", "9f9181f3-1b3c-4caa-a6e7-39819d15fc9f", "84e59017-2916-418c-9aed-588fff22249f", "c64d15c9-5a81-48e5-ac3d-02a685af294c", "f2c82da0-d99d-49ba-a23a-b4146935f2f8", "346f4729-b077-4df6-a1d4-0eb68a5427af", "a240eeab-5158-42e1-8200-2036885d1646", "cde9d417-2aa2-4408-b4cc-d808564e6d74", "7100874e-040e-4fcd-952e-04fa4b2ae5c5", "9f140819-7dfb-484d-bcdd-67fdc668a25d", "b556ccd8-b006-448d-8444-f303a72a3727", "0ab2363c-6291-4cb0-9da3-a341697347b2", "7ccc10b4-44a2-446e-94bf-f3d2588a1e06", "d76818e8-08ef-469c-b7c7-81c01601231f", "10a5a3f8-44ca-435e-bd15-cd8d97be41d6", "e30a6413-fbce-4fba-85f7-0eeafb022938"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Central Transport", "Geometry": {"BoundingBox": {"Width": 0.1148308515548706, "Height": 0.01157828513532877, "Left": 0.05718902871012688, "Top": 0.05624642223119736}, "Polygon": [{"X": 0.05718908831477165, "Y": 0.05624745413661003}, {"X": 0.17201988399028778, "Y": 0.05624642223119736}, {"X": 0.1720198094844818, "Y": 0.06782364845275879}, {"X": 0.05718902871012688, "Y": 0.06782470643520355}]}, "Id": "eed252d2-ad3f-42b4-b7fa-da1b08f2c687", "Relationships": [{"Type": "CHILD", "Ids": ["d78db949-916f-430f-b254-fd71b7ac7dd1", "ebe74301-bcc7-4c19-95e1-a5609c0aaa5d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "08/31/2025", "Geometry": {"BoundingBox": {"Width": 0.0731736347079277, "Height": 0.009412871673703194, "Left": 0.8657994270324707, "Top": 0.05629698932170868}, "Polygon": [{"X": 0.865799605846405, "Y": 0.05629764869809151}, {"X": 0.938973069190979, "Y": 0.05629698932170868}, {"X": 0.9389729499816895, "Y": 0.065709188580513}, {"X": 0.8657994270324707, "Y": 0.06570985913276672}]}, "Id": "255ba674-beeb-48b3-a612-8010007eb94e", "Relationships": [{"Type": "CHILD", "Ids": ["2aa9e48b-51bb-42db-bc4a-04695d1d8c03"]}]}, {"BlockType": "LINE", "Confidence": 99.89218139648438, "Text": "PRO#: 227407699", "Geometry": {"BoundingBox": {"Width": 0.12172986567020416, "Height": 0.009408773854374886, "Left": 0.057394713163375854, "Top": 0.07145050168037415}, "Polygon": [{"X": 0.057394761592149734, "Y": 0.07145162671804428}, {"X": 0.17912457883358002, "Y": 0.07145050168037415}, {"X": 0.17912451922893524, "Y": 0.08085812628269196}, {"X": 0.057394713163375854, "Y": 0.08085927367210388}]}, "Id": "7b93b824-2f68-4eea-a2cf-47968da9840b", "Relationships": [{"Type": "CHILD", "Ids": ["e81cb7b0-865c-4e21-a6ea-b0de9869f368", "6373ea1f-2b44-4b7f-a6d5-52353dd398e9"]}]}, {"BlockType": "LINE", "Confidence": 99.89476776123047, "Text": "Weight & Inspection", "Geometry": {"BoundingBox": {"Width": 0.13100199401378632, "Height": 0.011712157167494297, "Left": 0.8079518675804138, "Top": 0.07136138528585434}, "Polygon": [{"X": 0.8079520463943481, "Y": 0.07136259227991104}, {"X": 0.9389538764953613, "Y": 0.07136138528585434}, {"X": 0.938953697681427, "Y": 0.08307230472564697}, {"X": 0.8079518675804138, "Y": 0.08307354152202606}]}, "Id": "d1fbecfb-6fa2-48a1-92eb-bde45b0fb877", "Relationships": [{"Type": "CHILD", "Ids": ["7dd14a02-4f39-4f44-961e-64b52f241b2f", "a66e0914-b097-4c67-ac15-54ff67fa53b7", "d74be576-0815-486a-bbde-16b7945b74b8"]}]}, {"BlockType": "LINE", "Confidence": 99.94140625, "Text": "CENTRAL TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.16294315457344055, "Height": 0.013988837599754333, "Left": 0.4383789896965027, "Top": 0.1918865293264389}, "Polygon": [{"X": 0.43837910890579224, "Y": 0.19188834726810455}, {"X": 0.6013221144676208, "Y": 0.1918865293264389}, {"X": 0.6013219952583313, "Y": 0.205873504281044}, {"X": 0.4383789896965027, "Y": 0.20587536692619324}]}, "Id": "63adb11f-b101-4424-96c7-f3798a9f900b", "Relationships": [{"Type": "CHILD", "Ids": ["41ba07ad-b466-4f84-96cf-86fceca78573", "6ef50ba4-54ca-4fd6-bcef-ad615debe06c"]}]}, {"BlockType": "LINE", "Confidence": 99.88745880126953, "Text": "12225 Stephens Road", "Geometry": {"BoundingBox": {"Width": 0.12882336974143982, "Height": 0.01064684335142374, "Left": 0.4399399757385254, "Top": 0.2119910717010498}, "Polygon": [{"X": 0.43994009494781494, "Y": 0.211992546916008}, {"X": 0.5687633752822876, "Y": 0.2119910717010498}, {"X": 0.568763256072998, "Y": 0.2226364016532898}, {"X": 0.4399399757385254, "Y": 0.22263790667057037}]}, "Id": "15eed7b7-2665-4911-b993-9f6a6d6cf5a9", "Relationships": [{"Type": "CHILD", "Ids": ["75023b51-5a0f-4cc4-87d9-70790fe4a306", "ed79f8f6-0232-4244-a106-79810a9cb3fb", "52999710-ba42-4a93-a4b8-1c3eb4658b41"]}]}, {"BlockType": "LINE", "Confidence": 99.85464477539062, "Text": "Warren, MI 48089", "Geometry": {"BoundingBox": {"Width": 0.113776296377182, "Height": 0.010304750874638557, "Left": 0.44664397835731506, "Top": 0.22568704187870026}, "Polygon": [{"X": 0.4466440677642822, "Y": 0.2256883829832077}, {"X": 0.5604202747344971, "Y": 0.22568704187870026}, {"X": 0.5604201555252075, "Y": 0.23599044978618622}, {"X": 0.44664397835731506, "Y": 0.23599180579185486}]}, "Id": "46851495-ac57-4632-a76e-b16967d58699", "Relationships": [{"Type": "CHILD", "Ids": ["5f4a9619-e0c9-4d2d-8a37-66289e768dc1", "d41eae6a-517f-4028-a265-c2eb7e542547", "bf60582b-883f-4ca4-8aae-4f7153005b8a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CERTIFIED WEIGHT CERTIFICATE", "Geometry": {"BoundingBox": {"Width": 0.2301705777645111, "Height": 0.009217815473675728, "Left": 0.39221125841140747, "Top": 0.2900317907333374}, "Polygon": [{"X": 0.39221134781837463, "Y": 0.2900347113609314}, {"X": 0.622381865978241, "Y": 0.2900317907333374}, {"X": 0.6223817467689514, "Y": 0.2992466390132904}, {"X": 0.39221125841140747, "Y": 0.2992495894432068}]}, "Id": "90b40525-0c68-45d0-b578-86a55fff7910", "Relationships": [{"Type": "CHILD", "Ids": ["41973bea-5fd3-45af-993a-2b1352d33abc", "13eb7bdb-8387-44dc-a947-7b82d8602919", "c10b2f24-f6e6-485f-a7d8-3632e0a9ebc7"]}]}, {"BlockType": "LINE", "Confidence": 99.87091827392578, "Text": "Pro Number: 227407699", "Geometry": {"BoundingBox": {"Width": 0.17372579872608185, "Height": 0.009269524365663528, "Left": 0.15433162450790405, "Top": 0.31617191433906555}, "Polygon": [{"X": 0.15433168411254883, "Y": 0.3161742091178894}, {"X": 0.3280574083328247, "Y": 0.31617191433906555}, {"X": 0.32805734872817993, "Y": 0.3254391551017761}, {"X": 0.15433162450790405, "Y": 0.3254414498806}]}, "Id": "51f83343-7111-43b8-9e21-2134c9127b80", "Relationships": [{"Type": "CHILD", "Ids": ["0cec9bab-937c-427f-a005-3a715dda9a81", "6a996581-72db-4e59-b32f-10ca5c1a7cf1", "8d21eb92-c042-45d6-803a-bea2719fef69"]}]}, {"BlockType": "LINE", "Confidence": 99.96366119384766, "Text": "Bill of Lading", "Geometry": {"BoundingBox": {"Width": 0.08661317825317383, "Height": 0.011761363595724106, "Left": 0.6965249180793762, "Top": 0.3158475160598755}, "Polygon": [{"X": 0.6965250372886658, "Y": 0.3158486485481262}, {"X": 0.78313809633255, "Y": 0.3158475160598755}, {"X": 0.7831379175186157, "Y": 0.3276077210903168}, {"X": 0.6965249180793762, "Y": 0.3276088535785675}]}, "Id": "1af92174-47cf-433e-b193-b4766658ae2d", "Relationships": [{"Type": "CHILD", "Ids": ["ea85aa82-3143-447f-b424-8ecbece5cac7", "ceaa7b8d-40fd-4af0-94e1-813b6428f451", "d670fe7f-9038-4db4-8e6d-295192e77eda"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Date: 08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.1218271404504776, "Height": 0.01059777196496725, "Left": 0.15446539223194122, "Top": 0.32907816767692566}, "Polygon": [{"X": 0.154465451836586, "Y": 0.3290797770023346}, {"X": 0.2762925326824188, "Y": 0.32907816767692566}, {"X": 0.27629244327545166, "Y": 0.33967429399490356}, {"X": 0.15446539223194122, "Y": 0.3396759331226349}]}, "Id": "0da206e1-de28-4c45-8a53-15e12c28b905", "Relationships": [{"Type": "CHILD", "Ids": ["a7236181-61b6-428d-acc8-b67fc871a14e", "bf4cc605-81ff-48b4-b480-5dea5b495ef8"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "DIAMOND ENVELOPE", "Geometry": {"BoundingBox": {"Width": 0.10091961175203323, "Height": 0.006404740270227194, "Left": 0.16249734163284302, "Top": 0.3682035207748413}, "Polygon": [{"X": 0.1624973714351654, "Y": 0.3682049512863159}, {"X": 0.26341694593429565, "Y": 0.3682035207748413}, {"X": 0.2634168863296509, "Y": 0.3746068477630615}, {"X": 0.16249734163284302, "Y": 0.37460827827453613}]}, "Id": "eb516fa9-5c0b-4917-9297-fbd56b38390f", "Relationships": [{"Type": "CHILD", "Ids": ["e935cfd0-a2d0-41d8-879a-02f4c1108261", "e5c797fd-3ae8-47c6-a07c-bc58c2b808ca"]}]}, {"BlockType": "LINE", "Confidence": 99.74091339111328, "Text": "Weight Certificate", "Geometry": {"BoundingBox": {"Width": 0.08476640284061432, "Height": 0.008311820216476917, "Left": 0.7232305407524109, "Top": 0.3676687777042389}, "Polygon": [{"X": 0.7232306599617004, "Y": 0.3676699697971344}, {"X": 0.807996928691864, "Y": 0.3676687777042389}, {"X": 0.8079968094825745, "Y": 0.37597939372062683}, {"X": 0.7232305407524109, "Y": 0.37598058581352234}]}, "Id": "3c8881b5-1d34-4851-97c4-9f34a88b99ca", "Relationships": [{"Type": "CHILD", "Ids": ["55d3a02c-be24-4086-b3ad-8553e294b004", "1ee8df4e-6c61-4a5f-b1af-1ef10f4c006c"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "2270 WHITE OAK CIR", "Geometry": {"BoundingBox": {"Width": 0.10405416041612625, "Height": 0.006522432900965214, "Left": 0.16246169805526733, "Top": 0.37667709589004517}, "Polygon": [{"X": 0.16246172785758972, "Y": 0.37667855620384216}, {"X": 0.266515851020813, "Y": 0.37667709589004517}, {"X": 0.2665157914161682, "Y": 0.3831980526447296}, {"X": 0.16246169805526733, "Y": 0.3831995129585266}]}, "Id": "43828e19-a1d7-4574-ba9b-d54919ca4b67", "Relationships": [{"Type": "CHILD", "Ids": ["6739d4c0-000e-41d4-b389-b9516e12ffcb", "976cbb1e-72a3-4e89-97b9-3c0ab53d41e1", "b0bf7df0-730d-4cdd-a22f-39818b6c4843", "4f431d48-874f-4740-93bb-51b83e6a1708"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "We Certify that this shipment", "Geometry": {"BoundingBox": {"Width": 0.13997326791286469, "Height": 0.008207225240767002, "Left": 0.7073788642883301, "Top": 0.37618619203567505}, "Polygon": [{"X": 0.7073789834976196, "Y": 0.37618815898895264}, {"X": 0.847352147102356, "Y": 0.37618619203567505}, {"X": 0.8473520278930664, "Y": 0.3843914270401001}, {"X": 0.7073788642883301, "Y": 0.3843934237957001}]}, "Id": "88aff123-b833-45fb-b51f-7c03ab2e81e9", "Relationships": [{"Type": "CHILD", "Ids": ["71c06bfa-0181-44e4-a66d-a233a4e4b6fb", "5e7c928d-8d7d-48da-9545-353c6810306f", "7ff2d6a8-e716-4cdf-9f86-9c2b837d7e8d", "82c81974-7719-4576-ab51-ac406e622dd4", "bcc2b6ab-84d7-4352-9827-4d74ceaa83c4"]}]}, {"BlockType": "LINE", "Confidence": 99.71878814697266, "Text": "AURORA, IL 60502", "Geometry": {"BoundingBox": {"Width": 0.09084655344486237, "Height": 0.007452928461134434, "Left": 0.16181287169456482, "Top": 0.3850322663784027}, "Polygon": [{"X": 0.1618129163980484, "Y": 0.3850335478782654}, {"X": 0.2526594400405884, "Y": 0.3850322663784027}, {"X": 0.2526593804359436, "Y": 0.3924838900566101}, {"X": 0.16181287169456482, "Y": 0.39248520135879517}]}, "Id": "1b0e3fe7-66f8-45d8-b519-4f89bbd68c00", "Relationships": [{"Type": "CHILD", "Ids": ["a568a8b3-540b-444c-973d-93a9f8915dae", "801f46f0-826c-45dc-8f96-ed88f78c8cd0", "9b40e3ef-e706-46f9-9b9a-f455b4704003"]}]}, {"BlockType": "LINE", "Confidence": 99.95428466796875, "Text": "has been reweighed and that", "Geometry": {"BoundingBox": {"Width": 0.1379849761724472, "Height": 0.008062225766479969, "Left": 0.7077588438987732, "Top": 0.3848668932914734}, "Polygon": [{"X": 0.7077589631080627, "Y": 0.384868860244751}, {"X": 0.8457438349723816, "Y": 0.3848668932914734}, {"X": 0.845743715763092, "Y": 0.3929271399974823}, {"X": 0.7077588438987732, "Y": 0.3929291069507599}]}, "Id": "9b0d298d-c5f0-4a95-9d5f-28ecb5eddfc7", "Relationships": [{"Type": "CHILD", "Ids": ["67d3a913-0736-46e3-9b1a-8cee8a4db55f", "7fa77e86-59b2-413b-b743-809bbacf8116", "ff079737-2ad9-47de-9118-911dd7b97283", "6a3055c0-d2c2-4437-8919-40802ca0173b", "503a44bf-4441-4388-a253-b39d7fdc801f"]}]}, {"BlockType": "LINE", "Confidence": 99.98030853271484, "Text": "the corrected weight as stated", "Geometry": {"BoundingBox": {"Width": 0.14533431828022003, "Height": 0.008183035999536514, "Left": 0.707314670085907, "Top": 0.39328068494796753}, "Polygon": [{"X": 0.7073147296905518, "Y": 0.39328277111053467}, {"X": 0.8526489734649658, "Y": 0.39328068494796753}, {"X": 0.8526488542556763, "Y": 0.4014616012573242}, {"X": 0.707314670085907, "Y": 0.40146371722221375}]}, "Id": "990a8638-052c-4f91-8005-c7c2c323e570", "Relationships": [{"Type": "CHILD", "Ids": ["f06626d7-623e-428b-8708-4ecf2a6dee07", "a50b6dfe-8198-4d4b-afec-5184ce219291", "4a7a5536-e900-4511-99fc-5e1c281c6147", "d5e2341b-ac9d-4cf9-845a-1ce0cb6d2b91", "6f9d83b5-b262-425d-b0fa-977206015d21"]}]}, {"BlockType": "LINE", "Confidence": 99.91458129882812, "Text": "is the true and accurate", "Geometry": {"BoundingBox": {"Width": 0.11372467130422592, "Height": 0.00710035115480423, "Left": 0.7076213955879211, "Top": 0.40173888206481934}, "Polygon": [{"X": 0.7076214551925659, "Y": 0.40174052119255066}, {"X": 0.8213460445404053, "Y": 0.40173888206481934}, {"X": 0.8213459253311157, "Y": 0.40883758664131165}, {"X": 0.7076213955879211, "Y": 0.40883922576904297}]}, "Id": "b633bdd0-96b9-4c94-97fd-34950431fac3", "Relationships": [{"Type": "CHILD", "Ids": ["f67a35cd-5ffa-477f-9b3c-244539681a8b", "fc8c1713-5d0a-4d6e-ab41-a12f32d1d01a", "89dec979-92d8-44b5-b2bd-4bbffe25bbac", "544a0692-3595-46e3-bd16-b4a1927867b2", "bca57e99-5a4d-41de-8d60-75556466424a"]}]}, {"BlockType": "LINE", "Confidence": 99.90074920654297, "Text": "Declared Weight: 1148", "Geometry": {"BoundingBox": {"Width": 0.10963373631238937, "Height": 0.008119854144752026, "Left": 0.16241343319416046, "Top": 0.41033288836479187}, "Polygon": [{"X": 0.16241347789764404, "Y": 0.4103344976902008}, {"X": 0.27204716205596924, "Y": 0.41033288836479187}, {"X": 0.27204710245132446, "Y": 0.41845113039016724}, {"X": 0.16241343319416046, "Y": 0.41845273971557617}]}, "Id": "e294a0b8-9700-4663-b065-bb362ab255a9", "Relationships": [{"Type": "CHILD", "Ids": ["055d9ff7-bcd1-4fd5-b2c2-f93b605508aa", "d760f0ba-354c-422d-a989-b8bfafd9b0e3", "d5584019-5f32-4066-84f4-1402dcfd36ed"]}]}, {"BlockType": "LINE", "Confidence": 99.9280014038086, "Text": "weight of the shipment", "Geometry": {"BoundingBox": {"Width": 0.1111871749162674, "Height": 0.008168041706085205, "Left": 0.7069318294525146, "Top": 0.4102623462677002}, "Polygon": [{"X": 0.7069319486618042, "Y": 0.4102639853954315}, {"X": 0.8181189894676208, "Y": 0.4102623462677002}, {"X": 0.8181188702583313, "Y": 0.4184287488460541}, {"X": 0.7069318294525146, "Y": 0.4184303879737854}]}, "Id": "ae0a56ba-4c7e-4ec6-912f-f0e85489f8b5", "Relationships": [{"Type": "CHILD", "Ids": ["b1630103-94a0-4468-ae32-0befd7f98a17", "211f2e60-ed73-46a2-bd53-53356417abab", "ed5d29c0-6e93-4ae0-9acc-0d56ae6b9bdb", "d75dcaa8-1ea6-4b2b-96f6-637e9cd39729"]}]}, {"BlockType": "LINE", "Confidence": 99.86766815185547, "Text": "Corrected Weight: 1425", "Geometry": {"BoundingBox": {"Width": 0.11429888010025024, "Height": 0.008122419007122517, "Left": 0.16247409582138062, "Top": 0.41887912154197693}, "Polygon": [{"X": 0.1624741405248642, "Y": 0.41888079047203064}, {"X": 0.27677297592163086, "Y": 0.41887912154197693}, {"X": 0.2767729163169861, "Y": 0.4269998371601105}, {"X": 0.16247409582138062, "Y": 0.4270015358924866}]}, "Id": "e1393900-8eea-4ceb-8a5c-f49a25303123", "Relationships": [{"Type": "CHILD", "Ids": ["3402fce0-da81-4c00-bf25-fb76344e0c71", "8af18e9c-1ec1-4bb4-9f26-692535784437", "62feb1e3-3c76-4873-b3de-608586f9a94f"]}]}, {"BlockType": "LINE", "Confidence": 99.7120132446289, "Text": "HU", "Geometry": {"BoundingBox": {"Width": 0.017055867239832878, "Height": 0.006777491886168718, "Left": 0.16441786289215088, "Top": 0.4541013836860657}, "Polygon": [{"X": 0.16441790759563446, "Y": 0.45410165190696716}, {"X": 0.1814737319946289, "Y": 0.4541013836860657}, {"X": 0.18147367238998413, "Y": 0.4608786106109619}, {"X": 0.16441786289215088, "Y": 0.4608788788318634}]}, "Id": "148be307-d547-4a0e-859f-28fa01bf9efb", "Relationships": [{"Type": "CHILD", "Ids": ["c0e0b732-3cb4-4875-9002-fb37a5b6918b"]}]}, {"BlockType": "LINE", "Confidence": 99.89058685302734, "Text": "<PERSON>an Date", "Geometry": {"BoundingBox": {"Width": 0.0600147508084774, "Height": 0.0071591404266655445, "Left": 0.19829589128494263, "Top": 0.45388081669807434}, "Polygon": [{"X": 0.1982959508895874, "Y": 0.45388174057006836}, {"X": 0.2583106458187103, "Y": 0.45388081669807434}, {"X": 0.25831058621406555, "Y": 0.4610390365123749}, {"X": 0.19829589128494263, "Y": 0.4610399603843689}]}, "Id": "9161f6b5-ebc3-4a05-8c1a-bda4c68a99fb", "Relationships": [{"Type": "CHILD", "Ids": ["fe2a6864-1f4c-4561-acc8-8aebf3333573", "90255238-c825-4b1d-a1ed-5e7aa7c14b94"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.02890188619494438, "Height": 0.007359205279499292, "Left": 0.2874515652656555, "Top": 0.4534839987754822}, "Polygon": [{"X": 0.2874516248703003, "Y": 0.453484445810318}, {"X": 0.316353440284729, "Y": 0.4534839987754822}, {"X": 0.31635338068008423, "Y": 0.4608427584171295}, {"X": 0.2874515652656555, "Y": 0.46084320545196533}]}, "Id": "d5ac6c30-3148-4e3c-91b3-09154ccb2814", "Relationships": [{"Type": "CHILD", "Ids": ["6d42f625-e749-4efc-8c44-39486cbe611e"]}]}, {"BlockType": "LINE", "Confidence": 99.78605651855469, "Text": "Weight Terminal", "Geometry": {"BoundingBox": {"Width": 0.09505319595336914, "Height": 0.009380130097270012, "Left": 0.3337138891220093, "Top": 0.45351311564445496}, "Polygon": [{"X": 0.33371397852897644, "Y": 0.45351457595825195}, {"X": 0.4287670850753784, "Y": 0.45351311564445496}, {"X": 0.42876699566841125, "Y": 0.4628917872905731}, {"X": 0.3337138891220093, "Y": 0.4628932476043701}]}, "Id": "6d6944cf-8b96-4ea5-8b4b-177332d50841", "Relationships": [{"Type": "CHILD", "Ids": ["4c3a5826-fa70-4191-b9a3-b29a25743090", "a216b4b0-4019-4f82-b4ec-d9f9c0a207de"]}]}, {"BlockType": "LINE", "Confidence": 99.86088562011719, "Text": "Pro Number", "Geometry": {"BoundingBox": {"Width": 0.0677749365568161, "Height": 0.007399872411042452, "Left": 0.46933484077453613, "Top": 0.4536535441875458}, "Polygon": [{"X": 0.4693349301815033, "Y": 0.45365455746650696}, {"X": 0.5371097922325134, "Y": 0.4536535441875458}, {"X": 0.5371096730232239, "Y": 0.4610523581504822}, {"X": 0.46933484077453613, "Y": 0.46105340123176575}]}, "Id": "ee5ee496-bb0d-429b-9a04-a8ee237407af", "Relationships": [{"Type": "CHILD", "Ids": ["0e5db8ff-d722-42eb-87b2-399868c63a06", "b3320a51-121e-437c-b1db-b25fcce043f9"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "33136199", "Geometry": {"BoundingBox": {"Width": 0.058709219098091125, "Height": 0.007329114247113466, "Left": 0.1687587946653366, "Top": 0.4650166928768158}, "Polygon": [{"X": 0.1687588393688202, "Y": 0.4650176167488098}, {"X": 0.22746801376342773, "Y": 0.4650166928768158}, {"X": 0.22746795415878296, "Y": 0.47234490513801575}, {"X": 0.1687587946653366, "Y": 0.47234582901000977}]}, "Id": "fb694827-7696-4b3e-94c5-1e866f01149e", "Relationships": [{"Type": "CHILD", "Ids": ["53638deb-dd94-4c13-8c9f-6bd580b0118d"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.0687561109662056, "Height": 0.008462921716272831, "Left": 0.24354542791843414, "Top": 0.46471866965293884}, "Polygon": [{"X": 0.24354548752307892, "Y": 0.4647197127342224}, {"X": 0.31230154633522034, "Y": 0.46471866965293884}, {"X": 0.3123014569282532, "Y": 0.47318050265312195}, {"X": 0.24354542791843414, "Y": 0.4731815755367279}]}, "Id": "418d900d-97fb-4dc1-9766-86fb762732c5", "Relationships": [{"Type": "CHILD", "Ids": ["cc0455f0-796e-4b02-a4ab-8a6eed76760f"]}]}, {"BlockType": "LINE", "Confidence": 99.89178466796875, "Text": "00:02 880", "Geometry": {"BoundingBox": {"Width": 0.06711716204881668, "Height": 0.007216433063149452, "Left": 0.3340480327606201, "Top": 0.4650701880455017}, "Polygon": [{"X": 0.3340480923652649, "Y": 0.4650712311267853}, {"X": 0.4011651873588562, "Y": 0.4650701880455017}, {"X": 0.4011651277542114, "Y": 0.47228556871414185}, {"X": 0.3340480327606201, "Y": 0.4722866117954254}]}, "Id": "ab111f2d-ed00-41e0-8183-f06f116a240a", "Relationships": [{"Type": "CHILD", "Ids": ["35ce494a-f8b2-4050-8882-5cfd16060619", "fc11632e-c323-4aba-bb57-67f0553a72c4"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "463", "Geometry": {"BoundingBox": {"Width": 0.021867085248231888, "Height": 0.00682410504668951, "Left": 0.43186819553375244, "Top": 0.46527284383773804}, "Polygon": [{"X": 0.4318682551383972, "Y": 0.4652732014656067}, {"X": 0.45373526215553284, "Y": 0.46527284383773804}, {"X": 0.45373520255088806, "Y": 0.47209662199020386}, {"X": 0.43186819553375244, "Y": 0.4720969498157501}]}, "Id": "793c2598-b0f8-415f-aed1-8b1bf34da92a", "Relationships": [{"Type": "CHILD", "Ids": ["83076bab-274b-4fc1-8450-815ee36280bd"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "227407699", "Geometry": {"BoundingBox": {"Width": 0.060560133308172226, "Height": 0.007165390066802502, "Left": 0.5148974657058716, "Top": 0.4650382697582245}, "Polygon": [{"X": 0.5148975253105164, "Y": 0.4650391936302185}, {"X": 0.5754575729370117, "Y": 0.4650382697582245}, {"X": 0.5754575133323669, "Y": 0.47220271825790405}, {"X": 0.5148974657058716, "Y": 0.47220367193222046}]}, "Id": "fd37b166-0c67-4ccd-9989-50ff4e98ebe4", "Relationships": [{"Type": "CHILD", "Ids": ["8fe26dd1-6909-409a-b566-e69e92f6b62a"]}]}, {"BlockType": "LINE", "Confidence": 99.873046875, "Text": "33136200", "Geometry": {"BoundingBox": {"Width": 0.058386050164699554, "Height": 0.006965055596083403, "Left": 0.16896452009677887, "Top": 0.47567445039749146}, "Polygon": [{"X": 0.16896456480026245, "Y": 0.4756753742694855}, {"X": 0.22735057771205902, "Y": 0.47567445039749146}, {"X": 0.22735051810741425, "Y": 0.4826385974884033}, {"X": 0.16896452009677887, "Y": 0.48263952136039734}]}, "Id": "91b14752-e157-4b10-8f83-95f034be449e", "Relationships": [{"Type": "CHILD", "Ids": ["777e8d36-a227-4588-802d-219b3471010f"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.06862763315439224, "Height": 0.008570958860218525, "Left": 0.24351204931735992, "Top": 0.47528213262557983}, "Polygon": [{"X": 0.2435121089220047, "Y": 0.4752832055091858}, {"X": 0.31213968992233276, "Y": 0.47528213262557983}, {"X": 0.3121396005153656, "Y": 0.48385199904441833}, {"X": 0.24351204931735992, "Y": 0.4838530719280243}]}, "Id": "33f8ee14-5539-4397-82fc-fd49cf1ac311", "Relationships": [{"Type": "CHILD", "Ids": ["49ec7cb3-00f8-492d-bb75-a2c07c2ee36c"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "00:03", "Geometry": {"BoundingBox": {"Width": 0.033893387764692307, "Height": 0.0071488418616354465, "Left": 0.3341608941555023, "Top": 0.4757031202316284}, "Polygon": [{"X": 0.3341609537601471, "Y": 0.475703626871109}, {"X": 0.3680543005466461, "Y": 0.4757031202316284}, {"X": 0.36805421113967896, "Y": 0.48285141587257385}, {"X": 0.3341608941555023, "Y": 0.48285195231437683}]}, "Id": "9f9181f3-1b3c-4caa-a6e7-39819d15fc9f", "Relationships": [{"Type": "CHILD", "Ids": ["a085ce49-6723-4075-a76b-70c5019e619f"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "545", "Geometry": {"BoundingBox": {"Width": 0.02147347666323185, "Height": 0.006957860663533211, "Left": 0.3794381320476532, "Top": 0.4757300317287445}, "Polygon": [{"X": 0.379438191652298, "Y": 0.47573035955429077}, {"X": 0.4009115993976593, "Y": 0.4757300317287445}, {"X": 0.4009115397930145, "Y": 0.4826875627040863}, {"X": 0.3794381320476532, "Y": 0.48268789052963257}]}, "Id": "84e59017-2916-418c-9aed-588fff22249f", "Relationships": [{"Type": "CHILD", "Ids": ["9630ad70-11be-4b68-a0ff-8bb3859cb24a"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "463", "Geometry": {"BoundingBox": {"Width": 0.021909311413764954, "Height": 0.006979272235184908, "Left": 0.43184787034988403, "Top": 0.4756060838699341}, "Polygon": [{"X": 0.4318479299545288, "Y": 0.47560641169548035}, {"X": 0.4537571966648102, "Y": 0.4756060838699341}, {"X": 0.453757107257843, "Y": 0.48258501291275024}, {"X": 0.43184787034988403, "Y": 0.4825853407382965}]}, "Id": "c64d15c9-5a81-48e5-ac3d-02a685af294c", "Relationships": [{"Type": "CHILD", "Ids": ["f0317bcf-def8-43ee-ae8b-56e860d6c18b"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "227407699", "Geometry": {"BoundingBox": {"Width": 0.06058836355805397, "Height": 0.006870943587273359, "Left": 0.514869749546051, "Top": 0.4756676256656647}, "Polygon": [{"X": 0.5148698091506958, "Y": 0.4756685793399811}, {"X": 0.5754581093788147, "Y": 0.4756676256656647}, {"X": 0.5754579901695251, "Y": 0.4825376272201538}, {"X": 0.514869749546051, "Y": 0.4825385808944702}]}, "Id": "f2c82da0-d99d-49ba-a23a-b4146935f2f8", "Relationships": [{"Type": "CHILD", "Ids": ["259175cc-94e6-4bdb-9a5b-9bf04935427e"]}]}, {"BlockType": "LINE", "Confidence": 99.83238983154297, "Text": "3", "Geometry": {"BoundingBox": {"Width": 0.0063613103702664375, "Height": 0.006829916033893824, "Left": 0.16887255012989044, "Top": 0.48619070649147034}, "Polygon": [{"X": 0.16887259483337402, "Y": 0.4861908257007599}, {"X": 0.175233855843544, "Y": 0.48619070649147034}, {"X": 0.17523381114006042, "Y": 0.49302053451538086}, {"X": 0.16887255012989044, "Y": 0.493020623922348}]}, "Id": "346f4729-b077-4df6-a1d4-0eb68a5427af", "Relationships": [{"Type": "CHILD", "Ids": ["f7d7d58a-a7a1-4847-8fdd-36ab3d819ae9"]}]}, {"BlockType": "LINE", "Confidence": 99.68511199951172, "Text": "4", "Geometry": {"BoundingBox": {"Width": 0.0070258076302707195, "Height": 0.006691001355648041, "Left": 0.16855113208293915, "Top": 0.49654772877693176}, "Polygon": [{"X": 0.16855117678642273, "Y": 0.4965478479862213}, {"X": 0.17557694017887115, "Y": 0.49654772877693176}, {"X": 0.17557689547538757, "Y": 0.5032386183738708}, {"X": 0.16855113208293915, "Y": 0.5032387375831604}]}, "Id": "a240eeab-5158-42e1-8200-2036885d1646", "Relationships": [{"Type": "CHILD", "Ids": ["876eb657-e5fd-42f7-9ee9-ca643aaa2e7e"]}]}, {"BlockType": "LINE", "Confidence": 99.79252624511719, "Text": "5", "Geometry": {"BoundingBox": {"Width": 0.006197942886501551, "Height": 0.006747648119926453, "Left": 0.1691218912601471, "Top": 0.5072013139724731}, "Polygon": [{"X": 0.16912193596363068, "Y": 0.5072013735771179}, {"X": 0.1753198355436325, "Y": 0.5072013139724731}, {"X": 0.17531979084014893, "Y": 0.5139488577842712}, {"X": 0.1691218912601471, "Y": 0.5139489769935608}]}, "Id": "cde9d417-2aa2-4408-b4cc-d808564e6d74", "Relationships": [{"Type": "CHILD", "Ids": ["87035eee-3fe9-4dd9-aaf4-8c70b00ce517"]}]}, {"BlockType": "LINE", "Confidence": 99.71281433105469, "Text": "6", "Geometry": {"BoundingBox": {"Width": 0.00636710412800312, "Height": 0.0069289072416722775, "Left": 0.16888143122196198, "Top": 0.5180972814559937}, "Polygon": [{"X": 0.16888147592544556, "Y": 0.5180974006652832}, {"X": 0.17524853348731995, "Y": 0.5180972814559937}, {"X": 0.17524848878383636, "Y": 0.5250260829925537}, {"X": 0.16888143122196198, "Y": 0.5250262022018433}]}, "Id": "7100874e-040e-4fcd-952e-04fa4b2ae5c5", "Relationships": [{"Type": "CHILD", "Ids": ["ed60da17-2326-4c17-be69-53721c019af7"]}]}, {"BlockType": "LINE", "Confidence": 99.88201141357422, "Text": "7", "Geometry": {"BoundingBox": {"Width": 0.006649814546108246, "Height": 0.006885642185807228, "Left": 0.16893725097179413, "Top": 0.5284852385520935}, "Polygon": [{"X": 0.1689372956752777, "Y": 0.5284853577613831}, {"X": 0.17558707296848297, "Y": 0.5284852385520935}, {"X": 0.1755870282649994, "Y": 0.5353707671165466}, {"X": 0.16893725097179413, "Y": 0.5353708863258362}]}, "Id": "9f140819-7dfb-484d-bcdd-67fdc668a25d", "Relationships": [{"Type": "CHILD", "Ids": ["5eafdb51-889e-488f-a85a-c185b211e304"]}]}, {"BlockType": "LINE", "Confidence": 99.67375183105469, "Text": "8", "Geometry": {"BoundingBox": {"Width": 0.006595203652977943, "Height": 0.006734260357916355, "Left": 0.1688777208328247, "Top": 0.5392356514930725}, "Polygon": [{"X": 0.1688777655363083, "Y": 0.5392357707023621}, {"X": 0.1754729151725769, "Y": 0.5392356514930725}, {"X": 0.17547287046909332, "Y": 0.5459697842597961}, {"X": 0.1688777208328247, "Y": 0.5459699034690857}]}, "Id": "b556ccd8-b006-448d-8444-f303a72a3727", "Relationships": [{"Type": "CHILD", "Ids": ["a57b7820-eb7c-4286-8100-06bceb49b898"]}]}, {"BlockType": "LINE", "Confidence": 99.60299682617188, "Text": "9", "Geometry": {"BoundingBox": {"Width": 0.006715078372508287, "Height": 0.007085907738655806, "Left": 0.16874417662620544, "Top": 0.5495967268943787}, "Polygon": [{"X": 0.16874422132968903, "Y": 0.5495968461036682}, {"X": 0.17545926570892334, "Y": 0.5495967268943787}, {"X": 0.17545920610427856, "Y": 0.5566825270652771}, {"X": 0.16874417662620544, "Y": 0.5566826462745667}]}, "Id": "0ab2363c-6291-4cb0-9da3-a341697347b2", "Relationships": [{"Type": "CHILD", "Ids": ["36598866-e608-435b-aae9-d7fa87613397"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "10", "Geometry": {"BoundingBox": {"Width": 0.012692058458924294, "Height": 0.006892743520438671, "Left": 0.1696051061153412, "Top": 0.5602093935012817}, "Polygon": [{"X": 0.16960515081882477, "Y": 0.5602095723152161}, {"X": 0.18229715526103973, "Y": 0.5602093935012817}, {"X": 0.18229711055755615, "Y": 0.5671018958091736}, {"X": 0.1696051061153412, "Y": 0.5671021342277527}]}, "Id": "7ccc10b4-44a2-446e-94bf-f3d2588a1e06", "Relationships": [{"Type": "CHILD", "Ids": ["612a3c93-6aaa-497a-b2bb-350e08f97aca"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.027860941365361214, "Height": 0.007223647087812424, "Left": 0.16397152841091156, "Top": 0.5709370970726013}, "Polygon": [{"X": 0.16397157311439514, "Y": 0.5709375739097595}, {"X": 0.19183246791362762, "Y": 0.5709370970726013}, {"X": 0.19183242321014404, "Y": 0.5781602263450623}, {"X": 0.16397152841091156, "Y": 0.5781607031822205}]}, "Id": "d76818e8-08ef-469c-b7c7-81c01601231f", "Relationships": [{"Type": "CHILD", "Ids": ["615338f4-c173-4f98-87e7-f8dd7c800562"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "1425", "Geometry": {"BoundingBox": {"Width": 0.02627403661608696, "Height": 0.006929267197847366, "Left": 0.33493518829345703, "Top": 0.5713433623313904}, "Polygon": [{"X": 0.3349352478981018, "Y": 0.5713438391685486}, {"X": 0.3612092137336731, "Y": 0.5713433623313904}, {"X": 0.3612091541290283, "Y": 0.5782721638679504}, {"X": 0.33493518829345703, "Y": 0.5782726407051086}]}, "Id": "10a5a3f8-44ca-435e-bd15-cd8d97be41d6", "Relationships": [{"Type": "CHILD", "Ids": ["c8763038-dc17-452b-897b-24c903ca42c3"]}]}, {"BlockType": "LINE", "Confidence": 99.75875091552734, "Text": "Page 3/3", "Geometry": {"BoundingBox": {"Width": 0.05226879566907883, "Height": 0.010070810094475746, "Left": 0.8169759511947632, "Top": 0.9619084596633911}, "Polygon": [{"X": 0.8169761300086975, "Y": 0.9619096517562866}, {"X": 0.8692447543144226, "Y": 0.9619084596633911}, {"X": 0.8692446351051331, "Y": 0.9719780087471008}, {"X": 0.8169759511947632, "Y": 0.9719792604446411}]}, "Id": "e30a6413-fbce-4fba-85f7-0eeafb022938", "Relationships": [{"Type": "CHILD", "Ids": ["5d5a5624-06ce-4d18-99a5-7ca823f1b2e8", "e219191e-be1d-4440-901a-e2127fe51f88"]}]}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Central", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047523383051157, "Height": 0.009486476890742779, "Left": 0.05718903988599777, "Top": 0.05624702572822571}, "Polygon": [{"X": 0.05718908831477165, "Y": 0.05624745413661003}, {"X": 0.10471241921186447, "Y": 0.05624702572822571}, {"X": 0.10471236705780029, "Y": 0.06573306769132614}, {"X": 0.05718903988599777, "Y": 0.06573350727558136}]}, "Id": "d78db949-916f-430f-b254-fd71b7ac7dd1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Transport", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06371095031499863, "Height": 0.011445809155702591, "Left": 0.10830892622470856, "Top": 0.056378427892923355}, "Polygon": [{"X": 0.10830899327993393, "Y": 0.05637900158762932}, {"X": 0.17201988399028778, "Y": 0.056378427892923355}, {"X": 0.1720198094844818, "Y": 0.06782364845275879}, {"X": 0.10830892622470856, "Y": 0.06782423704862595}]}, "Id": "ebe74301-bcc7-4c19-95e1-a5609c0aaa5d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "08/31/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0731736347079277, "Height": 0.009412871673703194, "Left": 0.8657994270324707, "Top": 0.05629698932170868}, "Polygon": [{"X": 0.865799605846405, "Y": 0.05629764869809151}, {"X": 0.938973069190979, "Y": 0.05629698932170868}, {"X": 0.9389729499816895, "Y": 0.065709188580513}, {"X": 0.8657994270324707, "Y": 0.06570985913276672}]}, "Id": "2aa9e48b-51bb-42db-bc4a-04695d1d8c03"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "PRO#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043377768248319626, "Height": 0.009395409375429153, "Left": 0.057394713163375854, "Top": 0.07146386057138443}, "Polygon": [{"X": 0.057394761592149734, "Y": 0.07146426290273666}, {"X": 0.10077248513698578, "Y": 0.07146386057138443}, {"X": 0.1007724329829216, "Y": 0.08085886389017105}, {"X": 0.057394713163375854, "Y": 0.08085927367210388}]}, "Id": "e81cb7b0-865c-4e21-a6ea-b0de9869f368"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07391535490751266, "Height": 0.009347072802484035, "Left": 0.10520923137664795, "Top": 0.07145050168037415}, "Polygon": [{"X": 0.10520928353071213, "Y": 0.07145117968320847}, {"X": 0.17912457883358002, "Y": 0.07145050168037415}, {"X": 0.17912451922893524, "Y": 0.08079687505960464}, {"X": 0.10520923137664795, "Y": 0.08079757541418076}]}, "Id": "6373ea1f-2b44-4b7f-a6d5-52353dd398e9"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04729338735342026, "Height": 0.011711383238434792, "Left": 0.8079518675804138, "Top": 0.07136216014623642}, "Polygon": [{"X": 0.8079520463943481, "Y": 0.07136259227991104}, {"X": 0.8552452921867371, "Y": 0.07136216014623642}, {"X": 0.8552451133728027, "Y": 0.08307309448719025}, {"X": 0.8079518675804138, "Y": 0.08307354152202606}]}, "Id": "7dd14a02-4f39-4f44-961e-64b52f241b2f"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010260049253702164, "Height": 0.00927706342190504, "Left": 0.8587119579315186, "Top": 0.07156898826360703}, "Polygon": [{"X": 0.8587120771408081, "Y": 0.07156908512115479}, {"X": 0.8689720034599304, "Y": 0.07156898826360703}, {"X": 0.8689718842506409, "Y": 0.08084595203399658}, {"X": 0.8587119579315186, "Y": 0.08084604889154434}]}, "Id": "a66e0914-b097-4c67-ac15-54ff67fa53b7"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06620460003614426, "Height": 0.011348029598593712, "Left": 0.8727492690086365, "Top": 0.07158870249986649}, "Polygon": [{"X": 0.8727494478225708, "Y": 0.07158931344747543}, {"X": 0.9389538764953613, "Y": 0.07158870249986649}, {"X": 0.938953697681427, "Y": 0.0829361081123352}, {"X": 0.8727492690086365, "Y": 0.08293673396110535}]}, "Id": "d74be576-0815-486a-bbde-16b7945b74b8"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "CENTRAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0677325576543808, "Height": 0.01386458333581686, "Left": 0.4383789896965027, "Top": 0.19190871715545654}, "Polygon": [{"X": 0.43837910890579224, "Y": 0.19190946221351624}, {"X": 0.5061115622520447, "Y": 0.19190871715545654}, {"X": 0.5061113834381104, "Y": 0.2057725191116333}, {"X": 0.4383789896965027, "Y": 0.20577329397201538}]}, "Id": "41ba07ad-b466-4f84-96cf-86fceca78573"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09249085932970047, "Height": 0.013988036662340164, "Left": 0.508831262588501, "Top": 0.1918865293264389}, "Polygon": [{"X": 0.5088314414024353, "Y": 0.19188755750656128}, {"X": 0.6013221144676208, "Y": 0.1918865293264389}, {"X": 0.6013219952583313, "Y": 0.205873504281044}, {"X": 0.508831262588501, "Y": 0.20587456226348877}]}, "Id": "6ef50ba4-54ca-4fd6-bcef-ad615debe06c"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "12225", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035280805081129074, "Height": 0.00855326559394598, "Left": 0.4399400055408478, "Top": 0.2120484709739685}, "Polygon": [{"X": 0.43994009494781494, "Y": 0.21204887330532074}, {"X": 0.47522079944610596, "Y": 0.2120484709739685}, {"X": 0.4752207100391388, "Y": 0.22060133516788483}, {"X": 0.4399400055408478, "Y": 0.22060173749923706}]}, "Id": "75023b51-5a0f-4cc4-87d9-70790fe4a306"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05482405051589012, "Height": 0.010645987465977669, "Left": 0.47985780239105225, "Top": 0.21199145913124084}, "Polygon": [{"X": 0.4798579216003418, "Y": 0.211992084980011}, {"X": 0.5346818566322327, "Y": 0.21199145913124084}, {"X": 0.5346817374229431, "Y": 0.22263680398464203}, {"X": 0.47985780239105225, "Y": 0.22263744473457336}]}, "Id": "ed79f8f6-0232-4244-a106-79810a9cb3fb"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Road", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02949291281402111, "Height": 0.008774659596383572, "Left": 0.5392704606056213, "Top": 0.2120930254459381}, "Polygon": [{"X": 0.5392705202102661, "Y": 0.21209336817264557}, {"X": 0.5687633752822876, "Y": 0.2120930254459381}, {"X": 0.568763256072998, "Y": 0.2208673506975174}, {"X": 0.5392704606056213, "Y": 0.22086769342422485}]}, "Id": "52999710-ba42-4a93-a4b8-1c3eb4658b41"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04800622910261154, "Height": 0.010209893807768822, "Left": 0.44664397835731506, "Top": 0.2257819026708603}, "Polygon": [{"X": 0.4466440677642822, "Y": 0.22578246891498566}, {"X": 0.4946502149105072, "Y": 0.2257819026708603}, {"X": 0.49465009570121765, "Y": 0.2359912246465683}, {"X": 0.44664397835731506, "Y": 0.23599180579185486}]}, "Id": "5f4a9619-e0c9-4d2d-8a37-66289e768dc1"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "MI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0159319918602705, "Height": 0.00851416401565075, "Left": 0.49934086203575134, "Top": 0.22570796310901642}, "Polygon": [{"X": 0.4993409514427185, "Y": 0.22570815682411194}, {"X": 0.515272855758667, "Y": 0.22570796310901642}, {"X": 0.5152727365493774, "Y": 0.234221950173378}, {"X": 0.49934086203575134, "Y": 0.23422212898731232}]}, "Id": "d41eae6a-517f-4028-a265-c2eb7e542547"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "48089", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036994144320487976, "Height": 0.008614152669906616, "Left": 0.5234261155128479, "Top": 0.22568704187870026}, "Polygon": [{"X": 0.5234262347221375, "Y": 0.22568747401237488}, {"X": 0.5604202747344971, "Y": 0.22568704187870026}, {"X": 0.5604201555252075, "Y": 0.23430076241493225}, {"X": 0.5234261155128479, "Y": 0.23430119454860687}]}, "Id": "bf60582b-883f-4ca4-8aae-4f7153005b8a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CERTIFIED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07389842718839645, "Height": 0.008876699954271317, "Left": 0.39221125841140747, "Top": 0.29037290811538696}, "Polygon": [{"X": 0.39221134781837463, "Y": 0.290373831987381}, {"X": 0.4661096930503845, "Y": 0.29037290811538696}, {"X": 0.46610960364341736, "Y": 0.2992486357688904}, {"X": 0.39221125841140747, "Y": 0.2992495894432068}]}, "Id": "41973bea-5fd3-45af-993a-2b1352d33abc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WEIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05728071182966232, "Height": 0.009197513572871685, "Left": 0.47053226828575134, "Top": 0.2900329828262329}, "Polygon": [{"X": 0.4705323576927185, "Y": 0.2900336980819702}, {"X": 0.5278129577636719, "Y": 0.2900329828262329}, {"X": 0.5278128981590271, "Y": 0.29922977089881897}, {"X": 0.47053226828575134, "Y": 0.2992304861545563}]}, "Id": "13eb7bdb-8387-44dc-a947-7b82d8602919"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CERTIFICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08967792242765427, "Height": 0.008884044364094734, "Left": 0.5327039361000061, "Top": 0.2903496026992798}, "Polygon": [{"X": 0.5327039957046509, "Y": 0.2903507351875305}, {"X": 0.622381865978241, "Y": 0.2903496026992798}, {"X": 0.6223817467689514, "Y": 0.29923248291015625}, {"X": 0.5327039361000061, "Y": 0.29923364520072937}]}, "Id": "c10b2f24-f6e6-485f-a7d8-3632e0a9ebc7"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Pro", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021388905122876167, "Height": 0.008751227520406246, "Left": 0.15433162450790405, "Top": 0.3165864050388336}, "Polygon": [{"X": 0.15433166921138763, "Y": 0.3165866732597351}, {"X": 0.17572052776813507, "Y": 0.3165864050388336}, {"X": 0.1757204681634903, "Y": 0.32533735036849976}, {"X": 0.15433162450790405, "Y": 0.32533761858940125}]}, "Id": "0cec9bab-937c-427f-a005-3a715dda9a81"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05895177647471428, "Height": 0.009268013760447502, "Left": 0.18463827669620514, "Top": 0.3161730170249939}, "Polygon": [{"X": 0.18463833630084991, "Y": 0.316173791885376}, {"X": 0.24359004199504852, "Y": 0.3161730170249939}, {"X": 0.24358998239040375, "Y": 0.32544025778770447}, {"X": 0.18463827669620514, "Y": 0.32544103264808655}]}, "Id": "6a996581-72db-4e59-b32f-10ca5c1a7cf1"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07552546262741089, "Height": 0.009020472876727581, "Left": 0.2525319457054138, "Top": 0.31638628244400024}, "Polygon": [{"X": 0.252532035112381, "Y": 0.3163872957229614}, {"X": 0.3280574083328247, "Y": 0.31638628244400024}, {"X": 0.32805734872817993, "Y": 0.3254057765007019}, {"X": 0.2525319457054138, "Y": 0.3254067599773407}]}, "Id": "8d21eb92-c042-45d6-803a-bea2719fef69"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01808108016848564, "Height": 0.009077761322259903, "Left": 0.6965249180793762, "Top": 0.3161868751049042}, "Polygon": [{"X": 0.6965250372886658, "Y": 0.3161871135234833}, {"X": 0.714605987071991, "Y": 0.3161868751049042}, {"X": 0.7146058678627014, "Y": 0.3252643942832947}, {"X": 0.6965249180793762, "Y": 0.3252646327018738}]}, "Id": "ea85aa82-3143-447f-b424-8ecbece5cac7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014402875676751137, "Height": 0.009438899345695972, "Left": 0.7203461527824402, "Top": 0.31584814190864563}, "Polygon": [{"X": 0.7203463315963745, "Y": 0.31584832072257996}, {"X": 0.7347490787506104, "Y": 0.31584814190864563}, {"X": 0.734748899936676, "Y": 0.3252868354320526}, {"X": 0.7203461527824402, "Y": 0.3252870440483093}]}, "Id": "ceaa7b8d-40fd-4af0-94e1-813b6428f451"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Lading", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04320087283849716, "Height": 0.011417851783335209, "Left": 0.7399371862411499, "Top": 0.3161904215812683}, "Polygon": [{"X": 0.7399373650550842, "Y": 0.31619101762771606}, {"X": 0.78313809633255, "Y": 0.3161904215812683}, {"X": 0.7831379175186157, "Y": 0.3276077210903168}, {"X": 0.7399371862411499, "Y": 0.32760828733444214}]}, "Id": "d670fe7f-9038-4db4-8e6d-295192e77eda"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03578847274184227, "Height": 0.008917699567973614, "Left": 0.15446539223194122, "Top": 0.3295671343803406}, "Polygon": [{"X": 0.154465451836586, "Y": 0.3295676112174988}, {"X": 0.1902538686990738, "Y": 0.3295671343803406}, {"X": 0.19025380909442902, "Y": 0.33848437666893005}, {"X": 0.15446539223194122, "Y": 0.33848485350608826}]}, "Id": "a7236181-61b6-428d-acc8-b67fc871a14e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07925864309072495, "Height": 0.010597198270261288, "Left": 0.19703388214111328, "Top": 0.32907816767692566}, "Polygon": [{"X": 0.19703395664691925, "Y": 0.32907921075820923}, {"X": 0.2762925326824188, "Y": 0.32907816767692566}, {"X": 0.27629244327545166, "Y": 0.33967429399490356}, {"X": 0.19703388214111328, "Y": 0.33967533707618713}]}, "Id": "bf4cc605-81ff-48b4-b480-5dea5b495ef8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DIAMOND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04778068885207176, "Height": 0.006365248467773199, "Left": 0.16249734163284302, "Top": 0.368204265832901}, "Polygon": [{"X": 0.1624973714351654, "Y": 0.3682049512863159}, {"X": 0.21027801930904388, "Y": 0.368204265832901}, {"X": 0.2102779746055603, "Y": 0.3745688498020172}, {"X": 0.16249734163284302, "Y": 0.37456953525543213}]}, "Id": "e935cfd0-a2d0-41d8-879a-02f4c1108261"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "ENVELOPE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05010327696800232, "Height": 0.00638725608587265, "Left": 0.21331366896629333, "Top": 0.3682202994823456}, "Polygon": [{"X": 0.21331371366977692, "Y": 0.3682210147380829}, {"X": 0.26341694593429565, "Y": 0.3682202994823456}, {"X": 0.2634168863296509, "Y": 0.3746068477630615}, {"X": 0.21331366896629333, "Y": 0.37460756301879883}]}, "Id": "e5c797fd-3ae8-47c6-a07c-bc58c2b808ca"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03317679092288017, "Height": 0.008311101235449314, "Left": 0.7232305407524109, "Top": 0.3676694929599762}, "Polygon": [{"X": 0.7232306599617004, "Y": 0.3676699697971344}, {"X": 0.7564073204994202, "Y": 0.3676694929599762}, {"X": 0.7564072012901306, "Y": 0.3759801387786865}, {"X": 0.7232305407524109, "Y": 0.37598058581352234}]}, "Id": "55d3a02c-be24-4086-b3ad-8553e294b004"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Certificate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04864736273884773, "Height": 0.006783640012145042, "Left": 0.7593495845794678, "Top": 0.3677155077457428}, "Polygon": [{"X": 0.7593496441841125, "Y": 0.3677161931991577}, {"X": 0.807996928691864, "Y": 0.3677155077457428}, {"X": 0.8079968094825745, "Y": 0.3744984567165375}, {"X": 0.7593495845794678, "Y": 0.3744991421699524}]}, "Id": "1ee8df4e-6c61-4a5f-b1af-1ef10f4c006c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2270", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02383694052696228, "Height": 0.006322705652564764, "Left": 0.16246169805526733, "Top": 0.37672942876815796}, "Polygon": [{"X": 0.16246172785758972, "Y": 0.3767297565937042}, {"X": 0.18629863858222961, "Y": 0.37672942876815796}, {"X": 0.18629859387874603, "Y": 0.3830517828464508}, {"X": 0.16246169805526733, "Y": 0.38305214047431946}]}, "Id": "6739d4c0-000e-41d4-b389-b9516e12ffcb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "WHITE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032461635768413544, "Height": 0.006380218081176281, "Left": 0.18896152079105377, "Top": 0.3766777217388153}, "Polygon": [{"X": 0.18896156549453735, "Y": 0.3766781687736511}, {"X": 0.22142314910888672, "Y": 0.3766777217388153}, {"X": 0.22142310440540314, "Y": 0.38305747509002686}, {"X": 0.18896152079105377, "Y": 0.38305795192718506}]}, "Id": "976cbb1e-72a3-4e89-97b9-3c0ab53d41e1"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "OAK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02111927978694439, "Height": 0.006493091583251953, "Left": 0.22455410659313202, "Top": 0.3767055571079254}, "Polygon": [{"X": 0.2245541661977768, "Y": 0.3767058551311493}, {"X": 0.24567338824272156, "Y": 0.3767055571079254}, {"X": 0.24567334353923798, "Y": 0.3831983506679535}, {"X": 0.22455410659313202, "Y": 0.38319864869117737}]}, "Id": "b0bf7df0-730d-4cdd-a22f-39818b6c4843"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "CIR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017902487888932228, "Height": 0.00622687628492713, "Left": 0.2486133575439453, "Top": 0.3768872916698456}, "Polygon": [{"X": 0.2486134171485901, "Y": 0.3768875300884247}, {"X": 0.266515851020813, "Y": 0.3768872916698456}, {"X": 0.2665157914161682, "Y": 0.38311392068862915}, {"X": 0.2486133575439453, "Y": 0.38311415910720825}]}, "Id": "4f431d48-874f-4740-93bb-51b83e6a1708"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "We", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014652658253908157, "Height": 0.006238427013158798, "Left": 0.7073789238929749, "Top": 0.37680166959762573}, "Polygon": [{"X": 0.7073789834976196, "Y": 0.37680187821388245}, {"X": 0.7220315337181091, "Y": 0.37680166959762573}, {"X": 0.7220314741134644, "Y": 0.3830398917198181}, {"X": 0.7073789238929749, "Y": 0.38304010033607483}]}, "Id": "71c06bfa-0181-44e4-a66d-a233a4e4b6fb"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Certify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03157906234264374, "Height": 0.008171954192221165, "Left": 0.7253687977790833, "Top": 0.3762212097644806}, "Polygon": [{"X": 0.7253689169883728, "Y": 0.3762216567993164}, {"X": 0.7569478750228882, "Y": 0.3762212097644806}, {"X": 0.7569477558135986, "Y": 0.38439270853996277}, {"X": 0.7253687977790833, "Y": 0.3843931555747986}]}, "Id": "5e7c928d-8d7d-48da-9545-353c6810306f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019612392410635948, "Height": 0.006630921270698309, "Left": 0.7600220441818237, "Top": 0.37648865580558777}, "Polygon": [{"X": 0.7600221633911133, "Y": 0.37648892402648926}, {"X": 0.7796344757080078, "Y": 0.37648865580558777}, {"X": 0.7796343564987183, "Y": 0.38311928510665894}, {"X": 0.7600220441818237, "Y": 0.3831195831298828}]}, "Id": "7ff2d6a8-e716-4cdf-9f86-9c2b837d7e8d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0170767679810524, "Height": 0.00658020656555891, "Left": 0.7818982601165771, "Top": 0.37639084458351135}, "Polygon": [{"X": 0.7818983793258667, "Y": 0.37639108300209045}, {"X": 0.7989750504493713, "Y": 0.37639084458351135}, {"X": 0.7989749312400818, "Y": 0.38297080993652344}, {"X": 0.7818982601165771, "Y": 0.38297104835510254}]}, "Id": "82c81974-7719-4576-ab51-ac406e622dd4"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044950954616069794, "Height": 0.008148849941790104, "Left": 0.8024011850357056, "Top": 0.37618619203567505}, "Polygon": [{"X": 0.8024013042449951, "Y": 0.3761868178844452}, {"X": 0.847352147102356, "Y": 0.37618619203567505}, {"X": 0.8473520278930664, "Y": 0.38433441519737244}, {"X": 0.8024011850357056, "Y": 0.3843350410461426}]}, "Id": "bcc2b6ab-84d7-4352-9827-4d74ceaa83c4"}, {"BlockType": "WORD", "Confidence": 99.2930908203125, "Text": "AURORA,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04494357109069824, "Height": 0.0072541264817118645, "Left": 0.16181287169456482, "Top": 0.385231077671051}, "Polygon": [{"X": 0.1618129163980484, "Y": 0.38523170351982117}, {"X": 0.20675644278526306, "Y": 0.385231077671051}, {"X": 0.2067563831806183, "Y": 0.39248454570770264}, {"X": 0.16181287169456482, "Y": 0.39248520135879517}]}, "Id": "a568a8b3-540b-444c-973d-93a9f8915dae"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009762809611856937, "Height": 0.006594124715775251, "Left": 0.21053430438041687, "Top": 0.3850327432155609}, "Polygon": [{"X": 0.21053434908390045, "Y": 0.38503286242485046}, {"X": 0.22029711306095123, "Y": 0.3850327432155609}, {"X": 0.22029706835746765, "Y": 0.3916267156600952}, {"X": 0.21053430438041687, "Y": 0.39162686467170715}]}, "Id": "801f46f0-826c-45dc-8f96-ed88f78c8cd0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "60502", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029458606615662575, "Height": 0.006240761373192072, "Left": 0.22320081293582916, "Top": 0.38528716564178467}, "Polygon": [{"X": 0.22320085763931274, "Y": 0.3852875828742981}, {"X": 0.252659410238266, "Y": 0.38528716564178467}, {"X": 0.2526593804359436, "Y": 0.3915275037288666}, {"X": 0.22320081293582916, "Y": 0.39152792096138}]}, "Id": "9b40e3ef-e706-46f9-9b9a-f455b4704003"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "has", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016709955409169197, "Height": 0.006623478606343269, "Left": 0.7077588438987732, "Top": 0.38500329852104187}, "Polygon": [{"X": 0.7077589631080627, "Y": 0.38500353693962097}, {"X": 0.7244688272476196, "Y": 0.38500329852104187}, {"X": 0.7244687080383301, "Y": 0.3916265368461609}, {"X": 0.7077588438987732, "Y": 0.39162677526474}]}, "Id": "67d3a913-0736-46e3-9b1a-8cee8a4db55f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "been", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023208731785416603, "Height": 0.006726184394210577, "Left": 0.7273632884025574, "Top": 0.3849447965621948}, "Polygon": [{"X": 0.7273633480072021, "Y": 0.3849451243877411}, {"X": 0.7505720257759094, "Y": 0.3849447965621948}, {"X": 0.7505719065666199, "Y": 0.3916706442832947}, {"X": 0.7273632884025574, "Y": 0.39167100191116333}]}, "Id": "7fa77e86-59b2-413b-b743-809bbacf8116"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "reweighed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05010595545172691, "Height": 0.00796002522110939, "Left": 0.75407475233078, "Top": 0.38496842980384827}, "Polygon": [{"X": 0.7540748715400696, "Y": 0.38496914505958557}, {"X": 0.8041807413101196, "Y": 0.38496842980384827}, {"X": 0.8041806221008301, "Y": 0.39292773604393005}, {"X": 0.75407475233078, "Y": 0.39292845129966736}]}, "Id": "ff079737-2ad9-47de-9118-911dd7b97283"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01778884045779705, "Height": 0.006604108493775129, "Left": 0.806389570236206, "Top": 0.3849635720252991}, "Polygon": [{"X": 0.8063896894454956, "Y": 0.38496384024620056}, {"X": 0.8241783976554871, "Y": 0.3849635720252991}, {"X": 0.8241783380508423, "Y": 0.3915674388408661}, {"X": 0.806389570236206, "Y": 0.3915676772594452}]}, "Id": "6a3055c0-d2c2-4437-8919-40802ca0173b"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018977243453264236, "Height": 0.006806911434978247, "Left": 0.8267665505409241, "Top": 0.3848668932914734}, "Polygon": [{"X": 0.8267666697502136, "Y": 0.3848671615123749}, {"X": 0.8457438349723816, "Y": 0.3848668932914734}, {"X": 0.845743715763092, "Y": 0.3916735351085663}, {"X": 0.8267665505409241, "Y": 0.3916738033294678}]}, "Id": "503a44bf-4441-4388-a253-b39d7fdc801f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015432396903634071, "Height": 0.006619512569159269, "Left": 0.707314670085907, "Top": 0.39335307478904724}, "Polygon": [{"X": 0.7073147296905518, "Y": 0.39335328340530396}, {"X": 0.7227470874786377, "Y": 0.39335307478904724}, {"X": 0.7227469682693481, "Y": 0.3999723792076111}, {"X": 0.707314670085907, "Y": 0.3999725878238678}]}, "Id": "f06626d7-623e-428b-8708-4ecf2a6dee07"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "corrected", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045688241720199585, "Height": 0.006469257641583681, "Left": 0.7260426878929138, "Top": 0.3935489356517792}, "Polygon": [{"X": 0.7260427474975586, "Y": 0.3935495913028717}, {"X": 0.771730899810791, "Y": 0.3935489356517792}, {"X": 0.7717308402061462, "Y": 0.40001752972602844}, {"X": 0.7260426878929138, "Y": 0.40001818537712097}]}, "Id": "a50b6dfe-8198-4d4b-afec-5184ce219291"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031995803117752075, "Height": 0.008181402459740639, "Left": 0.7740893959999084, "Top": 0.39328134059906006}, "Polygon": [{"X": 0.774089515209198, "Y": 0.3932817876338959}, {"X": 0.8060852289199829, "Y": 0.39328134059906006}, {"X": 0.8060850501060486, "Y": 0.40146228671073914}, {"X": 0.7740893959999084, "Y": 0.40146273374557495}]}, "Id": "4a7a5536-e900-4511-99fc-5e1c281c6147"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "as", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010669945739209652, "Height": 0.004868826363235712, "Left": 0.8092235326766968, "Top": 0.394987553358078}, "Polygon": [{"X": 0.8092235922813416, "Y": 0.39498770236968994}, {"X": 0.819893479347229, "Y": 0.394987553358078}, {"X": 0.8198934197425842, "Y": 0.39985620975494385}, {"X": 0.8092235326766968, "Y": 0.3998563587665558}]}, "Id": "d5e2341b-ac9d-4cf9-845a-1ce0cb6d2b91"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "stated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029793880879878998, "Height": 0.006427379325032234, "Left": 0.8228550553321838, "Top": 0.39359018206596375}, "Polygon": [{"X": 0.8228551745414734, "Y": 0.39359062910079956}, {"X": 0.8526489734649658, "Y": 0.39359018206596375}, {"X": 0.8526488542556763, "Y": 0.4000171422958374}, {"X": 0.8228550553321838, "Y": 0.40001755952835083}]}, "Id": "6f9d83b5-b262-425d-b0fa-977206015d21"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007551476359367371, "Height": 0.0070420317351818085, "Left": 0.7076213955879211, "Top": 0.4017404317855835}, "Polygon": [{"X": 0.7076214551925659, "Y": 0.40174052119255066}, {"X": 0.7151728868484497, "Y": 0.4017404317855835}, {"X": 0.7151727676391602, "Y": 0.40878233313560486}, {"X": 0.7076213955879211, "Y": 0.4087824523448944}]}, "Id": "f67a35cd-5ffa-477f-9b3c-244539681a8b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015589390881359577, "Height": 0.006525528617203236, "Left": 0.717993974685669, "Top": 0.40196681022644043}, "Polygon": [{"X": 0.7179940938949585, "Y": 0.40196701884269714}, {"X": 0.733583390712738, "Y": 0.40196681022644043}, {"X": 0.7335832715034485, "Y": 0.4084920883178711}, {"X": 0.717993974685669, "Y": 0.4084923267364502}]}, "Id": "fc8c1713-5d0a-4d6e-ab41-a12f32d1d01a"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "true", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019698061048984528, "Height": 0.00635173823684454, "Left": 0.7363592386245728, "Top": 0.40228596329689026}, "Polygon": [{"X": 0.7363593578338623, "Y": 0.40228626132011414}, {"X": 0.7560573220252991, "Y": 0.40228596329689026}, {"X": 0.7560572028160095, "Y": 0.4086374044418335}, {"X": 0.7363592386245728, "Y": 0.4086377024650574}]}, "Id": "89dec979-92d8-44b5-b2bd-4bbffe25bbac"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01766594685614109, "Height": 0.006636554840952158, "Left": 0.7591797113418579, "Top": 0.4019549787044525}, "Polygon": [{"X": 0.7591797709465027, "Y": 0.401955246925354}, {"X": 0.7768456339836121, "Y": 0.4019549787044525}, {"X": 0.7768455743789673, "Y": 0.40859127044677734}, {"X": 0.7591797113418579, "Y": 0.40859153866767883}]}, "Id": "544a0692-3595-46e3-bd16-b4a1927867b2"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "accurate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0413362979888916, "Height": 0.0063436212949454784, "Left": 0.7800097465515137, "Top": 0.40249454975128174}, "Polygon": [{"X": 0.7800098657608032, "Y": 0.4024951756000519}, {"X": 0.8213460445404053, "Y": 0.40249454975128174}, {"X": 0.8213459253311157, "Y": 0.40883758664131165}, {"X": 0.7800097465515137, "Y": 0.4088381826877594}]}, "Id": "bca57e99-5a4d-41de-8d60-75556466424a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Declared", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04313170164823532, "Height": 0.0064966813661158085, "Left": 0.16241343319416046, "Top": 0.4106127619743347}, "Polygon": [{"X": 0.16241347789764404, "Y": 0.41061341762542725}, {"X": 0.20554512739181519, "Y": 0.4106127619743347}, {"X": 0.2055450826883316, "Y": 0.41710883378982544}, {"X": 0.16241343319416046, "Y": 0.4171094596385956}]}, "Id": "055d9ff7-bcd1-4fd5-b2c2-f93b605508aa"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03596487268805504, "Height": 0.008118771947920322, "Left": 0.20843079686164856, "Top": 0.4103333055973053}, "Polygon": [{"X": 0.20843085646629333, "Y": 0.4103338122367859}, {"X": 0.2443956732749939, "Y": 0.4103333055973053}, {"X": 0.24439561367034912, "Y": 0.41845154762268066}, {"X": 0.20843079686164856, "Y": 0.41845208406448364}]}, "Id": "d760f0ba-354c-422d-a989-b8bfafd9b0e3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1148", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023435501381754875, "Height": 0.006363362539559603, "Left": 0.2486116588115692, "Top": 0.4108577370643616}, "Polygon": [{"X": 0.2486117035150528, "Y": 0.4108580946922302}, {"X": 0.27204716205596924, "Y": 0.4108577370643616}, {"X": 0.27204710245132446, "Y": 0.4172207713127136}, {"X": 0.2486116588115692, "Y": 0.4172210991382599}]}, "Id": "d5584019-5f32-4066-84f4-1402dcfd36ed"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03254435956478119, "Height": 0.008135346695780754, "Left": 0.7069318294525146, "Top": 0.4102950394153595}, "Polygon": [{"X": 0.7069319486618042, "Y": 0.4102955162525177}, {"X": 0.739476203918457, "Y": 0.4102950394153595}, {"X": 0.7394760847091675, "Y": 0.4184299111366272}, {"X": 0.7069318294525146, "Y": 0.4184303879737854}]}, "Id": "b1630103-94a0-4468-ae32-0befd7f98a17"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009893991984426975, "Height": 0.006705030333250761, "Left": 0.7423079609870911, "Top": 0.410263329744339}, "Polygon": [{"X": 0.7423080205917358, "Y": 0.4102634787559509}, {"X": 0.7522019147872925, "Y": 0.410263329744339}, {"X": 0.7522018551826477, "Y": 0.4169681966304779}, {"X": 0.7423079609870911, "Y": 0.41696834564208984}]}, "Id": "211f2e60-ed73-46a2-bd53-53356417abab"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015859805047512054, "Height": 0.0065754735842347145, "Left": 0.7543373107910156, "Top": 0.4106237292289734}, "Polygon": [{"X": 0.7543374300003052, "Y": 0.4106239676475525}, {"X": 0.7701971530914307, "Y": 0.4106237292289734}, {"X": 0.7701970338821411, "Y": 0.4171989858150482}, {"X": 0.7543373107910156, "Y": 0.4171992242336273}]}, "Id": "ed5d29c0-6e93-4ae0-9acc-0d56ae6b9bdb"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04508283734321594, "Height": 0.007687367964535952, "Left": 0.7730361223220825, "Top": 0.41048917174339294}, "Polygon": [{"X": 0.7730362415313721, "Y": 0.4104898273944855}, {"X": 0.8181189894676208, "Y": 0.41048917174339294}, {"X": 0.8181188702583313, "Y": 0.4181758761405945}, {"X": 0.7730361223220825, "Y": 0.418176531791687}]}, "Id": "d75dcaa8-1ea6-4b2b-96f6-637e9cd39729"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Corrected", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04748039320111275, "Height": 0.006810094229876995, "Left": 0.16247409582138062, "Top": 0.4188801050186157}, "Polygon": [{"X": 0.1624741405248642, "Y": 0.41888079047203064}, {"X": 0.20995448529720306, "Y": 0.4188801050186157}, {"X": 0.20995444059371948, "Y": 0.4256894886493683}, {"X": 0.16247409582138062, "Y": 0.4256902039051056}]}, "Id": "3402fce0-da81-4c00-bf25-fb76344e0c71"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036091309040784836, "Height": 0.008014409802854061, "Left": 0.21280618011951447, "Top": 0.41898638010025024}, "Polygon": [{"X": 0.21280623972415924, "Y": 0.4189869165420532}, {"X": 0.2488974928855896, "Y": 0.41898638010025024}, {"X": 0.24889743328094482, "Y": 0.4270002543926239}, {"X": 0.21280618011951447, "Y": 0.4270007908344269}]}, "Id": "8af18e9c-1ec1-4bb4-9f26-692535784437"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "1425", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023578299209475517, "Height": 0.006388806272298098, "Left": 0.253194659948349, "Top": 0.419284462928772}, "Polygon": [{"X": 0.2531947195529938, "Y": 0.41928479075431824}, {"X": 0.27677297592163086, "Y": 0.419284462928772}, {"X": 0.2767729163169861, "Y": 0.4256729185581207}, {"X": 0.253194659948349, "Y": 0.425673246383667}]}, "Id": "62feb1e3-3c76-4873-b3de-608586f9a94f"}, {"BlockType": "WORD", "Confidence": 99.7120132446289, "Text": "HU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017055867239832878, "Height": 0.006777491886168718, "Left": 0.16441786289215088, "Top": 0.4541013836860657}, "Polygon": [{"X": 0.16441790759563446, "Y": 0.45410165190696716}, {"X": 0.1814737319946289, "Y": 0.4541013836860657}, {"X": 0.18147367238998413, "Y": 0.4608786106109619}, {"X": 0.16441786289215088, "Y": 0.4608788788318634}]}, "Id": "c0e0b732-3cb4-4875-9002-fb37a5b6918b"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027246743440628052, "Height": 0.007028465624898672, "Left": 0.19829589128494263, "Top": 0.4539552330970764}, "Polygon": [{"X": 0.1982959508895874, "Y": 0.45395565032958984}, {"X": 0.22554263472557068, "Y": 0.4539552330970764}, {"X": 0.2255425900220871, "Y": 0.4609832763671875}, {"X": 0.19829589128494263, "Y": 0.4609836935997009}]}, "Id": "fe2a6864-1f4c-4561-acc8-8aebf3333573"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02633608691394329, "Height": 0.007158621214330196, "Left": 0.2319745570421219, "Top": 0.45388081669807434}, "Polygon": [{"X": 0.23197461664676666, "Y": 0.4538812041282654}, {"X": 0.2583106458187103, "Y": 0.45388081669807434}, {"X": 0.25831058621406555, "Y": 0.4610390365123749}, {"X": 0.2319745570421219, "Y": 0.4610394239425659}]}, "Id": "90255238-c825-4b1d-a1ed-5e7aa7c14b94"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02890188619494438, "Height": 0.007359205279499292, "Left": 0.2874515652656555, "Top": 0.4534839987754822}, "Polygon": [{"X": 0.2874516248703003, "Y": 0.453484445810318}, {"X": 0.316353440284729, "Y": 0.4534839987754822}, {"X": 0.31635338068008423, "Y": 0.4608427584171295}, {"X": 0.2874515652656555, "Y": 0.46084320545196533}]}, "Id": "6d42f625-e749-4efc-8c44-39486cbe611e"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040659863501787186, "Height": 0.009368103928864002, "Left": 0.3337138891220093, "Top": 0.4535251557826996}, "Polygon": [{"X": 0.33371397852897644, "Y": 0.4535257816314697}, {"X": 0.37437376379966736, "Y": 0.4535251557826996}, {"X": 0.3743736743927002, "Y": 0.4628926217556}, {"X": 0.3337138891220093, "Y": 0.4628932476043701}]}, "Id": "4c3a5826-fa70-4191-b9a3-b29a25743090"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Terminal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0507369227707386, "Height": 0.007401087787002325, "Left": 0.3780301809310913, "Top": 0.45351311564445496}, "Polygon": [{"X": 0.3780302405357361, "Y": 0.4535139203071594}, {"X": 0.4287670850753784, "Y": 0.45351311564445496}, {"X": 0.42876702547073364, "Y": 0.46091344952583313}, {"X": 0.3780301809310913, "Y": 0.4609142243862152}]}, "Id": "a216b4b0-4019-4f82-b4ec-d9f9c0a207de"}, {"BlockType": "WORD", "Confidence": 99.7217788696289, "Text": "Pro", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01893853209912777, "Height": 0.007110124453902245, "Left": 0.46933484077453613, "Top": 0.45394328236579895}, "Polygon": [{"X": 0.4693349301815033, "Y": 0.4539435803890228}, {"X": 0.48827338218688965, "Y": 0.45394328236579895}, {"X": 0.4882732927799225, "Y": 0.46105310320854187}, {"X": 0.46933484077453613, "Y": 0.46105340123176575}]}, "Id": "0e5db8ff-d722-42eb-87b2-399868c63a06"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044754479080438614, "Height": 0.00717869633808732, "Left": 0.4923553168773651, "Top": 0.4536535441875458}, "Polygon": [{"X": 0.4923553764820099, "Y": 0.4536542296409607}, {"X": 0.5371097922325134, "Y": 0.4536535441875458}, {"X": 0.5371096730232239, "Y": 0.46083155274391174}, {"X": 0.4923553168773651, "Y": 0.46083223819732666}]}, "Id": "b3320a51-121e-437c-b1db-b25fcce043f9"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "33136199", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058709219098091125, "Height": 0.007329114247113466, "Left": 0.1687587946653366, "Top": 0.4650166928768158}, "Polygon": [{"X": 0.1687588393688202, "Y": 0.4650176167488098}, {"X": 0.22746801376342773, "Y": 0.4650166928768158}, {"X": 0.22746795415878296, "Y": 0.47234490513801575}, {"X": 0.1687587946653366, "Y": 0.47234582901000977}]}, "Id": "53638deb-dd94-4c13-8c9f-6bd580b0118d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0687561109662056, "Height": 0.008462921716272831, "Left": 0.24354542791843414, "Top": 0.46471866965293884}, "Polygon": [{"X": 0.24354548752307892, "Y": 0.4647197127342224}, {"X": 0.31230154633522034, "Y": 0.46471866965293884}, {"X": 0.3123014569282532, "Y": 0.47318050265312195}, {"X": 0.24354542791843414, "Y": 0.4731815755367279}]}, "Id": "cc0455f0-796e-4b02-a4ab-8a6eed76760f"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "00:02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03399926424026489, "Height": 0.007215920835733414, "Left": 0.3340480327606201, "Top": 0.4650706946849823}, "Polygon": [{"X": 0.3340480923652649, "Y": 0.4650712311267853}, {"X": 0.368047297000885, "Y": 0.4650706946849823}, {"X": 0.36804723739624023, "Y": 0.4722861051559448}, {"X": 0.3340480327606201, "Y": 0.4722866117954254}]}, "Id": "35ce494a-f8b2-4050-8882-5cfd16060619"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "880", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02212643437087536, "Height": 0.006782433483749628, "Left": 0.3790387511253357, "Top": 0.46533575654029846}, "Polygon": [{"X": 0.37903881072998047, "Y": 0.4653360843658447}, {"X": 0.4011651873588562, "Y": 0.46533575654029846}, {"X": 0.4011651277542114, "Y": 0.4721178412437439}, {"X": 0.3790387511253357, "Y": 0.47211816906929016}]}, "Id": "fc11632e-c323-4aba-bb57-67f0553a72c4"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "463", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021867085248231888, "Height": 0.00682410504668951, "Left": 0.43186819553375244, "Top": 0.46527284383773804}, "Polygon": [{"X": 0.4318682551383972, "Y": 0.4652732014656067}, {"X": 0.45373526215553284, "Y": 0.46527284383773804}, {"X": 0.45373520255088806, "Y": 0.47209662199020386}, {"X": 0.43186819553375244, "Y": 0.4720969498157501}]}, "Id": "83076bab-274b-4fc1-8450-815ee36280bd"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060560133308172226, "Height": 0.007165390066802502, "Left": 0.5148974657058716, "Top": 0.4650382697582245}, "Polygon": [{"X": 0.5148975253105164, "Y": 0.4650391936302185}, {"X": 0.5754575729370117, "Y": 0.4650382697582245}, {"X": 0.5754575133323669, "Y": 0.47220271825790405}, {"X": 0.5148974657058716, "Y": 0.47220367193222046}]}, "Id": "8fe26dd1-6909-409a-b566-e69e92f6b62a"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "33136200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058386050164699554, "Height": 0.006965055596083403, "Left": 0.16896452009677887, "Top": 0.47567445039749146}, "Polygon": [{"X": 0.16896456480026245, "Y": 0.4756753742694855}, {"X": 0.22735057771205902, "Y": 0.47567445039749146}, {"X": 0.22735051810741425, "Y": 0.4826385974884033}, {"X": 0.16896452009677887, "Y": 0.48263952136039734}]}, "Id": "777e8d36-a227-4588-802d-219b3471010f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06862763315439224, "Height": 0.008570958860218525, "Left": 0.24351204931735992, "Top": 0.47528213262557983}, "Polygon": [{"X": 0.2435121089220047, "Y": 0.4752832055091858}, {"X": 0.31213968992233276, "Y": 0.47528213262557983}, {"X": 0.3121396005153656, "Y": 0.48385199904441833}, {"X": 0.24351204931735992, "Y": 0.4838530719280243}]}, "Id": "49ec7cb3-00f8-492d-bb75-a2c07c2ee36c"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "00:03", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033893387764692307, "Height": 0.0071488418616354465, "Left": 0.3341608941555023, "Top": 0.4757031202316284}, "Polygon": [{"X": 0.3341609537601471, "Y": 0.475703626871109}, {"X": 0.3680543005466461, "Y": 0.4757031202316284}, {"X": 0.36805421113967896, "Y": 0.48285141587257385}, {"X": 0.3341608941555023, "Y": 0.48285195231437683}]}, "Id": "a085ce49-6723-4075-a76b-70c5019e619f"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "545", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02147347666323185, "Height": 0.006957860663533211, "Left": 0.3794381320476532, "Top": 0.4757300317287445}, "Polygon": [{"X": 0.379438191652298, "Y": 0.47573035955429077}, {"X": 0.4009115993976593, "Y": 0.4757300317287445}, {"X": 0.4009115397930145, "Y": 0.4826875627040863}, {"X": 0.3794381320476532, "Y": 0.48268789052963257}]}, "Id": "9630ad70-11be-4b68-a0ff-8bb3859cb24a"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "463", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021909311413764954, "Height": 0.006979272235184908, "Left": 0.43184787034988403, "Top": 0.4756060838699341}, "Polygon": [{"X": 0.4318479299545288, "Y": 0.47560641169548035}, {"X": 0.4537571966648102, "Y": 0.4756060838699341}, {"X": 0.453757107257843, "Y": 0.48258501291275024}, {"X": 0.43184787034988403, "Y": 0.4825853407382965}]}, "Id": "f0317bcf-def8-43ee-ae8b-56e860d6c18b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06058836355805397, "Height": 0.006870943587273359, "Left": 0.514869749546051, "Top": 0.4756676256656647}, "Polygon": [{"X": 0.5148698091506958, "Y": 0.4756685793399811}, {"X": 0.5754581093788147, "Y": 0.4756676256656647}, {"X": 0.5754579901695251, "Y": 0.4825376272201538}, {"X": 0.514869749546051, "Y": 0.4825385808944702}]}, "Id": "259175cc-94e6-4bdb-9a5b-9bf04935427e"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0063613103702664375, "Height": 0.006829916033893824, "Left": 0.16887255012989044, "Top": 0.48619070649147034}, "Polygon": [{"X": 0.16887259483337402, "Y": 0.4861908257007599}, {"X": 0.175233855843544, "Y": 0.48619070649147034}, {"X": 0.17523381114006042, "Y": 0.49302053451538086}, {"X": 0.16887255012989044, "Y": 0.493020623922348}]}, "Id": "f7d7d58a-a7a1-4847-8fdd-36ab3d819ae9"}, {"BlockType": "WORD", "Confidence": 99.68511199951172, "Text": "4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0070258076302707195, "Height": 0.006691001355648041, "Left": 0.16855113208293915, "Top": 0.49654772877693176}, "Polygon": [{"X": 0.16855117678642273, "Y": 0.4965478479862213}, {"X": 0.17557694017887115, "Y": 0.49654772877693176}, {"X": 0.17557689547538757, "Y": 0.5032386183738708}, {"X": 0.16855113208293915, "Y": 0.5032387375831604}]}, "Id": "876eb657-e5fd-42f7-9ee9-ca643aaa2e7e"}, {"BlockType": "WORD", "Confidence": 99.79252624511719, "Text": "5", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006197942886501551, "Height": 0.006747648119926453, "Left": 0.1691218912601471, "Top": 0.5072013139724731}, "Polygon": [{"X": 0.16912193596363068, "Y": 0.5072013735771179}, {"X": 0.1753198355436325, "Y": 0.5072013139724731}, {"X": 0.17531979084014893, "Y": 0.5139488577842712}, {"X": 0.1691218912601471, "Y": 0.5139489769935608}]}, "Id": "87035eee-3fe9-4dd9-aaf4-8c70b00ce517"}, {"BlockType": "WORD", "Confidence": 99.71281433105469, "Text": "6", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00636710412800312, "Height": 0.0069289072416722775, "Left": 0.16888143122196198, "Top": 0.5180972814559937}, "Polygon": [{"X": 0.16888147592544556, "Y": 0.5180974006652832}, {"X": 0.17524853348731995, "Y": 0.5180972814559937}, {"X": 0.17524848878383636, "Y": 0.5250260829925537}, {"X": 0.16888143122196198, "Y": 0.5250262022018433}]}, "Id": "ed60da17-2326-4c17-be69-53721c019af7"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "7", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006649814546108246, "Height": 0.006885642185807228, "Left": 0.16893725097179413, "Top": 0.5284852385520935}, "Polygon": [{"X": 0.1689372956752777, "Y": 0.5284853577613831}, {"X": 0.17558707296848297, "Y": 0.5284852385520935}, {"X": 0.1755870282649994, "Y": 0.5353707671165466}, {"X": 0.16893725097179413, "Y": 0.5353708863258362}]}, "Id": "5eafdb51-889e-488f-a85a-c185b211e304"}, {"BlockType": "WORD", "Confidence": 99.67375183105469, "Text": "8", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006595203652977943, "Height": 0.006734260357916355, "Left": 0.1688777208328247, "Top": 0.5392356514930725}, "Polygon": [{"X": 0.1688777655363083, "Y": 0.5392357707023621}, {"X": 0.1754729151725769, "Y": 0.5392356514930725}, {"X": 0.17547287046909332, "Y": 0.5459697842597961}, {"X": 0.1688777208328247, "Y": 0.5459699034690857}]}, "Id": "a57b7820-eb7c-4286-8100-06bceb49b898"}, {"BlockType": "WORD", "Confidence": 99.60299682617188, "Text": "9", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006715078372508287, "Height": 0.007085907738655806, "Left": 0.16874417662620544, "Top": 0.5495967268943787}, "Polygon": [{"X": 0.16874422132968903, "Y": 0.5495968461036682}, {"X": 0.17545926570892334, "Y": 0.5495967268943787}, {"X": 0.17545920610427856, "Y": 0.5566825270652771}, {"X": 0.16874417662620544, "Y": 0.5566826462745667}]}, "Id": "36598866-e608-435b-aae9-d7fa87613397"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "10", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012692058458924294, "Height": 0.006892743520438671, "Left": 0.1696051061153412, "Top": 0.5602093935012817}, "Polygon": [{"X": 0.16960515081882477, "Y": 0.5602095723152161}, {"X": 0.18229715526103973, "Y": 0.5602093935012817}, {"X": 0.18229711055755615, "Y": 0.5671018958091736}, {"X": 0.1696051061153412, "Y": 0.5671021342277527}]}, "Id": "612a3c93-6aaa-497a-b2bb-350e08f97aca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027860941365361214, "Height": 0.007223647087812424, "Left": 0.16397152841091156, "Top": 0.5709370970726013}, "Polygon": [{"X": 0.16397157311439514, "Y": 0.5709375739097595}, {"X": 0.19183246791362762, "Y": 0.5709370970726013}, {"X": 0.19183242321014404, "Y": 0.5781602263450623}, {"X": 0.16397152841091156, "Y": 0.5781607031822205}]}, "Id": "615338f4-c173-4f98-87e7-f8dd7c800562"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "1425", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02627403661608696, "Height": 0.006929267197847366, "Left": 0.33493518829345703, "Top": 0.5713433623313904}, "Polygon": [{"X": 0.3349352478981018, "Y": 0.5713438391685486}, {"X": 0.3612092137336731, "Y": 0.5713433623313904}, {"X": 0.3612091541290283, "Y": 0.5782721638679504}, {"X": 0.33493518829345703, "Y": 0.5782726407051086}]}, "Id": "c8763038-dc17-452b-897b-24c903ca42c3"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Page", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030416401103138924, "Height": 0.010070300661027431, "Left": 0.8169759511947632, "Top": 0.9619089365005493}, "Polygon": [{"X": 0.8169761300086975, "Y": 0.9619096517562866}, {"X": 0.8473923802375793, "Y": 0.9619089365005493}, {"X": 0.847392201423645, "Y": 0.9719785451889038}, {"X": 0.8169759511947632, "Y": 0.9719792604446411}]}, "Id": "5d5a5624-06ce-4d18-99a5-7ca823f1b2e8"}, {"BlockType": "WORD", "Confidence": 99.63548278808594, "Text": "3/3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01798633299767971, "Height": 0.008184125646948814, "Left": 0.8512584567070007, "Top": 0.9621924757957458}, "Polygon": [{"X": 0.8512585759162903, "Y": 0.9621928930282593}, {"X": 0.8692447543144226, "Y": 0.9621924757957458}, {"X": 0.8692446351051331, "Y": 0.970376193523407}, {"X": 0.8512584567070007, "Y": 0.9703766107559204}]}, "Id": "e219191e-be1d-4440-901a-e2127fe51f88"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "e30481eb-2b12-42e3-aab3-2fcfa4ca369d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "e30481eb-2b12-42e3-aab3-2fcfa4ca369d", "content-type": "application/x-amz-json-1.1", "content-length": "78383", "date": "Wed, 24 Sep 2025 07:19:38 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "74cc309e-dbcb-4c30-87d2-0fa506601157", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 07:19:40 GMT", "content-type": "application/json", "content-length": "695", "connection": "keep-alive", "x-amzn-requestid": "74cc309e-dbcb-4c30-87d2-0fa506601157"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need classify each page. Only one page here. It's a weight and inspection certificate. Keywords: \"Weight & Inspection\", \"CERTIFIED WEIGHT CERTIFICATE\", \"Corrected Weight\", \"Declared Weight\", etc. Also has \"Bill of Lading\" but main document is weight and inspection. So doc_type: weight_and_inspection_cert. page_no 1. Output array with one object."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"weight_and_inspection_cert\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2649, "outputTokens": 102, "totalTokens": 2751}, "metrics": {"latencyMs": 643}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/55d50227_W_DFY1VDZWR7NBDLJV02G2.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination, keywords indication and key fields/structure are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight with length, width and height.\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. If weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: This will include keywords like fright (As described vs As found) or (Original vs inspection) or Corrected class or Correction information\n        Other keywords indication:  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nCentral Transport\n08/31/2025\nPRO#: 227407699\nWeight & Inspection\nCENTRAL TRANSPORT\n12225 Stephens Road\nWarren, MI 48089\nCERTIFIED WEIGHT CERTIFICATE\nPro Number: 227407699\nBill of Lading\nDate: 08/26/2025\nDIAMOND ENVELOPE\nWeight Certificate\n2270 WHITE OAK CIR\nWe Certify that this shipment\nAURORA, IL 60502\nhas been reweighed and that\nthe corrected weight as stated\nis the true and accurate\nDeclared Weight: 1148\nweight of the shipment\nCorrected Weight: 1425\nHU\nScan Date\nTime\nWeight Terminal\nPro Number\n33136199\n08/26/2025\n00:02 880\n463\n227407699\n33136200\n08/26/2025\n00:03\n545\n463\n227407699\n3\n4\n5\n6\n7\n8\n9\n10\nTotal\n1425\nPage 3/3\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}