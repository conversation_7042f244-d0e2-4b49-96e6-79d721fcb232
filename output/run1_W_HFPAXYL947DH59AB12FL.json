{"classification_result": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 7.544635138856393e-08}, {"X": 1.0, "Y": 1.0}, {"X": 2.557371914235773e-08, "Y": 1.0}]}, "Id": "ffd0c4b9-a24c-49e6-b948-aa07b8967dab", "Relationships": [{"Type": "CHILD", "Ids": ["40a15346-3c98-4ada-bacc-868d26bb571e", "0a1159a8-6ba5-4de9-9b1c-9e66c3ca6566", "3c87c4c7-bb53-4eda-8f91-1bd1cfdc2f7f", "b46a7037-2510-4264-a2e8-dc8d3d74ba49", "9f1a54bd-513a-41c5-b390-db2ca20cdd78", "c695b39c-961c-4de1-8af1-10f28a9d8986", "905caa1f-1d27-4d59-bca1-3087746675fe", "65f884d5-ae0d-4912-be77-e0d3c4e98cf2", "c650c7af-71ce-49fe-a778-2e66df9414cd", "1ad5ea58-a474-479d-ad52-c59f4696734e", "c3f5eaa6-287b-4021-b23a-dae5362dab21", "1b986406-c7cd-44af-9e9e-b0f768667ab3", "72041cb5-f031-4b3b-b097-8de3fdbe845d", "3f27fac1-2f93-4784-8c05-121352190d86", "1ea41e23-1933-4a88-ad15-5e8703fac307", "a82fdb2e-55b8-48e1-bbef-a743a0685381", "edd8e93a-89b4-4c4b-b80a-f891b8385b13", "1e1f2d74-33eb-4e1e-86d9-8976be1dae3a", "3021c7e9-6c2f-407f-a425-c9da44707984", "8198084d-9029-40d7-a09e-0578335091ef", "82dc3770-cd26-4c13-9892-205c541295f5", "4e550386-facd-4198-b0c2-f426d4074e8a", "f8ae79c0-1210-4ae1-a06c-e4e597ab3a25", "5124d947-af62-470d-9ed4-306637e3b86e", "b55c1a57-c28e-4f86-b384-b39324552263", "1cab0b6d-ab9a-4b2f-a595-614638fdfbff", "4c520646-6c25-4f38-8724-149d472f05a7", "5913cf0b-9cc9-46c3-be1e-dec435ef0ca1", "15465403-a25d-40a7-83b3-c3bee813e0b5", "7ba24b88-fbaf-42d7-bab5-aabf155bdba9", "158a7243-0957-407d-bf94-64f3777cff0d"]}]}, {"BlockType": "LINE", "Confidence": 74.48072814941406, "Text": "®", "Geometry": {"BoundingBox": {"Width": 0.006339469458907843, "Height": 0.005026033613830805, "Left": 0.34887465834617615, "Top": 0.08214132487773895}, "Polygon": [{"X": 0.34887465834617615, "Y": 0.08214132487773895}, {"X": 0.35521411895751953, "Y": 0.08214139193296432}, {"X": 0.35521411895751953, "Y": 0.08716735988855362}, {"X": 0.34887465834617615, "Y": 0.08716728538274765}]}, "Id": "40a15346-3c98-4ada-bacc-868d26bb571e", "Relationships": [{"Type": "CHILD", "Ids": ["565c994b-db8d-42bc-a1d3-85d098e64648"]}]}, {"BlockType": "LINE", "Confidence": 93.14971923828125, "Text": "ABF", "Geometry": {"BoundingBox": {"Width": 0.16267691552639008, "Height": 0.0427052304148674, "Left": 0.1905127316713333, "Top": 0.08202743530273438}, "Polygon": [{"X": 0.1905127316713333, "Y": 0.08202743530273438}, {"X": 0.3531896471977234, "Y": 0.08202920854091644}, {"X": 0.3531896471977234, "Y": 0.12473266571760178}, {"X": 0.1905127912759781, "Y": 0.12473086267709732}]}, "Id": "0a1159a8-6ba5-4de9-9b1c-9e66c3ca6566", "Relationships": [{"Type": "CHILD", "Ids": ["b0b2d25e-3dfb-4d62-952f-c4d9d1d2e83f"]}]}, {"BlockType": "LINE", "Confidence": 99.28890228271484, "Text": "DIMENSION/CUBE", "Geometry": {"BoundingBox": {"Width": 0.33178985118865967, "Height": 0.022381968796253204, "Left": 0.4926004111766815, "Top": 0.09914660453796387}, "Polygon": [{"X": 0.4926004409790039, "Y": 0.09914660453796387}, {"X": 0.8243902325630188, "Y": 0.09915024042129517}, {"X": 0.824390172958374, "Y": 0.12152857333421707}, {"X": 0.4926004111766815, "Y": 0.12152490764856339}]}, "Id": "3c87c4c7-bb53-4eda-8f91-1bd1cfdc2f7f", "Relationships": [{"Type": "CHILD", "Ids": ["f116586d-eb17-4075-87eb-6d58f8e33fbd"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Freight", "Geometry": {"BoundingBox": {"Width": 0.06476163864135742, "Height": 0.013669872656464577, "Left": 0.26666852831840515, "Top": 0.12770748138427734}, "Polygon": [{"X": 0.26666852831840515, "Y": 0.12770748138427734}, {"X": 0.3314301669597626, "Y": 0.12770819664001465}, {"X": 0.3314301669597626, "Y": 0.14137735962867737}, {"X": 0.26666852831840515, "Y": 0.14137662947177887}]}, "Id": "b46a7037-2510-4264-a2e8-dc8d3d74ba49", "Relationships": [{"Type": "CHILD", "Ids": ["2c689231-de23-4d1d-9965-8c1f31958387"]}]}, {"BlockType": "LINE", "Confidence": 98.82222747802734, "Text": "CERTIFICATE", "Geometry": {"BoundingBox": {"Width": 0.2919008433818817, "Height": 0.018494024872779846, "Left": 0.5108564496040344, "Top": 0.12465068697929382}, "Polygon": [{"X": 0.5108565092086792, "Y": 0.12465068697929382}, {"X": 0.8027573227882385, "Y": 0.12465392053127289}, {"X": 0.8027572631835938, "Y": 0.14314471185207367}, {"X": 0.5108564496040344, "Y": 0.1431414633989334}]}, "Id": "9f1a54bd-513a-41c5-b390-db2ca20cdd78", "Relationships": [{"Type": "CHILD", "Ids": ["56c8a4a7-a659-4395-a2fc-ec2e3e461afc"]}]}, {"BlockType": "LINE", "Confidence": 97.34647369384766, "Text": "An ArcBest Company", "Geometry": {"BoundingBox": {"Width": 0.14520306885242462, "Height": 0.01164002250880003, "Left": 0.1923685520887375, "Top": 0.1498125046491623}, "Polygon": [{"X": 0.1923685520887375, "Y": 0.1498125046491623}, {"X": 0.3375716209411621, "Y": 0.14981412887573242}, {"X": 0.3375716209411621, "Y": 0.1614525318145752}, {"X": 0.19236856698989868, "Y": 0.16145090758800507}]}, "Id": "c695b39c-961c-4de1-8af1-10f28a9d8986", "Relationships": [{"Type": "CHILD", "Ids": ["bba23112-7f87-4e99-8f60-21e9aa94bfb4", "ec608b0a-04a2-4f87-977a-e8e0565d8224", "cd3f26f7-8570-49b3-bcca-3bf98b8d4969"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Pronumber:", "Geometry": {"BoundingBox": {"Width": 0.09193293750286102, "Height": 0.010984220542013645, "Left": 0.11849311739206314, "Top": 0.1780705749988556}, "Polygon": [{"X": 0.11849311739206314, "Y": 0.1780705749988556}, {"X": 0.21042604744434357, "Y": 0.17807160317897797}, {"X": 0.21042606234550476, "Y": 0.18905478715896606}, {"X": 0.11849313229322433, "Y": 0.1890537440776825}]}, "Id": "905caa1f-1d27-4d59-bca1-3087746675fe", "Relationships": [{"Type": "CHILD", "Ids": ["56a76e2f-2378-4a58-a34e-d1217e7f96fc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "039721315", "Geometry": {"BoundingBox": {"Width": 0.08949947357177734, "Height": 0.010717591270804405, "Left": 0.3751376271247864, "Top": 0.17839008569717407}, "Polygon": [{"X": 0.3751376271247864, "Y": 0.17839008569717407}, {"X": 0.4646371006965637, "Y": 0.17839109897613525}, {"X": 0.4646371006965637, "Y": 0.18910767138004303}, {"X": 0.3751376271247864, "Y": 0.18910665810108185}]}, "Id": "65f884d5-ae0d-4912-be77-e0d3c4e98cf2", "Relationships": [{"Type": "CHILD", "Ids": ["824711bf-6c3d-4163-9b84-13d51fed190c"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "Trailer No: CITY 400177", "Geometry": {"BoundingBox": {"Width": 0.19406260550022125, "Height": 0.011353877373039722, "Left": 0.6320160031318665, "Top": 0.17781290411949158}, "Polygon": [{"X": 0.6320160627365112, "Y": 0.17781290411949158}, {"X": 0.8260786533355713, "Y": 0.17781509459018707}, {"X": 0.8260785937309265, "Y": 0.18916678428649902}, {"X": 0.6320160031318665, "Y": 0.18916457891464233}]}, "Id": "c650c7af-71ce-49fe-a778-2e66df9414cd", "Relationships": [{"Type": "CHILD", "Ids": ["292dce95-04d8-410e-9ef1-4ec52507eef9", "9ee2361b-3c0e-4b2b-8cda-e3f7ea6346d4", "38caccd9-bd35-4574-89a4-75ab2b1c26f5", "fe5e26da-70bc-44e0-81d4-37ce8c6c5a10"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Shipper:", "Geometry": {"BoundingBox": {"Width": 0.0655154213309288, "Height": 0.013902951031923294, "Left": 0.11807923018932343, "Top": 0.19845592975616455}, "Polygon": [{"X": 0.11807923018932343, "Y": 0.19845592975616455}, {"X": 0.18359462916851044, "Y": 0.19845667481422424}, {"X": 0.18359465897083282, "Y": 0.21235887706279755}, {"X": 0.11807925254106522, "Y": 0.21235813200473785}]}, "Id": "1ad5ea58-a474-479d-ad52-c59f4696734e", "Relationships": [{"Type": "CHILD", "Ids": ["0e821ad3-11e3-4a32-9d57-5fdbedec69df"]}]}, {"BlockType": "LINE", "Confidence": 74.35736083984375, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.06894475966691971, "Height": 0.010925788432359695, "Left": 0.3758789002895355, "Top": 0.19868183135986328}, "Polygon": [{"X": 0.3758789002895355, "Y": 0.19868183135986328}, {"X": 0.44482365250587463, "Y": 0.19868262112140656}, {"X": 0.44482365250587463, "Y": 0.20960763096809387}, {"X": 0.3758789002895355, "Y": 0.2096068412065506}]}, "Id": "c3f5eaa6-287b-4021-b23a-dae5362dab21", "Relationships": [{"Type": "CHILD", "Ids": ["0f040590-cead-44f4-a6c1-6a080c196194", "f18d6e1d-c505-4f11-a291-4bd57e9ec00f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Totals", "Geometry": {"BoundingBox": {"Width": 0.051575835794210434, "Height": 0.011113166809082031, "Left": 0.11736416071653366, "Top": 0.24743975698947906}, "Polygon": [{"X": 0.11736416071653366, "Y": 0.24743975698947906}, {"X": 0.1689399778842926, "Y": 0.24744035303592682}, {"X": 0.1689399927854538, "Y": 0.2585529088973999}, {"X": 0.11736417561769485, "Y": 0.25855231285095215}]}, "Id": "1b986406-c7cd-44af-9e9e-b0f768667ab3", "Relationships": [{"Type": "CHILD", "Ids": ["d820e90d-ac3e-44a8-982a-cd52d38e29c9"]}]}, {"BlockType": "LINE", "Confidence": 99.89546203613281, "Text": "Total Weight:", "Geometry": {"BoundingBox": {"Width": 0.10251712799072266, "Height": 0.013923395425081253, "Left": 0.11737159639596939, "Top": 0.2678735852241516}, "Polygon": [{"X": 0.11737159639596939, "Y": 0.2678735852241516}, {"X": 0.21988870203495026, "Y": 0.2678747773170471}, {"X": 0.21988871693611145, "Y": 0.28179699182510376}, {"X": 0.11737161874771118, "Y": 0.28179579973220825}]}, "Id": "72041cb5-f031-4b3b-b097-8de3fdbe845d", "Relationships": [{"Type": "CHILD", "Ids": ["031e2f55-2947-4769-9011-46435287357e", "f0e59ec5-f956-490f-bb44-faaf99ff893d"]}]}, {"BlockType": "LINE", "Confidence": 96.4003677368164, "Text": "1524 lbs", "Geometry": {"BoundingBox": {"Width": 0.06652414798736572, "Height": 0.010997388511896133, "Left": 0.3759978413581848, "Top": 0.2682224214076996}, "Polygon": [{"X": 0.3759978413581848, "Y": 0.2682224214076996}, {"X": 0.44252198934555054, "Y": 0.26822319626808167}, {"X": 0.44252198934555054, "Y": 0.2792198061943054}, {"X": 0.3759978413581848, "Y": 0.27921903133392334}]}, "Id": "3f27fac1-2f93-4784-8c05-121352190d86", "Relationships": [{"Type": "CHILD", "Ids": ["5d08961b-db4f-47f3-8c7a-4c7ffb34b959", "59f1e29f-c5bd-4df8-91f5-eaffe52861d4"]}]}, {"BlockType": "LINE", "Confidence": 99.89044952392578, "Text": "Total Rated Dimensions:", "Geometry": {"BoundingBox": {"Width": 0.1937260776758194, "Height": 0.011108738370239735, "Left": 0.1173943504691124, "Top": 0.28832706809043884}, "Polygon": [{"X": 0.1173943504691124, "Y": 0.28832706809043884}, {"X": 0.3111204206943512, "Y": 0.2883293330669403}, {"X": 0.3111204206943512, "Y": 0.2994357943534851}, {"X": 0.11739436537027359, "Y": 0.29943352937698364}]}, "Id": "1ea41e23-1933-4a88-ad15-5e8703fac307", "Relationships": [{"Type": "CHILD", "Ids": ["42b17bcd-5687-4db5-b8b7-78f50dc079c2", "a3043ef5-1688-4a31-9d96-1017c59ad286", "23137037-df38-4bc4-ad4f-41f8741955b6"]}]}, {"BlockType": "LINE", "Confidence": 98.83544921875, "Text": "96 L X 80 W X 90 H", "Geometry": {"BoundingBox": {"Width": 0.15766525268554688, "Height": 0.011332605965435505, "Left": 0.3752478063106537, "Top": 0.28813883662223816}, "Polygon": [{"X": 0.3752478063106537, "Y": 0.28813883662223816}, {"X": 0.532913088798523, "Y": 0.2881406843662262}, {"X": 0.5329130291938782, "Y": 0.2994714379310608}, {"X": 0.3752478063106537, "Y": 0.29946959018707275}]}, "Id": "a82fdb2e-55b8-48e1-bbef-a743a0685381", "Relationships": [{"Type": "CHILD", "Ids": ["5dc26f1b-5186-49e8-9386-6620bdf65b88", "7ad200b9-163c-4f04-9838-03d75c59e04d", "c2240191-e83d-45ca-94cb-f3da699afcfd", "7fb5e666-65a0-4845-bb43-9b758c405141", "3b8aa582-0795-4a8b-8290-1e864d819482", "ff807e56-5573-42d3-a4dc-9d49683a93a3", "2e04b6dc-6a0c-421c-80d4-7ed73fbdb7d3", "30bdee4a-85da-4631-bebb-7f287d9350b5"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Total Mileage:", "Geometry": {"BoundingBox": {"Width": 0.10955259203910828, "Height": 0.01384307723492384, "Left": 0.11740542203187943, "Top": 0.30893585085868835}, "Polygon": [{"X": 0.11740542203187943, "Y": 0.30893585085868835}, {"X": 0.2269580066204071, "Y": 0.308937132358551}, {"X": 0.2269580215215683, "Y": 0.3227789103984833}, {"X": 0.11740544438362122, "Y": 0.3227776288986206}]}, "Id": "edd8e93a-89b4-4c4b-b80a-f891b8385b13", "Relationships": [{"Type": "CHILD", "Ids": ["2500b0de-8f02-4b5c-890d-7a783b906443", "9e9bb16b-c50a-43c5-a35b-77947f280162"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "585 Miles", "Geometry": {"BoundingBox": {"Width": 0.07598401606082916, "Height": 0.01110181026160717, "Left": 0.3751773536205292, "Top": 0.3089587092399597}, "Polygon": [{"X": 0.3751773536205292, "Y": 0.3089587092399597}, {"X": 0.45116138458251953, "Y": 0.30895960330963135}, {"X": 0.45116138458251953, "Y": 0.32006052136421204}, {"X": 0.3751773536205292, "Y": 0.3200596272945404}]}, "Id": "1e1f2d74-33eb-4e1e-86d9-8976be1dae3a", "Relationships": [{"Type": "CHILD", "Ids": ["dd96263c-eea6-4b1e-97fe-683fe4af0c35", "ce4c297f-970a-4e70-93a6-4a1761d0ee08"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Rated Cube:", "Geometry": {"BoundingBox": {"Width": 0.14289678633213043, "Height": 0.011265255510807037, "Left": 0.11745674163103104, "Top": 0.329391211271286}, "Polygon": [{"X": 0.11745674163103104, "Y": 0.329391211271286}, {"X": 0.26035353541374207, "Y": 0.3293929398059845}, {"X": 0.26035353541374207, "Y": 0.34065648913383484}, {"X": 0.11745676398277283, "Y": 0.34065476059913635}]}, "Id": "3021c7e9-6c2f-407f-a425-c9da44707984", "Relationships": [{"Type": "CHILD", "Ids": ["b5738245-b31c-48b3-b188-2f0e76cbd2e0", "f14b500b-077b-4fc4-b8f1-27ff7a6226dd", "44af0026-36bb-4440-bf0e-fefc134f2edd"]}]}, {"BlockType": "LINE", "Confidence": 99.97694396972656, "Text": "400 Cubic Feet", "Geometry": {"BoundingBox": {"Width": 0.1219046339392662, "Height": 0.011335556395351887, "Left": 0.37501901388168335, "Top": 0.3294031322002411}, "Polygon": [{"X": 0.37501901388168335, "Y": 0.3294031322002411}, {"X": 0.49692365527153015, "Y": 0.3294045627117157}, {"X": 0.49692365527153015, "Y": 0.3407386839389801}, {"X": 0.37501901388168335, "Y": 0.3407372236251831}]}, "Id": "8198084d-9029-40d7-a09e-0578335091ef", "Relationships": [{"Type": "CHILD", "Ids": ["38cc621a-1ed9-46fe-8f3a-abe6ce40ac9d", "45c004da-3475-4831-b7f5-6e7104652d7f", "99e0a411-d22a-4238-8d62-9d324b1a7c3c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Rated Density:", "Geometry": {"BoundingBox": {"Width": 0.16035030782222748, "Height": 0.013428212143480778, "Left": 0.11742996424436569, "Top": 0.35024720430374146}, "Polygon": [{"X": 0.11742996424436569, "Y": 0.35024720430374146}, {"X": 0.2777802646160126, "Y": 0.35024914145469666}, {"X": 0.2777802646160126, "Y": 0.36367541551589966}, {"X": 0.11742998659610748, "Y": 0.36367347836494446}]}, "Id": "82dc3770-cd26-4c13-9892-205c541295f5", "Relationships": [{"Type": "CHILD", "Ids": ["ab2c0f3e-9f3d-4f06-9662-97a51206371c", "69cbb231-eb83-4c29-9f92-c63895015ebf", "04d37140-86a3-40d3-965c-502214d07758"]}]}, {"BlockType": "LINE", "Confidence": 99.9462890625, "Text": "3.81 PCF", "Geometry": {"BoundingBox": {"Width": 0.07562465220689774, "Height": 0.011033754795789719, "Left": 0.374855637550354, "Top": 0.35017815232276917}, "Polygon": [{"X": 0.374855637550354, "Y": 0.35017815232276917}, {"X": 0.45048031210899353, "Y": 0.3501790761947632}, {"X": 0.45048028230667114, "Y": 0.3612119257450104}, {"X": 0.374855637550354, "Y": 0.36121100187301636}]}, "Id": "4e550386-facd-4198-b0c2-f426d4074e8a", "Relationships": [{"Type": "CHILD", "Ids": ["6286ad4a-0670-427e-ba0c-fb3d677f1139", "aee47d8a-8da4-49b1-b50c-266c4a59cfdf"]}]}, {"BlockType": "LINE", "Confidence": 99.97608947753906, "Text": "Trailer Measurements Done By Dock", "Geometry": {"BoundingBox": {"Width": 0.31160902976989746, "Height": 0.013894562609493732, "Left": 0.11739374697208405, "Top": 0.39880236983299255}, "Polygon": [{"X": 0.11739374697208405, "Y": 0.39880236983299255}, {"X": 0.4290027618408203, "Y": 0.3988061845302582}, {"X": 0.4290027618408203, "Y": 0.4126969575881958}, {"X": 0.11739376932382584, "Y": 0.4126931428909302}]}, "Id": "f8ae79c0-1210-4ae1-a06c-e4e597ab3a25", "Relationships": [{"Type": "CHILD", "Ids": ["3c9021f7-e377-47ef-8a35-c90dec4ab709", "c52b6d3d-4bb3-4684-802b-8f0ee4bfef12", "6aadc228-12bf-4567-9ca5-f153e3812a6a", "c870f086-ae75-4f2a-9ff8-55f11cb3c4f4", "ceaee963-8edc-4095-ac5f-08cdacb57f7a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Name", "Geometry": {"BoundingBox": {"Width": 0.047138530761003494, "Height": 0.011117014102637768, "Left": 0.21916338801383972, "Top": 0.41947993636131287}, "Polygon": [{"X": 0.21916338801383972, "Y": 0.41947993636131287}, {"X": 0.2663019001483917, "Y": 0.41948050260543823}, {"X": 0.2663019299507141, "Y": 0.4305969476699829}, {"X": 0.21916340291500092, "Y": 0.43059635162353516}]}, "Id": "5124d947-af62-470d-9ed4-306637e3b86e", "Relationships": [{"Type": "CHILD", "Ids": ["c0164d68-a977-4ef3-a70a-45f540f73e6b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Station", "Geometry": {"BoundingBox": {"Width": 0.054860107600688934, "Height": 0.011255352757871151, "Left": 0.472452312707901, "Top": 0.41941818594932556}, "Polygon": [{"X": 0.472452312707901, "Y": 0.41941818594932556}, {"X": 0.5273123979568481, "Y": 0.4194188714027405}, {"X": 0.5273123979568481, "Y": 0.4306735396385193}, {"X": 0.472452312707901, "Y": 0.43067288398742676}]}, "Id": "b55c1a57-c28e-4f86-b384-b39324552263", "Relationships": [{"Type": "CHILD", "Ids": ["ba222e9d-598b-4825-be7e-4f181851be51"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.036779239773750305, "Height": 0.01097977813333273, "Left": 0.7396636605262756, "Top": 0.41967999935150146}, "Polygon": [{"X": 0.7396636605262756, "Y": 0.41967999935150146}, {"X": 0.7764428853988647, "Y": 0.4196804463863373}, {"X": 0.77644282579422, "Y": 0.43065977096557617}, {"X": 0.7396636605262756, "Y": 0.43065932393074036}]}, "Id": "1cab0b6d-ab9a-4b2f-a595-614638fdfbff", "Relationships": [{"Type": "CHILD", "Ids": ["f462a5ae-a58a-4438-ad06-797554227e9a"]}]}, {"BlockType": "LINE", "Confidence": 99.81086730957031, "Text": "<PERSON>", "Geometry": {"BoundingBox": {"Width": 0.09609071165323257, "Height": 0.011131948791444302, "Left": 0.19454851746559143, "Top": 0.43979141116142273}, "Polygon": [{"X": 0.19454851746559143, "Y": 0.43979141116142273}, {"X": 0.2906392216682434, "Y": 0.43979260325431824}, {"X": 0.2906392216682434, "Y": 0.450923353433609}, {"X": 0.19454853236675262, "Y": 0.4509221613407135}]}, "Id": "4c520646-6c25-4f38-8724-149d472f05a7", "Relationships": [{"Type": "CHILD", "Ids": ["33720f77-856a-4cd9-b9a6-76b33d8eaad2", "3377938a-829e-488c-a6c4-e601d80a89d4"]}]}, {"BlockType": "LINE", "Confidence": 97.7746810913086, "Text": "039 - Milwaukee, WI", "Geometry": {"BoundingBox": {"Width": 0.16153322160243988, "Height": 0.012806203216314316, "Left": 0.41877132654190063, "Top": 0.4401106536388397}, "Polygon": [{"X": 0.41877132654190063, "Y": 0.4401106536388397}, {"X": 0.5803045630455017, "Y": 0.4401126503944397}, {"X": 0.5803045630455017, "Y": 0.45291686058044434}, {"X": 0.41877132654190063, "Y": 0.45291486382484436}]}, "Id": "5913cf0b-9cc9-46c3-be1e-dec435ef0ca1", "Relationships": [{"Type": "CHILD", "Ids": ["02976f73-5c0f-4933-93f1-81963f2f9bd5", "2b7064f2-a5f2-43d1-bb8d-38f260fdb5e6", "12de9ca2-33d0-4ca9-84b7-fdcc3cfae9f5", "3c26f327-0381-4e9b-a8fc-2971555c38cb"]}]}, {"BlockType": "LINE", "Confidence": 99.82747650146484, "Text": "09/15/2025 2:50 PM", "Geometry": {"BoundingBox": {"Width": 0.16168177127838135, "Height": 0.011347051709890366, "Left": 0.6771408915519714, "Top": 0.43976184725761414}, "Polygon": [{"X": 0.6771409511566162, "Y": 0.43976184725761414}, {"X": 0.8388226628303528, "Y": 0.4397638440132141}, {"X": 0.838822603225708, "Y": 0.4511089026927948}, {"X": 0.6771408915519714, "Y": 0.45110687613487244}]}, "Id": "15465403-a25d-40a7-83b3-c3bee813e0b5", "Relationships": [{"Type": "CHILD", "Ids": ["65d360dc-89b2-420c-a0b7-d32e578ffc81", "76db097c-290b-4beb-be5d-2ee7db63a112", "61d4b7bd-70f8-4dc5-98aa-3250b83ab978"]}]}, {"BlockType": "LINE", "Confidence": 99.95814514160156, "Text": "Dimensions outlined above are actual dimensions and may be subject to Item 110 paragraph 14 for", "Geometry": {"BoundingBox": {"Width": 0.5817596316337585, "Height": 0.011057156138122082, "Left": 0.08938408643007278, "Top": 0.489078551530838}, "Polygon": [{"X": 0.08938408643007278, "Y": 0.489078551530838}, {"X": 0.6711437106132507, "Y": 0.489085853099823}, {"X": 0.671143651008606, "Y": 0.5001357197761536}, {"X": 0.08938410878181458, "Y": 0.5001283884048462}]}, "Id": "7ba24b88-fbaf-42d7-bab5-aabf155bdba9", "Relationships": [{"Type": "CHILD", "Ids": ["5032562a-537c-4647-8769-b4668b782abb", "4ebc02ba-7531-430b-bb98-47e13d0456ab", "c099da1a-4d6d-43c5-bd17-44198f83f01d", "2e947555-1ee9-497d-9cb2-414aae191ce3", "dbefda0b-3696-410c-8305-324d86533d55", "6b8be775-9ab1-4682-8079-30f4d5157985", "67e6d868-a928-48ba-a17e-dff57b564487", "50370462-fae1-4f9e-96be-3677e3721564", "9c3acf5f-5028-44ef-a5e2-c8930b7f9fce", "be25d162-cc88-407d-af5c-9869d21b2c09", "34e20427-ccf3-4c15-b13d-d1b258253e7b", "a792cf8b-ef89-42d4-92cd-0230a3f6210e", "f12cde54-0c98-4cc0-bd65-cffcc23c0020", "7c648561-5275-46c9-a7b8-c46dfa72c1dc", "8fa9a136-6714-44bd-84a1-0c8c03589427", "aa88b666-2938-4f2a-80f6-97e74084bda1"]}]}, {"BlockType": "LINE", "Confidence": 99.97017669677734, "Text": "calculating effective occupancy.", "Geometry": {"BoundingBox": {"Width": 0.18842099606990814, "Height": 0.010935102589428425, "Left": 0.08917249739170074, "Top": 0.5028409361839294}, "Polygon": [{"X": 0.08917249739170074, "Y": 0.5028409361839294}, {"X": 0.2775934934616089, "Y": 0.5028433203697205}, {"X": 0.2775934934616089, "Y": 0.5137760639190674}, {"X": 0.08917251974344254, "Y": 0.5137736797332764}]}, "Id": "158a7243-0957-407d-bf94-64f3777cff0d", "Relationships": [{"Type": "CHILD", "Ids": ["c1e22abb-9e06-436e-90bb-04dbc7e83665", "9035eb14-4dbd-48fa-8217-5a8c1c491840", "6bd69de2-d6ec-4506-90ef-487078e16bf7"]}]}, {"BlockType": "WORD", "Confidence": 74.48072814941406, "Text": "®", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006339469458907843, "Height": 0.005026033613830805, "Left": 0.34887465834617615, "Top": 0.08214132487773895}, "Polygon": [{"X": 0.34887465834617615, "Y": 0.08214132487773895}, {"X": 0.35521411895751953, "Y": 0.08214139193296432}, {"X": 0.35521411895751953, "Y": 0.08716735988855362}, {"X": 0.34887465834617615, "Y": 0.08716728538274765}]}, "Id": "565c994b-db8d-42bc-a1d3-85d098e64648"}, {"BlockType": "WORD", "Confidence": 93.14971923828125, "Text": "ABF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16267691552639008, "Height": 0.0427052304148674, "Left": 0.1905127316713333, "Top": 0.08202743530273438}, "Polygon": [{"X": 0.1905127316713333, "Y": 0.08202743530273438}, {"X": 0.3531896471977234, "Y": 0.08202920854091644}, {"X": 0.3531896471977234, "Y": 0.12473266571760178}, {"X": 0.1905127912759781, "Y": 0.12473086267709732}]}, "Id": "b0b2d25e-3dfb-4d62-952f-c4d9d1d2e83f"}, {"BlockType": "WORD", "Confidence": 99.28890228271484, "Text": "DIMENSION/CUBE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.33178985118865967, "Height": 0.022381968796253204, "Left": 0.4926004111766815, "Top": 0.09914660453796387}, "Polygon": [{"X": 0.4926004409790039, "Y": 0.09914660453796387}, {"X": 0.8243902325630188, "Y": 0.09915024042129517}, {"X": 0.824390172958374, "Y": 0.12152857333421707}, {"X": 0.4926004111766815, "Y": 0.12152490764856339}]}, "Id": "f116586d-eb17-4075-87eb-6d58f8e33fbd"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06476163864135742, "Height": 0.013669872656464577, "Left": 0.26666852831840515, "Top": 0.12770748138427734}, "Polygon": [{"X": 0.26666852831840515, "Y": 0.12770748138427734}, {"X": 0.3314301669597626, "Y": 0.12770819664001465}, {"X": 0.3314301669597626, "Y": 0.14137735962867737}, {"X": 0.26666852831840515, "Y": 0.14137662947177887}]}, "Id": "2c689231-de23-4d1d-9965-8c1f31958387"}, {"BlockType": "WORD", "Confidence": 98.82222747802734, "Text": "CERTIFICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2919008433818817, "Height": 0.018494024872779846, "Left": 0.5108564496040344, "Top": 0.12465068697929382}, "Polygon": [{"X": 0.5108565092086792, "Y": 0.12465068697929382}, {"X": 0.8027573227882385, "Y": 0.12465392053127289}, {"X": 0.8027572631835938, "Y": 0.14314471185207367}, {"X": 0.5108564496040344, "Y": 0.1431414633989334}]}, "Id": "56c8a4a7-a659-4395-a2fc-ec2e3e461afc"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "An", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01786676049232483, "Height": 0.008850419893860817, "Left": 0.1923685520887375, "Top": 0.150098979473114}, "Polygon": [{"X": 0.1923685520887375, "Y": 0.150098979473114}, {"X": 0.21023531258106232, "Y": 0.15009917318820953}, {"X": 0.21023531258106232, "Y": 0.15894940495491028}, {"X": 0.19236856698989868, "Y": 0.15894919633865356}]}, "Id": "bba23112-7f87-4e99-8f60-21e9aa94bfb4"}, {"BlockType": "WORD", "Confidence": 92.10857391357422, "Text": "ArcBest", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05668032541871071, "Height": 0.009282219223678112, "Left": 0.21459156274795532, "Top": 0.14991319179534912}, "Polygon": [{"X": 0.21459156274795532, "Y": 0.14991319179534912}, {"X": 0.27127188444137573, "Y": 0.14991383254528046}, {"X": 0.27127188444137573, "Y": 0.1591954231262207}, {"X": 0.21459157764911652, "Y": 0.15919478237628937}]}, "Id": "ec608b0a-04a2-4f87-977a-e8e0565d8224"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Company", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06225316971540451, "Height": 0.011639096774160862, "Left": 0.275318443775177, "Top": 0.1498134434223175}, "Polygon": [{"X": 0.275318443775177, "Y": 0.1498134434223175}, {"X": 0.3375716209411621, "Y": 0.14981412887573242}, {"X": 0.3375716209411621, "Y": 0.1614525318145752}, {"X": 0.2753184735774994, "Y": 0.16145183145999908}]}, "Id": "cd3f26f7-8570-49b3-bcca-3bf98b8d4969"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Pronumber:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09193293750286102, "Height": 0.010984220542013645, "Left": 0.11849311739206314, "Top": 0.1780705749988556}, "Polygon": [{"X": 0.11849311739206314, "Y": 0.1780705749988556}, {"X": 0.21042604744434357, "Y": 0.17807160317897797}, {"X": 0.21042606234550476, "Y": 0.18905478715896606}, {"X": 0.11849313229322433, "Y": 0.1890537440776825}]}, "Id": "56a76e2f-2378-4a58-a34e-d1217e7f96fc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "039721315", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08949947357177734, "Height": 0.010717591270804405, "Left": 0.3751376271247864, "Top": 0.17839008569717407}, "Polygon": [{"X": 0.3751376271247864, "Y": 0.17839008569717407}, {"X": 0.4646371006965637, "Y": 0.17839109897613525}, {"X": 0.4646371006965637, "Y": 0.18910767138004303}, {"X": 0.3751376271247864, "Y": 0.18910665810108185}]}, "Id": "824711bf-6c3d-4163-9b84-13d51fed190c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05114024505019188, "Height": 0.011007430963218212, "Left": 0.6320160031318665, "Top": 0.1780363917350769}, "Polygon": [{"X": 0.6320160627365112, "Y": 0.1780363917350769}, {"X": 0.6831562519073486, "Y": 0.17803697288036346}, {"X": 0.6831562519073486, "Y": 0.1890438199043274}, {"X": 0.6320160031318665, "Y": 0.18904323875904083}]}, "Id": "292dce95-04d8-410e-9ef1-4ec52507eef9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02622046321630478, "Height": 0.010956352576613426, "Left": 0.6885656714439392, "Top": 0.17810016870498657}, "Polygon": [{"X": 0.688565731048584, "Y": 0.17810016870498657}, {"X": 0.714786171913147, "Y": 0.17810045182704926}, {"X": 0.7147861123085022, "Y": 0.18905651569366455}, {"X": 0.6885656714439392, "Y": 0.18905621767044067}]}, "Id": "9ee2361b-3c0e-4b2b-8cda-e3f7ea6346d4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "CITY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04056987911462784, "Height": 0.0111733078956604, "Left": 0.7208412289619446, "Top": 0.17781390249729156}, "Polygon": [{"X": 0.7208412289619446, "Y": 0.17781390249729156}, {"X": 0.7614110708236694, "Y": 0.17781436443328857}, {"X": 0.7614110708236694, "Y": 0.18898721039295197}, {"X": 0.7208412289619446, "Y": 0.18898674845695496}]}, "Id": "38caccd9-bd35-4574-89a4-75ab2b1c26f5"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "400177", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060013268142938614, "Height": 0.010922485962510109, "Left": 0.7660653591156006, "Top": 0.17824429273605347}, "Polygon": [{"X": 0.7660653591156006, "Y": 0.17824429273605347}, {"X": 0.8260785937309265, "Y": 0.17824497818946838}, {"X": 0.8260785937309265, "Y": 0.18916678428649902}, {"X": 0.7660653591156006, "Y": 0.1891660988330841}]}, "Id": "fe5e26da-70bc-44e0-81d4-37ce8c6c5a10"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Shipper:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0655154213309288, "Height": 0.013902951031923294, "Left": 0.11807923018932343, "Top": 0.19845592975616455}, "Polygon": [{"X": 0.11807923018932343, "Y": 0.19845592975616455}, {"X": 0.18359462916851044, "Y": 0.19845667481422424}, {"X": 0.18359465897083282, "Y": 0.21235887706279755}, {"X": 0.11807925254106522, "Y": 0.21235813200473785}]}, "Id": "0e821ad3-11e3-4a32-9d57-5fdbedec69df"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05457213893532753, "Height": 0.010898514650762081, "Left": 0.3758789002895355, "Top": 0.19870895147323608}, "Polygon": [{"X": 0.3758789002895355, "Y": 0.19870895147323608}, {"X": 0.43045103549957275, "Y": 0.19870956242084503}, {"X": 0.43045103549957275, "Y": 0.20960746705532074}, {"X": 0.3758789002895355, "Y": 0.2096068412065506}]}, "Id": "0f040590-cead-44f4-a6c1-6a080c196194"}, {"BlockType": "WORD", "Confidence": 48.75458526611328, "Text": "Il", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008359834551811218, "Height": 0.010732059367001057, "Left": 0.4364638328552246, "Top": 0.1986825317144394}, "Polygon": [{"X": 0.4364638328552246, "Y": 0.1986825317144394}, {"X": 0.44482365250587463, "Y": 0.19868262112140656}, {"X": 0.44482365250587463, "Y": 0.20941458642482758}, {"X": 0.4364638328552246, "Y": 0.20941448211669922}]}, "Id": "f18d6e1d-c505-4f11-a291-4bd57e9ec00f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Totals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051575835794210434, "Height": 0.011113166809082031, "Left": 0.11736416071653366, "Top": 0.24743975698947906}, "Polygon": [{"X": 0.11736416071653366, "Y": 0.24743975698947906}, {"X": 0.1689399778842926, "Y": 0.24744035303592682}, {"X": 0.1689399927854538, "Y": 0.2585529088973999}, {"X": 0.11736417561769485, "Y": 0.25855231285095215}]}, "Id": "d820e90d-ac3e-44a8-982a-cd52d38e29c9"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037716351449489594, "Height": 0.0111613217741251, "Left": 0.11737159639596939, "Top": 0.26795804500579834}, "Polygon": [{"X": 0.11737159639596939, "Y": 0.26795804500579834}, {"X": 0.1550879329442978, "Y": 0.26795846223831177}, {"X": 0.15508794784545898, "Y": 0.2791193425655365}, {"X": 0.11737161129713058, "Y": 0.27911892533302307}]}, "Id": "031e2f55-2947-4769-9011-46435287357e"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05961133912205696, "Height": 0.01392289623618126, "Left": 0.1602773815393448, "Top": 0.2678740918636322}, "Polygon": [{"X": 0.1602773815393448, "Y": 0.2678740918636322}, {"X": 0.21988870203495026, "Y": 0.2678747773170471}, {"X": 0.21988871693611145, "Y": 0.28179699182510376}, {"X": 0.16027739644050598, "Y": 0.28179630637168884}]}, "Id": "f0e59ec5-f956-490f-bb44-faaf99ff893d"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "1524", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03900344297289848, "Height": 0.010997066274285316, "Left": 0.3759978413581848, "Top": 0.2682224214076996}, "Polygon": [{"X": 0.3759978413581848, "Y": 0.2682224214076996}, {"X": 0.4150012731552124, "Y": 0.2682228982448578}, {"X": 0.4150012731552124, "Y": 0.27921950817108154}, {"X": 0.3759978413581848, "Y": 0.27921903133392334}]}, "Id": "5d08961b-db4f-47f3-8c7a-4c7ffb34b959"}, {"BlockType": "WORD", "Confidence": 92.90816497802734, "Text": "lbs", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022185446694493294, "Height": 0.010823125019669533, "Left": 0.4203365445137024, "Top": 0.2682436406612396}, "Polygon": [{"X": 0.4203365445137024, "Y": 0.2682436406612396}, {"X": 0.44252198934555054, "Y": 0.2682439088821411}, {"X": 0.44252198934555054, "Y": 0.2790667712688446}, {"X": 0.4203365445137024, "Y": 0.2790665030479431}]}, "Id": "59f1e29f-c5bd-4df8-91f5-eaffe52861d4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03774109482765198, "Height": 0.011106900870800018, "Left": 0.1173943504691124, "Top": 0.28832706809043884}, "Polygon": [{"X": 0.1173943504691124, "Y": 0.28832706809043884}, {"X": 0.15513543784618378, "Y": 0.28832751512527466}, {"X": 0.15513545274734497, "Y": 0.29943397641181946}, {"X": 0.11739436537027359, "Y": 0.29943352937698364}]}, "Id": "42b17bcd-5687-4db5-b8b7-78f50dc079c2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Rated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04678768664598465, "Height": 0.011021428741514683, "Left": 0.16140106320381165, "Top": 0.2883329689502716}, "Polygon": [{"X": 0.16140106320381165, "Y": 0.2883329689502716}, {"X": 0.2081887274980545, "Y": 0.2883335053920746}, {"X": 0.2081887423992157, "Y": 0.2993543744087219}, {"X": 0.16140107810497284, "Y": 0.29935383796691895}]}, "Id": "a3043ef5-1688-4a31-9d96-1017c59ad286"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Dimensions:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09686389565467834, "Height": 0.011000875383615494, "Left": 0.21425652503967285, "Top": 0.2883497476577759}, "Polygon": [{"X": 0.21425652503967285, "Y": 0.2883497476577759}, {"X": 0.3111204206943512, "Y": 0.2883508801460266}, {"X": 0.3111204206943512, "Y": 0.2993506193161011}, {"X": 0.21425653994083405, "Y": 0.29934948682785034}]}, "Id": "23137037-df38-4bc4-ad4f-41f8741955b6"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "96", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019508909434080124, "Height": 0.010741621255874634, "Left": 0.3752478063106537, "Top": 0.2887282073497772}, "Polygon": [{"X": 0.3752478063106537, "Y": 0.2887282073497772}, {"X": 0.3947567045688629, "Y": 0.2887284457683563}, {"X": 0.3947567045688629, "Y": 0.29946982860565186}, {"X": 0.3752478063106537, "Y": 0.29946959018707275}]}, "Id": "5dc26f1b-5186-49e8-9386-6620bdf65b88"}, {"BlockType": "WORD", "Confidence": 99.46050262451172, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009842955507338047, "Height": 0.010726122185587883, "Left": 0.40027883648872375, "Top": 0.2884473204612732}, "Polygon": [{"X": 0.40027883648872375, "Y": 0.2884473204612732}, {"X": 0.41012176871299744, "Y": 0.28844743967056274}, {"X": 0.41012176871299744, "Y": 0.2991734445095062}, {"X": 0.40027883648872375, "Y": 0.2991733253002167}]}, "Id": "7ad200b9-163c-4f04-9838-03d75c59e04d"}, {"BlockType": "WORD", "Confidence": 98.76654052734375, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01181031670421362, "Height": 0.0109790014103055, "Left": 0.41498586535453796, "Top": 0.2882789373397827}, "Polygon": [{"X": 0.41498586535453796, "Y": 0.2882789373397827}, {"X": 0.42679616808891296, "Y": 0.28827905654907227}, {"X": 0.42679616808891296, "Y": 0.29925793409347534}, {"X": 0.41498586535453796, "Y": 0.2992577850818634}]}, "Id": "c2240191-e83d-45ca-94cb-f3da699afcfd"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "80", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01950649917125702, "Height": 0.01073036901652813, "Left": 0.43227505683898926, "Top": 0.2886411249637604}, "Polygon": [{"X": 0.43227505683898926, "Y": 0.2886411249637604}, {"X": 0.4517815411090851, "Y": 0.2886413633823395}, {"X": 0.4517815411090851, "Y": 0.29937151074409485}, {"X": 0.43227505683898926, "Y": 0.29937127232551575}]}, "Id": "7fb5e666-65a0-4845-bb43-9b758c405141"}, {"BlockType": "WORD", "Confidence": 99.50035858154297, "Text": "W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017547812312841415, "Height": 0.010877614840865135, "Left": 0.4564078450202942, "Top": 0.2883608341217041}, "Polygon": [{"X": 0.4564078450202942, "Y": 0.2883608341217041}, {"X": 0.4739556610584259, "Y": 0.2883610427379608}, {"X": 0.4739556610584259, "Y": 0.2992384433746338}, {"X": 0.4564078450202942, "Y": 0.29923826456069946}]}, "Id": "3b8aa582-0795-4a8b-8290-1e864d819482"}, {"BlockType": "WORD", "Confidence": 97.02985382080078, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011946570128202438, "Height": 0.011149393394589424, "Left": 0.47875556349754333, "Top": 0.28814005851745605}, "Polygon": [{"X": 0.47875556349754333, "Y": 0.28814005851745605}, {"X": 0.4907021224498749, "Y": 0.288140207529068}, {"X": 0.4907021224498749, "Y": 0.2992894649505615}, {"X": 0.47875556349754333, "Y": 0.2992893159389496}]}, "Id": "ff807e56-5573-42d3-a4dc-9d49683a93a3"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "90", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019846832379698753, "Height": 0.010747736319899559, "Left": 0.49579742550849915, "Top": 0.28869038820266724}, "Polygon": [{"X": 0.49579745531082153, "Y": 0.28869038820266724}, {"X": 0.5156442523002625, "Y": 0.28869062662124634}, {"X": 0.5156442523002625, "Y": 0.29943814873695374}, {"X": 0.49579742550849915, "Y": 0.29943791031837463}]}, "Id": "2e04b6dc-6a0c-421c-80d4-7ed73fbdb7d3"}, {"BlockType": "WORD", "Confidence": 96.67829132080078, "Text": "H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011462719179689884, "Height": 0.01059288065880537, "Left": 0.5214503407478333, "Top": 0.2885550558567047}, "Polygon": [{"X": 0.5214503407478333, "Y": 0.2885550558567047}, {"X": 0.532913088798523, "Y": 0.28855517506599426}, {"X": 0.5329130291938782, "Y": 0.29914793372154236}, {"X": 0.5214503407478333, "Y": 0.2991477847099304}]}, "Id": "30bdee4a-85da-4631-bebb-7f287d9350b5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037727221846580505, "Height": 0.011006463319063187, "Left": 0.11740542203187943, "Top": 0.30893585085868835}, "Polygon": [{"X": 0.11740542203187943, "Y": 0.30893585085868835}, {"X": 0.15513262152671814, "Y": 0.30893629789352417}, {"X": 0.15513263642787933, "Y": 0.31994232535362244}, {"X": 0.11740543693304062, "Y": 0.31994184851646423}]}, "Id": "2500b0de-8f02-4b5c-890d-7a783b906443"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Mileage:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06583818793296814, "Height": 0.013751119375228882, "Left": 0.16111983358860016, "Top": 0.3090278208255768}, "Polygon": [{"X": 0.16111983358860016, "Y": 0.3090278208255768}, {"X": 0.2269580066204071, "Y": 0.30902859568595886}, {"X": 0.2269580215215683, "Y": 0.3227789103984833}, {"X": 0.16111984848976135, "Y": 0.3227781355381012}]}, "Id": "9e9bb16b-c50a-43c5-a35b-77947f280162"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "585", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029458044096827507, "Height": 0.01069637294858694, "Left": 0.3751773536205292, "Top": 0.30936360359191895}, "Polygon": [{"X": 0.3751773536205292, "Y": 0.30936360359191895}, {"X": 0.40463539958000183, "Y": 0.3093639612197876}, {"X": 0.40463539958000183, "Y": 0.32005998492240906}, {"X": 0.3751773536205292, "Y": 0.3200596272945404}]}, "Id": "dd96263c-eea6-4b1e-97fe-683fe4af0c35"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04069038853049278, "Height": 0.010945414192974567, "Left": 0.41047099232673645, "Top": 0.30895912647247314}, "Polygon": [{"X": 0.41047099232673645, "Y": 0.30895912647247314}, {"X": 0.45116138458251953, "Y": 0.30895960330963135}, {"X": 0.45116138458251953, "Y": 0.31990453600883484}, {"X": 0.41047099232673645, "Y": 0.31990405917167664}]}, "Id": "ce4c297f-970a-4e70-93a6-4a1761d0ee08"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037625495344400406, "Height": 0.01116451621055603, "Left": 0.11745674163103104, "Top": 0.3294907212257385}, "Polygon": [{"X": 0.11745674163103104, "Y": 0.3294907212257385}, {"X": 0.15508222579956055, "Y": 0.32949116826057434}, {"X": 0.15508224070072174, "Y": 0.34065523743629456}, {"X": 0.11745676398277283, "Y": 0.34065476059913635}]}, "Id": "b5738245-b31c-48b3-b188-2f0e76cbd2e0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Rated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04665423557162285, "Height": 0.010991754941642284, "Left": 0.1614403873682022, "Top": 0.329606294631958}, "Polygon": [{"X": 0.1614403873682022, "Y": 0.329606294631958}, {"X": 0.20809461176395416, "Y": 0.329606831073761}, {"X": 0.20809462666511536, "Y": 0.34059804677963257}, {"X": 0.1614404022693634, "Y": 0.3405974805355072}]}, "Id": "f14b500b-077b-4fc4-b8f1-27ff7a6226dd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Cube:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046526506543159485, "Height": 0.011190352030098438, "Left": 0.21382702887058258, "Top": 0.32939237356185913}, "Polygon": [{"X": 0.21382702887058258, "Y": 0.32939237356185913}, {"X": 0.26035353541374207, "Y": 0.3293929398059845}, {"X": 0.26035353541374207, "Y": 0.3405827283859253}, {"X": 0.21382704377174377, "Y": 0.3405821621417999}]}, "Id": "44af0026-36bb-4440-bf0e-fefc134f2edd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "400", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029517151415348053, "Height": 0.010696209967136383, "Left": 0.37501901388168335, "Top": 0.33004137873649597}, "Polygon": [{"X": 0.37501901388168335, "Y": 0.33004137873649597}, {"X": 0.4045361876487732, "Y": 0.33004170656204224}, {"X": 0.4045361578464508, "Y": 0.34073758125305176}, {"X": 0.37501901388168335, "Y": 0.3407372236251831}]}, "Id": "38cc621a-1ed9-46fe-8f3a-abe6ce40ac9d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Cubic", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04572402685880661, "Height": 0.011162428185343742, "Left": 0.41022148728370667, "Top": 0.3294035494327545}, "Polygon": [{"X": 0.41022148728370667, "Y": 0.3294035494327545}, {"X": 0.45594552159309387, "Y": 0.3294040858745575}, {"X": 0.45594552159309387, "Y": 0.3405659794807434}, {"X": 0.41022148728370667, "Y": 0.34056541323661804}]}, "Id": "45c004da-3475-4831-b7f5-6e7104652d7f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Feet", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03512891009449959, "Height": 0.011038278229534626, "Left": 0.46179473400115967, "Top": 0.32962316274642944}, "Polygon": [{"X": 0.46179476380348206, "Y": 0.32962316274642944}, {"X": 0.49692365527153015, "Y": 0.32962357997894287}, {"X": 0.49692365527153015, "Y": 0.3406614363193512}, {"X": 0.46179473400115967, "Y": 0.34066101908683777}]}, "Id": "99e0a411-d22a-4238-8d62-9d324b1a7c3c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03771156445145607, "Height": 0.011049039661884308, "Left": 0.11742996424436569, "Top": 0.3502516448497772}, "Polygon": [{"X": 0.11742996424436569, "Y": 0.3502516448497772}, {"X": 0.15514151751995087, "Y": 0.35025209188461304}, {"X": 0.15514153242111206, "Y": 0.36130067706108093}, {"X": 0.11742997914552689, "Y": 0.3613002300262451}]}, "Id": "ab2c0f3e-9f3d-4f06-9662-97a51206371c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Rated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04671593755483627, "Height": 0.010824340395629406, "Left": 0.1614668071269989, "Top": 0.35025808215141296}, "Polygon": [{"X": 0.1614668071269989, "Y": 0.35025808215141296}, {"X": 0.20818272233009338, "Y": 0.35025864839553833}, {"X": 0.20818273723125458, "Y": 0.36108240485191345}, {"X": 0.1614668220281601, "Y": 0.3610818684101105}]}, "Id": "69cbb231-eb83-4c29-9f92-c63895015ebf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Density:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0635739341378212, "Height": 0.013427051715552807, "Left": 0.21420633792877197, "Top": 0.3502483665943146}, "Polygon": [{"X": 0.21420633792877197, "Y": 0.3502483665943146}, {"X": 0.2777802646160126, "Y": 0.35024914145469666}, {"X": 0.2777802646160126, "Y": 0.36367541551589966}, {"X": 0.21420635282993317, "Y": 0.3636746406555176}]}, "Id": "04d37140-86a3-40d3-965c-502214d07758"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "3.81", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03210205212235451, "Height": 0.010749034583568573, "Left": 0.374855637550354, "Top": 0.3504623770713806}, "Polygon": [{"X": 0.374855637550354, "Y": 0.3504623770713806}, {"X": 0.4069576859474182, "Y": 0.35046273469924927}, {"X": 0.4069576859474182, "Y": 0.3612113893032074}, {"X": 0.374855637550354, "Y": 0.36121100187301636}]}, "Id": "6286ad4a-0670-427e-ba0c-fb3d677f1139"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "PCF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03451605886220932, "Height": 0.011023086495697498, "Left": 0.4159642457962036, "Top": 0.35017865896224976}, "Polygon": [{"X": 0.4159642457962036, "Y": 0.35017865896224976}, {"X": 0.45048031210899353, "Y": 0.3501790761947632}, {"X": 0.45048028230667114, "Y": 0.3612017333507538}, {"X": 0.4159642457962036, "Y": 0.36120131611824036}]}, "Id": "aee47d8a-8da4-49b1-b50c-266c4a59cfdf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054132625460624695, "Height": 0.011148477904498577, "Left": 0.11739374697208405, "Top": 0.39880236983299255}, "Polygon": [{"X": 0.11739374697208405, "Y": 0.39880236983299255}, {"X": 0.17152635753154755, "Y": 0.39880305528640747}, {"X": 0.17152637243270874, "Y": 0.409950852394104}, {"X": 0.11739376187324524, "Y": 0.4099501967430115}]}, "Id": "3c9021f7-e377-47ef-8a35-c90dec4ab709"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Measurements", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12498781085014343, "Height": 0.010902028530836105, "Left": 0.17718467116355896, "Top": 0.39898207783699036}, "Polygon": [{"X": 0.17718467116355896, "Y": 0.39898207783699036}, {"X": 0.3021724820137024, "Y": 0.39898359775543213}, {"X": 0.3021724820137024, "Y": 0.40988412499427795}, {"X": 0.17718468606472015, "Y": 0.4098825752735138}]}, "Id": "c52b6d3d-4bb3-4684-802b-8f0ee4bfef12"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Done", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04396834969520569, "Height": 0.011012043803930283, "Left": 0.3079691529273987, "Top": 0.39897334575653076}, "Polygon": [{"X": 0.3079691529273987, "Y": 0.39897334575653076}, {"X": 0.35193750262260437, "Y": 0.39897388219833374}, {"X": 0.35193750262260437, "Y": 0.40998539328575134}, {"X": 0.3079691529273987, "Y": 0.40998485684394836}]}, "Id": "6aadc228-12bf-4567-9ca5-f153e3812a6a"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "By", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02240031212568283, "Height": 0.013723374344408512, "Left": 0.35793158411979675, "Top": 0.3989729881286621}, "Polygon": [{"X": 0.35793158411979675, "Y": 0.3989729881286621}, {"X": 0.3803319036960602, "Y": 0.3989732563495636}, {"X": 0.3803319036960602, "Y": 0.41269636154174805}, {"X": 0.35793158411979675, "Y": 0.41269606351852417}]}, "Id": "c870f086-ae75-4f2a-9ff8-55f11cb3c4f4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043045524507761, "Height": 0.011104841716587543, "Left": 0.3859572410583496, "Top": 0.398842453956604}, "Polygon": [{"X": 0.3859572410583496, "Y": 0.398842453956604}, {"X": 0.4290027618408203, "Y": 0.398842990398407}, {"X": 0.4290027618408203, "Y": 0.40994730591773987}, {"X": 0.3859572410583496, "Y": 0.4099467694759369}]}, "Id": "ceaee963-8edc-4095-ac5f-08cdacb57f7a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Name", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047138530761003494, "Height": 0.011117014102637768, "Left": 0.21916338801383972, "Top": 0.41947993636131287}, "Polygon": [{"X": 0.21916338801383972, "Y": 0.41947993636131287}, {"X": 0.2663019001483917, "Y": 0.41948050260543823}, {"X": 0.2663019299507141, "Y": 0.4305969476699829}, {"X": 0.21916340291500092, "Y": 0.43059635162353516}]}, "Id": "c0164d68-a977-4ef3-a70a-45f540f73e6b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Station", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054860107600688934, "Height": 0.011255352757871151, "Left": 0.472452312707901, "Top": 0.41941818594932556}, "Polygon": [{"X": 0.472452312707901, "Y": 0.41941818594932556}, {"X": 0.5273123979568481, "Y": 0.4194188714027405}, {"X": 0.5273123979568481, "Y": 0.4306735396385193}, {"X": 0.472452312707901, "Y": 0.43067288398742676}]}, "Id": "ba222e9d-598b-4825-be7e-4f181851be51"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036779239773750305, "Height": 0.01097977813333273, "Left": 0.7396636605262756, "Top": 0.41967999935150146}, "Polygon": [{"X": 0.7396636605262756, "Y": 0.41967999935150146}, {"X": 0.7764428853988647, "Y": 0.4196804463863373}, {"X": 0.77644282579422, "Y": 0.43065977096557617}, {"X": 0.7396636605262756, "Y": 0.43065932393074036}]}, "Id": "f462a5ae-a58a-4438-ad06-797554227e9a"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02944539301097393, "Height": 0.0111044617369771, "Left": 0.19454851746559143, "Top": 0.43979141116142273}, "Polygon": [{"X": 0.19454851746559143, "Y": 0.43979141116142273}, {"X": 0.22399389743804932, "Y": 0.4397917687892914}, {"X": 0.2239939123392105, "Y": 0.45089587569236755}, {"X": 0.19454853236675262, "Y": 0.4508955180644989}]}, "Id": "33720f77-856a-4cd9-b9a6-76b33d8eaad2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0616178885102272, "Height": 0.010917012579739094, "Left": 0.2290213406085968, "Top": 0.4400063455104828}, "Polygon": [{"X": 0.2290213406085968, "Y": 0.4400063455104828}, {"X": 0.2906392216682434, "Y": 0.44000712037086487}, {"X": 0.2906392216682434, "Y": 0.450923353433609}, {"X": 0.229021355509758, "Y": 0.4509226083755493}]}, "Id": "3377938a-829e-488c-a6c4-e601d80a89d4"}, {"BlockType": "WORD", "Confidence": 99.4921875, "Text": "039", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029549570754170418, "Height": 0.010683998465538025, "Left": 0.41877132654190063, "Top": 0.4403051733970642}, "Polygon": [{"X": 0.41877132654190063, "Y": 0.4403051733970642}, {"X": 0.4483208954334259, "Y": 0.44030556082725525}, {"X": 0.4483208954334259, "Y": 0.4509891867637634}, {"X": 0.41877132654190063, "Y": 0.4509888291358948}]}, "Id": "02976f73-5c0f-4933-93f1-81963f2f9bd5"}, {"BlockType": "WORD", "Confidence": 91.84331512451172, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005608774721622467, "Height": 0.0021004066802561283, "Left": 0.45371273159980774, "Top": 0.4454392194747925}, "Polygon": [{"X": 0.45371273159980774, "Y": 0.4454392194747925}, {"X": 0.4593214988708496, "Y": 0.44543930888175964}, {"X": 0.4593214988708496, "Y": 0.44753962755203247}, {"X": 0.45371273159980774, "Y": 0.4475395679473877}]}, "Id": "2b7064f2-a5f2-43d1-bb8d-38f260fdb5e6"}, {"BlockType": "WORD", "Confidence": 99.80309295654297, "Text": "Milwaukee,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08866575360298157, "Height": 0.012805301696062088, "Left": 0.46497198939323425, "Top": 0.4401112198829651}, "Polygon": [{"X": 0.46497198939323425, "Y": 0.4401112198829651}, {"X": 0.5536377429962158, "Y": 0.44011232256889343}, {"X": 0.5536377429962158, "Y": 0.45291653275489807}, {"X": 0.46497198939323425, "Y": 0.4529154300689697}]}, "Id": "12de9ca2-33d0-4ca9-84b7-fdcc3cfae9f5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02135656401515007, "Height": 0.010567082092165947, "Left": 0.5589479804039001, "Top": 0.4401193857192993}, "Polygon": [{"X": 0.5589480400085449, "Y": 0.4401193857192993}, {"X": 0.5803045630455017, "Y": 0.4401196539402008}, {"X": 0.5803045630455017, "Y": 0.4506864845752716}, {"X": 0.5589479804039001, "Y": 0.4506862163543701}]}, "Id": "3c26f327-0381-4e9b-a8fc-2971555c38cb"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "09/15/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08939097821712494, "Height": 0.01123108435422182, "Left": 0.6771408915519714, "Top": 0.43976184725761414}, "Polygon": [{"X": 0.6771409511566162, "Y": 0.43976184725761414}, {"X": 0.7665318846702576, "Y": 0.4397629499435425}, {"X": 0.7665318250656128, "Y": 0.4509929418563843}, {"X": 0.6771408915519714, "Y": 0.45099180936813354}]}, "Id": "65d360dc-89b2-420c-a0b7-d32e578ffc81"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "2:50", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034989431500434875, "Height": 0.011033323593437672, "Left": 0.7716981768608093, "Top": 0.440075159072876}, "Polygon": [{"X": 0.7716982364654541, "Y": 0.440075159072876}, {"X": 0.806687593460083, "Y": 0.4400756061077118}, {"X": 0.806687593460083, "Y": 0.45110848546028137}, {"X": 0.7716981768608093, "Y": 0.45110806822776794}]}, "Id": "76db097c-290b-4beb-be5d-2ee7db63a112"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026217132806777954, "Height": 0.010938335210084915, "Left": 0.8126055598258972, "Top": 0.4400234818458557}, "Polygon": [{"X": 0.8126055598258972, "Y": 0.4400234818458557}, {"X": 0.8388226628303528, "Y": 0.440023809671402}, {"X": 0.8388226628303528, "Y": 0.4509618282318115}, {"X": 0.8126055598258972, "Y": 0.45096150040626526}]}, "Id": "61d4b7bd-70f8-4dc5-98aa-3250b83ab978"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Dimensions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0700768306851387, "Height": 0.008775998838245869, "Left": 0.08938408643007278, "Top": 0.48916903138160706}, "Polygon": [{"X": 0.08938408643007278, "Y": 0.48916903138160706}, {"X": 0.1594609022140503, "Y": 0.4891699254512787}, {"X": 0.1594609171152115, "Y": 0.49794504046440125}, {"X": 0.08938410878181458, "Y": 0.4979441463947296}]}, "Id": "5032562a-537c-4647-8769-b4668b782abb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "outlined", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04824087396264076, "Height": 0.0086873983964324, "Left": 0.16326278448104858, "Top": 0.48918670415878296}, "Polygon": [{"X": 0.16326278448104858, "Y": 0.48918670415878296}, {"X": 0.21150365471839905, "Y": 0.4891873002052307}, {"X": 0.21150366961956024, "Y": 0.49787408113479614}, {"X": 0.16326279938220978, "Y": 0.4978734850883484}]}, "Id": "4ebc02ba-7531-430b-bb98-47e13d0456ab"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "above", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03514384105801582, "Height": 0.008688497357070446, "Left": 0.21499523520469666, "Top": 0.48921558260917664}, "Polygon": [{"X": 0.21499523520469666, "Y": 0.48921558260917664}, {"X": 0.2501390874385834, "Y": 0.48921602964401245}, {"X": 0.2501390874385834, "Y": 0.49790409207344055}, {"X": 0.21499525010585785, "Y": 0.49790364503860474}]}, "Id": "c099da1a-4d6d-43c5-bd17-44198f83f01d"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018027108162641525, "Height": 0.006227880250662565, "Left": 0.2538045346736908, "Top": 0.4915772080421448}, "Polygon": [{"X": 0.2538045346736908, "Y": 0.4915772080421448}, {"X": 0.2718316316604614, "Y": 0.4915774464607239}, {"X": 0.2718316316604614, "Y": 0.49780508875846863}, {"X": 0.2538045346736908, "Y": 0.4978048503398895}]}, "Id": "2e947555-1ee9-497d-9cb2-414aae191ce3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "actual", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03468172997236252, "Height": 0.008566259406507015, "Left": 0.2755959630012512, "Top": 0.4892851412296295}, "Polygon": [{"X": 0.2755959630012512, "Y": 0.4892851412296295}, {"X": 0.31027770042419434, "Y": 0.48928558826446533}, {"X": 0.31027770042419434, "Y": 0.4978514015674591}, {"X": 0.2755959630012512, "Y": 0.4978509545326233}]}, "Id": "dbefda0b-3696-410c-8305-324d86533d55"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "dimensions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06713207811117172, "Height": 0.008750570937991142, "Left": 0.31414371728897095, "Top": 0.48908138275146484}, "Polygon": [{"X": 0.31414371728897095, "Y": 0.48908138275146484}, {"X": 0.38127580285072327, "Y": 0.4890822172164917}, {"X": 0.38127580285072327, "Y": 0.49783194065093994}, {"X": 0.31414371728897095, "Y": 0.4978311061859131}]}, "Id": "6b8be775-9ab1-4682-8079-30f4d5157985"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021065007895231247, "Height": 0.008394727483391762, "Left": 0.38497963547706604, "Top": 0.4893995523452759}, "Polygon": [{"X": 0.38497963547706604, "Y": 0.4893995523452759}, {"X": 0.4060446619987488, "Y": 0.48939982056617737}, {"X": 0.4060446619987488, "Y": 0.4977942705154419}, {"X": 0.38497963547706604, "Y": 0.4977940022945404}]}, "Id": "67e6d868-a928-48ba-a17e-dff57b564487"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025389039888978004, "Height": 0.008323672227561474, "Left": 0.4096216857433319, "Top": 0.4916723072528839}, "Polygon": [{"X": 0.4096216857433319, "Y": 0.4916723072528839}, {"X": 0.43501073122024536, "Y": 0.4916726052761078}, {"X": 0.43501073122024536, "Y": 0.49999597668647766}, {"X": 0.4096216857433319, "Y": 0.4999956488609314}]}, "Id": "50370462-fae1-4f9e-96be-3677e3721564"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014134123921394348, "Height": 0.008610333316028118, "Left": 0.43843531608581543, "Top": 0.4891878366470337}, "Polygon": [{"X": 0.43843531608581543, "Y": 0.4891878366470337}, {"X": 0.4525694251060486, "Y": 0.489188015460968}, {"X": 0.4525694251060486, "Y": 0.4977981746196747}, {"X": 0.43843531608581543, "Y": 0.49779799580574036}]}, "Id": "9c3acf5f-5028-44ef-a5e2-c8930b7f9fce"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "subject", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041990749537944794, "Height": 0.010886943899095058, "Left": 0.4561784565448761, "Top": 0.48916417360305786}, "Polygon": [{"X": 0.4561784565448761, "Y": 0.48916417360305786}, {"X": 0.4981691837310791, "Y": 0.48916468024253845}, {"X": 0.4981691837310791, "Y": 0.5000510811805725}, {"X": 0.4561784565448761, "Y": 0.5000506043434143}]}, "Id": "be25d162-cc88-407d-af5c-9869d21b2c09"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011592037975788116, "Height": 0.007321405224502087, "Left": 0.501384973526001, "Top": 0.49053022265434265}, "Polygon": [{"X": 0.501384973526001, "Y": 0.49053022265434265}, {"X": 0.5129770040512085, "Y": 0.4905303716659546}, {"X": 0.5129770040512085, "Y": 0.4978516399860382}, {"X": 0.501384973526001, "Y": 0.49785149097442627}]}, "Id": "34e20427-ccf3-4c15-b13d-d1b258253e7b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026752473786473274, "Height": 0.008541135117411613, "Left": 0.5169074535369873, "Top": 0.48931190371513367}, "Polygon": [{"X": 0.5169074535369873, "Y": 0.48931190371513367}, {"X": 0.5436599254608154, "Y": 0.48931223154067993}, {"X": 0.5436599254608154, "Y": 0.49785304069519043}, {"X": 0.5169074535369873, "Y": 0.49785271286964417}]}, "Id": "a792cf8b-ef89-42d4-92cd-0230a3f6210e"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "110", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02070763148367405, "Height": 0.008633173070847988, "Left": 0.5487020611763, "Top": 0.48922958970069885}, "Polygon": [{"X": 0.5487020611763, "Y": 0.48922958970069885}, {"X": 0.5694096684455872, "Y": 0.48922985792160034}, {"X": 0.5694096684455872, "Y": 0.4978627562522888}, {"X": 0.5487020611763, "Y": 0.4978625178337097}]}, "Id": "f12cde54-0c98-4cc0-bd65-cffcc23c0020"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "paragraph", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05892593041062355, "Height": 0.01080799289047718, "Left": 0.5729468464851379, "Top": 0.48932722210884094}, "Polygon": [{"X": 0.5729469060897827, "Y": 0.48932722210884094}, {"X": 0.631872832775116, "Y": 0.48932796716690063}, {"X": 0.6318727731704712, "Y": 0.5001352429389954}, {"X": 0.5729468464851379, "Y": 0.5001344680786133}]}, "Id": "7c648561-5275-46c9-a7b8-c46dfa72c1dc"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013622219674289227, "Height": 0.008635204285383224, "Left": 0.6366398930549622, "Top": 0.48921680450439453}, "Polygon": [{"X": 0.6366398930549622, "Y": 0.48921680450439453}, {"X": 0.6502621173858643, "Y": 0.48921698331832886}, {"X": 0.6502620577812195, "Y": 0.49785202741622925}, {"X": 0.6366398930549622, "Y": 0.4978518486022949}]}, "Id": "8fa9a136-6714-44bd-84a1-0c8c03589427"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017403628677129745, "Height": 0.008633257821202278, "Left": 0.6537400484085083, "Top": 0.48916688561439514}, "Polygon": [{"X": 0.6537401080131531, "Y": 0.48916688561439514}, {"X": 0.6711437106132507, "Y": 0.48916709423065186}, {"X": 0.6711437106132507, "Y": 0.49780014157295227}, {"X": 0.6537400484085083, "Y": 0.49779993295669556}]}, "Id": "aa88b666-2938-4f2a-80f6-97e74084bda1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "calculating", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06484396755695343, "Height": 0.010887501761317253, "Left": 0.08917249739170074, "Top": 0.502886950969696}, "Polygon": [{"X": 0.08917249739170074, "Y": 0.502886950969696}, {"X": 0.15401645004749298, "Y": 0.5028877854347229}, {"X": 0.15401646494865417, "Y": 0.5137744545936584}, {"X": 0.08917251974344254, "Y": 0.5137736797332764}]}, "Id": "c1e22abb-9e06-436e-90bb-04dbc7e83665"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "effective", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05154340714216232, "Height": 0.00873889122158289, "Left": 0.1574237197637558, "Top": 0.5028417706489563}, "Polygon": [{"X": 0.1574237197637558, "Y": 0.5028417706489563}, {"X": 0.20896711945533752, "Y": 0.5028424263000488}, {"X": 0.20896713435649872, "Y": 0.5115807056427002}, {"X": 0.157423734664917, "Y": 0.5115800499916077}]}, "Id": "9035eb14-4dbd-48fa-8217-5a8c1c491840"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "occupancy.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06490760296583176, "Height": 0.00850472878664732, "Left": 0.21268588304519653, "Top": 0.5051955580711365}, "Polygon": [{"X": 0.21268588304519653, "Y": 0.5051955580711365}, {"X": 0.2775934934616089, "Y": 0.5051963925361633}, {"X": 0.2775934934616089, "Y": 0.5137003064155579}, {"X": 0.21268589794635773, "Y": 0.513699471950531}]}, "Id": "6bd69de2-d6ec-4506-90ef-487078e16bf7"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "8d8b7880-41c1-41f9-880c-1f1fdba7a0c9", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "8d8b7880-41c1-41f9-880c-1f1fdba7a0c9", "content-type": "application/x-amz-json-1.1", "content-length": "60342", "date": "Wed, 24 Sep 2025 07:19:40 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "a8bffd46-3d83-41ef-b1c7-975bf7f1df09", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 07:19:43 GMT", "content-type": "application/json", "content-length": "1183", "connection": "keep-alive", "x-amzn-requestid": "a8bffd46-3d83-41ef-b1c7-975bf7f1df09"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page. The content: \"ABF DIMENSION/CUBE Freight CERTIFICATE ... Pronumber: 039721315 ... Total Weight: 1524 lbs ... Total Rated Dimensions ... Total Mileage ... etc.\" This looks like a freight certificate, which is a weight and inspection certificate? The definitions: weight_and_inspection_cert: \"Certificate/Report issued by authority confirming weight with length, width and height.\" This page includes weight, dimensions, cube, density. It is a certificate of measurement. The keywords: \"Certificate\", \"Total Weight\", \"Total Rated Dimensions\", \"Total Rated Cube\", \"Total Rated Density\". This fits weight_and_inspection_cert. There's no mention of NMFC. So doc_type: weight_and_inspection_cert. Output must be array documents: page_no 1, doc_type weight_and_inspection_cert. Use the function."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"weight_and_inspection_cert\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2570, "outputTokens": 202, "totalTokens": 2772}, "metrics": {"latencyMs": 981}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/9ca69825_W_HFPAXYL947DH59AB12FL.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination, keywords indication and key fields/structure are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight with length, width and height.\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. If weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: This will include keywords like fright (As described vs As found) or (Original vs inspection) or Corrected class or Correction information\n        Other keywords indication:  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n®\nABF\nDIMENSION/CUBE\nFreight\nCERTIFICATE\nAn ArcBest Company\nPronumber:\n039721315\nTrailer No: CITY 400177\nShipper:\nRubies Il\nTotals\nTotal Weight:\n1524 lbs\nTotal Rated Dimensions:\n96 L X 80 W X 90 H\nTotal Mileage:\n585 Miles\nTotal Rated Cube:\n400 Cubic Feet\nTotal Rated Density:\n3.81 PCF\nTrailer Measurements Done By Dock\nName\nStation\nDate\nJeff <PERSON>\n039 - Milwaukee, WI\n09/15/2025 2:50 PM\nDimensions outlined above are actual dimensions and may be subject to Item 110 paragraph 14 for\ncalculating effective occupancy.\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}