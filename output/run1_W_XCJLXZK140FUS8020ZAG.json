{"classification_result": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 1.9616864577187698e-08, "Y": 1.0}]}, "Id": "dd6a475f-1ec1-45ac-92dc-0300e7ede5f7", "Relationships": [{"Type": "CHILD", "Ids": ["96df9e09-96f8-4702-9d28-a928e21feeee", "25cced1e-2cbe-4393-a690-2cbe5f70cb9a", "f0893e75-19da-4bf2-8b22-bd7fbb9fadcc", "efadeeeb-5952-431a-bfb7-68e44a97f781", "2bc464f0-26dd-4b85-88bd-16be9d2ed8de", "7f4e6c0e-2d82-4716-ba4a-faffd2eda520", "6ac2fb30-648c-4867-a56b-2173e9d9af21", "145aaa86-5ad9-4a9d-bd25-65a5ea9dda84", "9cb5a16e-5fb9-49c0-b827-1b1da3606e63", "c211eb69-603d-4a78-b50e-94b61ffc70af", "16ae2a85-f18d-4b1a-8aed-27e05a8c9e79", "e05096d6-2458-4baa-934b-cf1e6820d9a4", "de3c03d2-1d51-4165-a863-b5fa4b991bd7", "75e7599c-b710-45fc-a3fb-4a926a34fa1b", "3c1cdd64-78ad-4749-b79d-ca08875433fb", "91383fb5-b87c-4c33-a2b2-42a5b7c1aac9", "6b7b6aa2-2de8-4ea2-8f23-e6371e1a779a", "957121b5-4913-498a-b51d-9b5766b132aa", "9f810a06-fcd4-4aa7-9764-8bd79148e0dd", "d3e4cea5-b03c-4c68-89f2-d6f6dd3751b9", "fad2c75e-e2a9-4bc4-ad4c-f430dcacbfa8", "ee4da6b0-0f8e-4a83-a242-bd741e451604", "ee1fce00-ff5d-4f86-9937-f0d61940aa30", "3d1e59e1-58f9-4900-a28f-08442dd886d3", "240e5690-41a9-45a6-9e04-2f1f40c4bc7b", "092447d4-6564-4ab9-93a6-c8e941cc1e04", "d60046da-09d9-48bb-b3d8-2c9ff124bb8c", "76614fb6-c628-400f-a479-453c71affbe3", "53f18375-5df7-4a03-9b4c-10ebf2df211d", "5e8d7b48-346d-40c9-b5da-8c10b2317354", "edea8931-3bd0-46bb-b147-0abca06282cc"]}]}, {"BlockType": "LINE", "Confidence": 82.90676879882812, "Text": "®", "Geometry": {"BoundingBox": {"Width": 0.006544790230691433, "Height": 0.005231551360338926, "Left": 0.34556087851524353, "Top": 0.08397401124238968}, "Polygon": [{"X": 0.34556087851524353, "Y": 0.08397402614355087}, {"X": 0.35210567712783813, "Y": 0.08397401124238968}, {"X": 0.35210567712783813, "Y": 0.0892055556178093}, {"X": 0.34556087851524353, "Y": 0.08920556306838989}]}, "Id": "96df9e09-96f8-4702-9d28-a928e21feeee", "Relationships": [{"Type": "CHILD", "Ids": ["4f704662-e0d1-4c42-b869-a2f39e80937e"]}]}, {"BlockType": "LINE", "Confidence": 91.42359161376953, "Text": "ABF", "Geometry": {"BoundingBox": {"Width": 0.16419638693332672, "Height": 0.0437476709485054, "Left": 0.1863717883825302, "Top": 0.0837559700012207}, "Polygon": [{"X": 0.1863717883825302, "Y": 0.08375624567270279}, {"X": 0.35056814551353455, "Y": 0.0837559700012207}, {"X": 0.35056817531585693, "Y": 0.1275033950805664}, {"X": 0.18637189269065857, "Y": 0.1275036484003067}]}, "Id": "25cced1e-2cbe-4393-a690-2cbe5f70cb9a", "Relationships": [{"Type": "CHILD", "Ids": ["30ffa3ae-7e3d-4788-8765-a7670196ecf4"]}]}, {"BlockType": "LINE", "Confidence": 98.85921478271484, "Text": "DIMENSION/CUBE", "Geometry": {"BoundingBox": {"Width": 0.33914610743522644, "Height": 0.02311941795051098, "Left": 0.49188968539237976, "Top": 0.10118571668863297}, "Polygon": [{"X": 0.49188971519470215, "Y": 0.10118626803159714}, {"X": 0.8310357928276062, "Y": 0.10118571668863297}, {"X": 0.8310356736183167, "Y": 0.12430461496114731}, {"X": 0.49188968539237976, "Y": 0.12430513650178909}]}, "Id": "f0893e75-19da-4bf2-8b22-bd7fbb9fadcc", "Relationships": [{"Type": "CHILD", "Ids": ["a2d1921b-e63d-4066-8958-ccae80ef1a46"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Freight", "Geometry": {"BoundingBox": {"Width": 0.06602073460817337, "Height": 0.014087923802435398, "Left": 0.2620227038860321, "Top": 0.13036800920963287}, "Polygon": [{"X": 0.2620227038860321, "Y": 0.13036811351776123}, {"X": 0.3280434310436249, "Y": 0.13036800920963287}, {"X": 0.3280434310436249, "Y": 0.14445583522319794}, {"X": 0.2620227336883545, "Y": 0.1444559246301651}]}, "Id": "efadeeeb-5952-431a-bfb7-68e44a97f781", "Relationships": [{"Type": "CHILD", "Ids": ["cb8f4c9a-1660-4c27-8ab7-f6d0872c9420"]}]}, {"BlockType": "LINE", "Confidence": 99.56153869628906, "Text": "CERTIFICATE", "Geometry": {"BoundingBox": {"Width": 0.29736945033073425, "Height": 0.01909993216395378, "Left": 0.5122085213661194, "Top": 0.12711404263973236}, "Polygon": [{"X": 0.5122085809707642, "Y": 0.12711448967456818}, {"X": 0.809578001499176, "Y": 0.12711404263973236}, {"X": 0.8095778822898865, "Y": 0.14621354639530182}, {"X": 0.5122085213661194, "Y": 0.14621397852897644}]}, "Id": "2bc464f0-26dd-4b85-88bd-16be9d2ed8de", "Relationships": [{"Type": "CHILD", "Ids": ["fc4e6f4d-f49e-4c14-a5b1-84fc5aa2622a"]}]}, {"BlockType": "LINE", "Confidence": 98.8974838256836, "Text": "An ArcBest Company", "Geometry": {"BoundingBox": {"Width": 0.14804215729236603, "Height": 0.011951346881687641, "Left": 0.18612322211265564, "Top": 0.1528737097978592}, "Polygon": [{"X": 0.18612322211265564, "Y": 0.1528739184141159}, {"X": 0.3341653645038605, "Y": 0.1528737097978592}, {"X": 0.33416539430618286, "Y": 0.16482485830783844}, {"X": 0.18612325191497803, "Y": 0.16482506692409515}]}, "Id": "7f4e6c0e-2d82-4716-ba4a-faffd2eda520", "Relationships": [{"Type": "CHILD", "Ids": ["da3dc96b-aa54-477f-9669-98fccb78cef0", "4c4e004b-598f-464c-84a8-651961e2b049", "5f334658-2c60-4845-9dca-d67eb2cd4afe"]}]}, {"BlockType": "LINE", "Confidence": 99.87065887451172, "Text": "Pronumber:", "Geometry": {"BoundingBox": {"Width": 0.09246920794248581, "Height": 0.011047291569411755, "Left": 0.11052803695201874, "Top": 0.18101191520690918}, "Polygon": [{"X": 0.11052803695201874, "Y": 0.18101203441619873}, {"X": 0.20299722254276276, "Y": 0.18101191520690918}, {"X": 0.20299725234508514, "Y": 0.19205908477306366}, {"X": 0.11052807420492172, "Y": 0.1920592039823532}]}, "Id": "6ac2fb30-648c-4867-a56b-2173e9d9af21", "Relationships": [{"Type": "CHILD", "Ids": ["ee372b7d-82e7-4a1a-b5a0-b64aeed4c385"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "352309638", "Geometry": {"BoundingBox": {"Width": 0.08982306718826294, "Height": 0.011103572323918343, "Left": 0.3737112283706665, "Top": 0.1811026781797409}, "Polygon": [{"X": 0.3737112283706665, "Y": 0.18110279738903046}, {"X": 0.46353429555892944, "Y": 0.1811026781797409}, {"X": 0.46353429555892944, "Y": 0.19220614433288574}, {"X": 0.3737112283706665, "Y": 0.1922062486410141}]}, "Id": "145aaa86-5ad9-4a9d-bd25-65a5ea9dda84", "Relationships": [{"Type": "CHILD", "Ids": ["0c26c84a-9ff1-4fb2-8387-b503bb447803"]}]}, {"BlockType": "LINE", "Confidence": 99.9658203125, "Text": "Trailer No: CITY 410498", "Geometry": {"BoundingBox": {"Width": 0.19430261850357056, "Height": 0.011172185651957989, "Left": 0.63603675365448, "Top": 0.180928036570549}, "Polygon": [{"X": 0.6360368132591248, "Y": 0.1809283047914505}, {"X": 0.8303393721580505, "Y": 0.180928036570549}, {"X": 0.8303393125534058, "Y": 0.19209997355937958}, {"X": 0.63603675365448, "Y": 0.19210022687911987}]}, "Id": "9cb5a16e-5fb9-49c0-b827-1b1da3606e63", "Relationships": [{"Type": "CHILD", "Ids": ["16e647e4-9c28-49b8-b7cb-1e97b5f78016", "5ba9aa9c-1c6f-49e0-a448-fb28fd68ce49", "ca0d36eb-b633-4d71-83d8-a2a778152f59", "0ef76978-7234-4a24-be76-34893c129fc6"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Shipper:", "Geometry": {"BoundingBox": {"Width": 0.06541091948747635, "Height": 0.013754081912338734, "Left": 0.11052986234426498, "Top": 0.19902680814266205}, "Polygon": [{"X": 0.11052986234426498, "Y": 0.1990268975496292}, {"X": 0.17594075202941895, "Y": 0.19902680814266205}, {"X": 0.17594078183174133, "Y": 0.21278081834316254}, {"X": 0.11052990704774857, "Y": 0.2127808928489685}]}, "Id": "c211eb69-603d-4a78-b50e-94b61ffc70af", "Relationships": [{"Type": "CHILD", "Ids": ["b40b8d8c-3c3c-4d56-958f-8041f017af66"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Headstone Deals", "Geometry": {"BoundingBox": {"Width": 0.13699108362197876, "Height": 0.011009757407009602, "Left": 0.3744701147079468, "Top": 0.19915220141410828}, "Polygon": [{"X": 0.3744701147079468, "Y": 0.1991523802280426}, {"X": 0.5114611983299255, "Y": 0.19915220141410828}, {"X": 0.5114611983299255, "Y": 0.21016179025173187}, {"X": 0.3744701147079468, "Y": 0.2101619690656662}]}, "Id": "16ae2a85-f18d-4b1a-8aed-27e05a8c9e79", "Relationships": [{"Type": "CHILD", "Ids": ["9cdebee7-0738-4aa8-a1b1-05350a455b9a", "0d5f8b22-c894-4a68-b2c3-d5efc96c9730"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Totals", "Geometry": {"BoundingBox": {"Width": 0.05161131173372269, "Height": 0.011102463118731976, "Left": 0.10968312621116638, "Top": 0.24648213386535645}, "Polygon": [{"X": 0.10968312621116638, "Y": 0.24648219347000122}, {"X": 0.16129440069198608, "Y": 0.24648213386535645}, {"X": 0.16129443049430847, "Y": 0.2575845420360565}, {"X": 0.10968315601348877, "Y": 0.2575846016407013}]}, "Id": "e05096d6-2458-4baa-934b-cf1e6820d9a4", "Relationships": [{"Type": "CHILD", "Ids": ["ee664f63-9e33-4285-89b8-f07aa06ae419"]}]}, {"BlockType": "LINE", "Confidence": 99.94548797607422, "Text": "Total Weight:", "Geometry": {"BoundingBox": {"Width": 0.10294857621192932, "Height": 0.013788904994726181, "Left": 0.1098165512084961, "Top": 0.2648628354072571}, "Polygon": [{"X": 0.1098165512084961, "Y": 0.26486295461654663}, {"X": 0.21276509761810303, "Y": 0.2648628354072571}, {"X": 0.21276512742042542, "Y": 0.2786516547203064}, {"X": 0.10981659591197968, "Y": 0.27865174412727356}]}, "Id": "de3c03d2-1d51-4165-a863-b5fa4b991bd7", "Relationships": [{"Type": "CHILD", "Ids": ["f16b7bfc-3c02-4fc1-aa00-b7b9671322c3", "f0c64bc7-940b-4b3e-9675-84a211419888"]}]}, {"BlockType": "LINE", "Confidence": 97.44609069824219, "Text": "280 lbs", "Geometry": {"BoundingBox": {"Width": 0.057782694697380066, "Height": 0.011156139895319939, "Left": 0.3737349510192871, "Top": 0.2649824619293213}, "Polygon": [{"X": 0.3737349510192871, "Y": 0.26498252153396606}, {"X": 0.431517630815506, "Y": 0.2649824619293213}, {"X": 0.431517630815506, "Y": 0.2761385440826416}, {"X": 0.3737349510192871, "Y": 0.2761386036872864}]}, "Id": "75e7599c-b710-45fc-a3fb-4a926a34fa1b", "Relationships": [{"Type": "CHILD", "Ids": ["ebc4e61a-bfe0-4d4d-989e-2dbfefb5f37f", "67bc086d-452e-4cc4-8c3a-cf8ad4c4ba31"]}]}, {"BlockType": "LINE", "Confidence": 99.88719940185547, "Text": "Total Rated Dimensions:", "Geometry": {"BoundingBox": {"Width": 0.19399678707122803, "Height": 0.010948152281343937, "Left": 0.10999516397714615, "Top": 0.2834796905517578}, "Polygon": [{"X": 0.10999516397714615, "Y": 0.28347986936569214}, {"X": 0.3039919435977936, "Y": 0.2834796905517578}, {"X": 0.3039919435977936, "Y": 0.29442766308784485}, {"X": 0.10999520123004913, "Y": 0.2944278419017792}]}, "Id": "3c1cdd64-78ad-4749-b79d-ca08875433fb", "Relationships": [{"Type": "CHILD", "Ids": ["c7d8ca73-c197-4b4c-a176-6ca7764adbe7", "698f6d2e-6937-4763-a93f-5f4cf50569ec", "387e7f2c-c076-4abe-b54a-bc901729a6c8"]}]}, {"BlockType": "LINE", "Confidence": 98.92366790771484, "Text": "48 L X 40 W X 16 H", "Geometry": {"BoundingBox": {"Width": 0.15823505818843842, "Height": 0.011254629120230675, "Left": 0.3737664222717285, "Top": 0.2832982838153839}, "Polygon": [{"X": 0.3737664222717285, "Y": 0.28329843282699585}, {"X": 0.5320014953613281, "Y": 0.2832982838153839}, {"X": 0.5320014357566833, "Y": 0.2945527732372284}, {"X": 0.3737664222717285, "Y": 0.29455292224884033}]}, "Id": "91383fb5-b87c-4c33-a2b2-42a5b7c1aac9", "Relationships": [{"Type": "CHILD", "Ids": ["26c03897-6344-4398-ada1-cf89d04829c7", "c179fb14-6ab0-404a-8ca7-b163bb34fe1f", "03704f62-1b37-4d50-adce-5730dc5464b2", "881cf4ab-3cf8-4d53-a53d-69860a7cd6aa", "709176ad-b2ce-4736-9f35-3a54fdd1f74b", "18218852-b999-47f0-93b4-4d46a73f4135", "732750de-7fbd-423d-9cc0-dd598ab7a030", "4c6d5c13-91c5-4d8e-8dd4-46abc802c22e"]}]}, {"BlockType": "LINE", "Confidence": 99.95525360107422, "Text": "Total Mileage:", "Geometry": {"BoundingBox": {"Width": 0.10993698239326477, "Height": 0.013781802728772163, "Left": 0.10994478315114975, "Top": 0.30163636803627014}, "Polygon": [{"X": 0.10994478315114975, "Y": 0.3016364574432373}, {"X": 0.21988174319267273, "Y": 0.30163636803627014}, {"X": 0.21988175809383392, "Y": 0.3154180943965912}, {"X": 0.10994482785463333, "Y": 0.31541818380355835}]}, "Id": "6b7b6aa2-2de8-4ea2-8f23-e6371e1a779a", "Relationships": [{"Type": "CHILD", "Ids": ["cde69f79-079b-4371-b960-bbd8ec15a907", "cbb32bfc-f56e-4b06-9ea2-862d58b550f4"]}]}, {"BlockType": "LINE", "Confidence": 99.84375, "Text": "1,900 Miles", "Geometry": {"BoundingBox": {"Width": 0.09043123573064804, "Height": 0.012709901668131351, "Left": 0.3750159740447998, "Top": 0.30161741375923157}, "Polygon": [{"X": 0.3750159740447998, "Y": 0.30161750316619873}, {"X": 0.46544721722602844, "Y": 0.30161741375923157}, {"X": 0.46544718742370605, "Y": 0.3143272399902344}, {"X": 0.3750159740447998, "Y": 0.31432729959487915}]}, "Id": "957121b5-4913-498a-b51d-9b5766b132aa", "Relationships": [{"Type": "CHILD", "Ids": ["66856221-718b-4ba3-80de-88d885885732", "57a04f7c-6cfa-4d5a-9d4c-ef76746b7098"]}]}, {"BlockType": "LINE", "Confidence": 99.99674224853516, "Text": "Total Rated Cube:", "Geometry": {"BoundingBox": {"Width": 0.14325794577598572, "Height": 0.011030927300453186, "Left": 0.10994534194469452, "Top": 0.32013410329818726}, "Polygon": [{"X": 0.10994534194469452, "Y": 0.3201342225074768}, {"X": 0.25320327281951904, "Y": 0.32013410329818726}, {"X": 0.25320327281951904, "Y": 0.3311648964881897}, {"X": 0.1099453791975975, "Y": 0.33116501569747925}]}, "Id": "9f810a06-fcd4-4aa7-9764-8bd79148e0dd", "Relationships": [{"Type": "CHILD", "Ids": ["a44fb839-74fe-49cf-bd4b-92b3dc2ed9fb", "a09b9864-c242-4127-9b18-10596b485890", "20fc59c2-ef4b-48dc-aede-c284b559b560"]}]}, {"BlockType": "LINE", "Confidence": 99.89583587646484, "Text": "17.78 <PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.13636669516563416, "Height": 0.011121213436126709, "Left": 0.3750312924385071, "Top": 0.32007160782814026}, "Polygon": [{"X": 0.3750312924385071, "Y": 0.3200717270374298}, {"X": 0.5113980174064636, "Y": 0.32007160782814026}, {"X": 0.5113979578018188, "Y": 0.3311927020549774}, {"X": 0.3750312924385071, "Y": 0.33119282126426697}]}, "Id": "d3e4cea5-b03c-4c68-89f2-d6f6dd3751b9", "Relationships": [{"Type": "CHILD", "Ids": ["400f7e5a-8c13-44ec-bb8b-0e41311e4496", "741733ba-b2f9-4eb2-8f40-e5e3773792da", "aefe98e6-9392-4bbe-b4aa-35def03cd6f8"]}]}, {"BlockType": "LINE", "Confidence": 99.99674224853516, "Text": "Total Rated Density:", "Geometry": {"BoundingBox": {"Width": 0.16021817922592163, "Height": 0.013626997359097004, "Left": 0.11006525903940201, "Top": 0.3382006287574768}, "Polygon": [{"X": 0.11006525903940201, "Y": 0.33820074796676636}, {"X": 0.27028343081474304, "Y": 0.3382006287574768}, {"X": 0.27028343081474304, "Y": 0.3518275022506714}, {"X": 0.11006529629230499, "Y": 0.35182762145996094}]}, "Id": "fad2c75e-e2a9-4bc4-ad4c-f430dcacbfa8", "Relationships": [{"Type": "CHILD", "Ids": ["b0a87e78-0da5-4b98-914e-6065d512f0b0", "f82fa5f0-bc48-40ac-b001-e9acf6f61062", "a92b4123-be85-4353-8224-57993911a272"]}]}, {"BlockType": "LINE", "Confidence": 99.55078125, "Text": "15.748 PCF", "Geometry": {"BoundingBox": {"Width": 0.09403590857982635, "Height": 0.011022278107702732, "Left": 0.37491366267204285, "Top": 0.33837810158729553}, "Polygon": [{"X": 0.37491366267204285, "Y": 0.3383781611919403}, {"X": 0.468949556350708, "Y": 0.33837810158729553}, {"X": 0.468949556350708, "Y": 0.3494003117084503}, {"X": 0.37491366267204285, "Y": 0.3494003713130951}]}, "Id": "ee4da6b0-0f8e-4a83-a242-bd741e451604", "Relationships": [{"Type": "CHILD", "Ids": ["194d65ad-d76b-4070-8eab-cf6936ccf1ba", "bdc891d9-517f-4034-b452-595f9408cc39"]}]}, {"BlockType": "LINE", "Confidence": 99.92630004882812, "Text": "Trailer Measurements Done By Dock", "Geometry": {"BoundingBox": {"Width": 0.31239157915115356, "Height": 0.013727175071835518, "Left": 0.10968217998743057, "Top": 0.3858337998390198}, "Polygon": [{"X": 0.10968217998743057, "Y": 0.3858340084552765}, {"X": 0.42207375168800354, "Y": 0.3858337998390198}, {"X": 0.42207375168800354, "Y": 0.399560809135437}, {"X": 0.10968222469091415, "Y": 0.39956098794937134}]}, "Id": "ee1fce00-ff5d-4f86-9937-f0d61940aa30", "Relationships": [{"Type": "CHILD", "Ids": ["ca5aad0e-d4ac-45d3-82fd-850de900ab9c", "87dd67f9-2f21-4af7-9320-ad945848167c", "22c43499-7520-434d-8849-2c2acaba5710", "dc5ae060-51af-4290-85f2-2c076526e6c2", "10ea4207-1915-4695-977e-0dd9fe328a26"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Name", "Geometry": {"BoundingBox": {"Width": 0.04676304757595062, "Height": 0.010930419899523258, "Left": 0.21485596895217896, "Top": 0.40411847829818726}, "Polygon": [{"X": 0.21485596895217896, "Y": 0.40411850810050964}, {"X": 0.2616190016269684, "Y": 0.40411847829818726}, {"X": 0.2616190016269684, "Y": 0.41504886746406555}, {"X": 0.21485598385334015, "Y": 0.41504889726638794}]}, "Id": "3d1e59e1-58f9-4900-a28f-08442dd886d3", "Relationships": [{"Type": "CHILD", "Ids": ["6d199827-dccf-435d-83c4-21c7ec5b7e91"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Station", "Geometry": {"BoundingBox": {"Width": 0.05527513474225998, "Height": 0.010969388298690319, "Left": 0.47340425848960876, "Top": 0.4040168523788452}, "Polygon": [{"X": 0.47340428829193115, "Y": 0.4040168821811676}, {"X": 0.528679370880127, "Y": 0.4040168523788452}, {"X": 0.528679370880127, "Y": 0.4149862229824066}, {"X": 0.47340425848960876, "Y": 0.414986252784729}]}, "Id": "240e5690-41a9-45a6-9e04-2f1f40c4bc7b", "Relationships": [{"Type": "CHILD", "Ids": ["2f00cc70-439c-42d2-9cd7-43e8b5f41ff9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.036862775683403015, "Height": 0.010849637910723686, "Left": 0.7453412413597107, "Top": 0.4041246771812439}, "Polygon": [{"X": 0.7453413009643555, "Y": 0.4041246771812439}, {"X": 0.7822040319442749, "Y": 0.4041246771812439}, {"X": 0.7822039723396301, "Y": 0.41497430205345154}, {"X": 0.7453412413597107, "Y": 0.41497430205345154}]}, "Id": "092447d4-6564-4ab9-93a6-c8e941cc1e04", "Relationships": [{"Type": "CHILD", "Ids": ["4268b4ea-4acb-46c9-88f6-348bc935d3dd"]}]}, {"BlockType": "LINE", "Confidence": 99.90035247802734, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.12828165292739868, "Height": 0.013511269353330135, "Left": 0.17404647171497345, "Top": 0.42260441184043884}, "Polygon": [{"X": 0.17404647171497345, "Y": 0.4226044714450836}, {"X": 0.30232810974121094, "Y": 0.42260441184043884}, {"X": 0.30232810974121094, "Y": 0.4361156225204468}, {"X": 0.17404650151729584, "Y": 0.43611568212509155}]}, "Id": "d60046da-09d9-48bb-b3d8-2c9ff124bb8c", "Relationships": [{"Type": "CHILD", "Ids": ["082fb6c4-55a9-4dd7-b71d-8956c7f97396", "31d30088-1c39-40e7-a52c-c82bbcf9ab8d"]}]}, {"BlockType": "LINE", "Confidence": 97.17948150634766, "Text": "352 - Avenel, NJ", "Geometry": {"BoundingBox": {"Width": 0.13210774958133698, "Height": 0.013055645860731602, "Left": 0.4350687265396118, "Top": 0.422454297542572}, "Polygon": [{"X": 0.4350687265396118, "Y": 0.4224543571472168}, {"X": 0.5671764612197876, "Y": 0.422454297542572}, {"X": 0.5671764612197876, "Y": 0.43550989031791687}, {"X": 0.4350687265396118, "Y": 0.43550994992256165}]}, "Id": "76614fb6-c628-400f-a479-453c71affbe3", "Relationships": [{"Type": "CHILD", "Ids": ["87c23390-67ea-4ead-871b-0cdcfa99531d", "6844f5a6-4c9d-4455-9e3a-9df0d1e3ec49", "c3814d12-0af1-4eb2-9e86-51a5042c4785", "706bc274-7a35-45eb-8c24-00b831f44249"]}]}, {"BlockType": "LINE", "Confidence": 99.71028900146484, "Text": "07/16/2025 2:10 PM", "Geometry": {"BoundingBox": {"Width": 0.1620771437883377, "Height": 0.011287374421954155, "Left": 0.6825687885284424, "Top": 0.4224686324596405}, "Polygon": [{"X": 0.6825687885284424, "Y": 0.4224686920642853}, {"X": 0.8446459174156189, "Y": 0.4224686324596405}, {"X": 0.8446458578109741, "Y": 0.43375593423843384}, {"X": 0.6825687885284424, "Y": 0.4337559938430786}]}, "Id": "53f18375-5df7-4a03-9b4c-10ebf2df211d", "Relationships": [{"Type": "CHILD", "Ids": ["47948d9b-d3f9-4e00-af07-1f0df150165e", "406da5e7-db5f-44be-be07-658c42b90ba0", "3763978f-9afa-4bd3-af3a-e63b5ad35b61"]}]}, {"BlockType": "LINE", "Confidence": 99.95753479003906, "Text": "Dimensions outlined above are actual dimensions and may be subject to Item 110 paragraph 14 for", "Geometry": {"BoundingBox": {"Width": 0.5932554006576538, "Height": 0.011593159288167953, "Left": 0.08117885142564774, "Top": 0.46901386976242065}, "Polygon": [{"X": 0.08117885142564774, "Y": 0.4690140187740326}, {"X": 0.6744343042373657, "Y": 0.46901386976242065}, {"X": 0.674434244632721, "Y": 0.48060688376426697}, {"X": 0.08117889612913132, "Y": 0.4806070029735565}]}, "Id": "5e8d7b48-346d-40c9-b5da-8c10b2317354", "Relationships": [{"Type": "CHILD", "Ids": ["695b0333-3d3a-4b29-a43a-e2b2eca643ad", "92ad9247-0e8d-4a99-9a3d-6d9b635740c6", "fc4bc1a2-4e51-4005-9f74-0d801669cd57", "b1137d3f-11e6-4d9e-a3c7-5f22e6f82fd2", "9576a8d4-fd3b-43ca-b736-09f3343c237b", "3e331638-a575-4a45-b9df-f1e0bbadde11", "7ec01ec1-3afd-4ff2-a2a4-e852eb3dc26d", "b0119c4d-6dc7-459a-8364-a89eca326bce", "12885301-35bb-4223-a832-7bc2629db1e0", "2689b1d3-e1fb-4388-a7cd-d2d8de35ddbd", "83630195-f12a-401e-a788-ed40086819b2", "3987433a-0076-4a57-a1ed-511820d35365", "7f446de7-436c-499f-a1e7-7ab9dbf2b402", "45da0e5d-50a9-45ff-91f6-b3ce0daee096", "949d48b5-e367-4bda-91f3-125a0ba2c592", "f15f44c0-bd97-4d9c-9a70-66509683b574"]}]}, {"BlockType": "LINE", "Confidence": 99.98345947265625, "Text": "calculating effective occupancy.", "Geometry": {"BoundingBox": {"Width": 0.1916247457265854, "Height": 0.011674925684928894, "Left": 0.08120424300432205, "Top": 0.4832383096218109}, "Polygon": [{"X": 0.08120424300432205, "Y": 0.4832383394241333}, {"X": 0.27282896637916565, "Y": 0.4832383096218109}, {"X": 0.27282899618148804, "Y": 0.4949131906032562}, {"X": 0.08120428025722504, "Y": 0.4949132204055786}]}, "Id": "edea8931-3bd0-46bb-b147-0abca06282cc", "Relationships": [{"Type": "CHILD", "Ids": ["95ec6614-9e13-4d5e-9509-2abe908249ef", "528beaeb-a1ab-46aa-8c8d-09ca0eb773fe", "8e844f4d-36f9-41fd-bb1b-120ee498b1bf"]}]}, {"BlockType": "WORD", "Confidence": 82.90676879882812, "Text": "®", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006544790230691433, "Height": 0.005231551360338926, "Left": 0.34556087851524353, "Top": 0.08397401124238968}, "Polygon": [{"X": 0.34556087851524353, "Y": 0.08397402614355087}, {"X": 0.35210567712783813, "Y": 0.08397401124238968}, {"X": 0.35210567712783813, "Y": 0.0892055556178093}, {"X": 0.34556087851524353, "Y": 0.08920556306838989}]}, "Id": "4f704662-e0d1-4c42-b869-a2f39e80937e"}, {"BlockType": "WORD", "Confidence": 91.42359161376953, "Text": "ABF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16419638693332672, "Height": 0.0437476709485054, "Left": 0.1863717883825302, "Top": 0.0837559700012207}, "Polygon": [{"X": 0.1863717883825302, "Y": 0.08375624567270279}, {"X": 0.35056814551353455, "Y": 0.0837559700012207}, {"X": 0.35056817531585693, "Y": 0.1275033950805664}, {"X": 0.18637189269065857, "Y": 0.1275036484003067}]}, "Id": "30ffa3ae-7e3d-4788-8765-a7670196ecf4"}, {"BlockType": "WORD", "Confidence": 98.85921478271484, "Text": "DIMENSION/CUBE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.33914610743522644, "Height": 0.02311941795051098, "Left": 0.49188968539237976, "Top": 0.10118571668863297}, "Polygon": [{"X": 0.49188971519470215, "Y": 0.10118626803159714}, {"X": 0.8310357928276062, "Y": 0.10118571668863297}, {"X": 0.8310356736183167, "Y": 0.12430461496114731}, {"X": 0.49188968539237976, "Y": 0.12430513650178909}]}, "Id": "a2d1921b-e63d-4066-8958-ccae80ef1a46"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06602073460817337, "Height": 0.014087923802435398, "Left": 0.2620227038860321, "Top": 0.13036800920963287}, "Polygon": [{"X": 0.2620227038860321, "Y": 0.13036811351776123}, {"X": 0.3280434310436249, "Y": 0.13036800920963287}, {"X": 0.3280434310436249, "Y": 0.14445583522319794}, {"X": 0.2620227336883545, "Y": 0.1444559246301651}]}, "Id": "cb8f4c9a-1660-4c27-8ab7-f6d0872c9420"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "CERTIFICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.29736945033073425, "Height": 0.01909993216395378, "Left": 0.5122085213661194, "Top": 0.12711404263973236}, "Polygon": [{"X": 0.5122085809707642, "Y": 0.12711448967456818}, {"X": 0.809578001499176, "Y": 0.12711404263973236}, {"X": 0.8095778822898865, "Y": 0.14621354639530182}, {"X": 0.5122085213661194, "Y": 0.14621397852897644}]}, "Id": "fc4e6f4d-f49e-4c14-a5b1-84fc5aa2622a"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "An", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01833299919962883, "Height": 0.009173985570669174, "Left": 0.18612322211265564, "Top": 0.15317994356155396}, "Polygon": [{"X": 0.18612322211265564, "Y": 0.15317997336387634}, {"X": 0.20445619523525238, "Y": 0.15317994356155396}, {"X": 0.20445622503757477, "Y": 0.16235390305519104}, {"X": 0.18612323701381683, "Y": 0.16235393285751343}]}, "Id": "da3dc96b-aa54-477f-9669-98fccb78cef0"}, {"BlockType": "WORD", "Confidence": 96.82178497314453, "Text": "ArcBest", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058094706386327744, "Height": 0.009328647516667843, "Left": 0.2088058441877365, "Top": 0.15304400026798248}, "Polygon": [{"X": 0.2088058441877365, "Y": 0.15304407477378845}, {"X": 0.26690053939819336, "Y": 0.15304400026798248}, {"X": 0.26690053939819336, "Y": 0.16237255930900574}, {"X": 0.2088058590888977, "Y": 0.1623726338148117}]}, "Id": "4c4e004b-598f-464c-84a8-651961e2b049"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Company", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06347786635160446, "Height": 0.01195122953504324, "Left": 0.2706875205039978, "Top": 0.1528737097978592}, "Polygon": [{"X": 0.2706875205039978, "Y": 0.15287379920482635}, {"X": 0.3341653645038605, "Y": 0.1528737097978592}, {"X": 0.33416539430618286, "Y": 0.16482485830783844}, {"X": 0.2706875205039978, "Y": 0.1648249477148056}]}, "Id": "5f334658-2c60-4845-9dca-d67eb2cd4afe"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Pronumber:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09246920794248581, "Height": 0.011047291569411755, "Left": 0.11052803695201874, "Top": 0.18101191520690918}, "Polygon": [{"X": 0.11052803695201874, "Y": 0.18101203441619873}, {"X": 0.20299722254276276, "Y": 0.18101191520690918}, {"X": 0.20299725234508514, "Y": 0.19205908477306366}, {"X": 0.11052807420492172, "Y": 0.1920592039823532}]}, "Id": "ee372b7d-82e7-4a1a-b5a0-b64aeed4c385"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "352309638", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08982306718826294, "Height": 0.011103572323918343, "Left": 0.3737112283706665, "Top": 0.1811026781797409}, "Polygon": [{"X": 0.3737112283706665, "Y": 0.18110279738903046}, {"X": 0.46353429555892944, "Y": 0.1811026781797409}, {"X": 0.46353429555892944, "Y": 0.19220614433288574}, {"X": 0.3737112283706665, "Y": 0.1922062486410141}]}, "Id": "0c26c84a-9ff1-4fb2-8387-b503bb447803"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050973568111658096, "Height": 0.010954533703625202, "Left": 0.63603675365448, "Top": 0.1810583472251892}, "Polygon": [{"X": 0.6360368132591248, "Y": 0.18105840682983398}, {"X": 0.6870103478431702, "Y": 0.1810583472251892}, {"X": 0.6870102882385254, "Y": 0.19201281666755676}, {"X": 0.63603675365448, "Y": 0.19201287627220154}]}, "Id": "16e647e4-9c28-49b8-b7cb-1e97b5f78016"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0264002475887537, "Height": 0.011072097346186638, "Left": 0.6920583248138428, "Top": 0.18102805316448212}, "Polygon": [{"X": 0.6920583844184875, "Y": 0.1810280978679657}, {"X": 0.7184585928916931, "Y": 0.18102805316448212}, {"X": 0.7184585332870483, "Y": 0.19210012257099152}, {"X": 0.6920583248138428, "Y": 0.1921001523733139}]}, "Id": "5ba9aa9c-1c6f-49e0-a448-fb28fd68ce49"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "CITY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04036648944020271, "Height": 0.011076513677835464, "Left": 0.724669337272644, "Top": 0.18092812597751617}, "Polygon": [{"X": 0.7246693968772888, "Y": 0.18092818558216095}, {"X": 0.7650358080863953, "Y": 0.18092812597751617}, {"X": 0.7650357484817505, "Y": 0.19200459122657776}, {"X": 0.724669337272644, "Y": 0.19200465083122253}]}, "Id": "ca0d36eb-b633-4d71-83d8-a2a778152f59"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "410498", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06042822077870369, "Height": 0.010986443608999252, "Left": 0.7699111700057983, "Top": 0.18103894591331482}, "Polygon": [{"X": 0.7699112296104431, "Y": 0.1810390204191208}, {"X": 0.8303393721580505, "Y": 0.18103894591331482}, {"X": 0.8303393125534058, "Y": 0.1920253038406372}, {"X": 0.7699111700057983, "Y": 0.19202537834644318}]}, "Id": "0ef76978-7234-4a24-be76-34893c129fc6"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Shipper:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06541091948747635, "Height": 0.013754081912338734, "Left": 0.11052986234426498, "Top": 0.19902680814266205}, "Polygon": [{"X": 0.11052986234426498, "Y": 0.1990268975496292}, {"X": 0.17594075202941895, "Y": 0.19902680814266205}, {"X": 0.17594078183174133, "Y": 0.21278081834316254}, {"X": 0.11052990704774857, "Y": 0.2127808928489685}]}, "Id": "b40b8d8c-3c3c-4d56-958f-8041f017af66"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Headstone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08621614426374435, "Height": 0.011009693145751953, "Left": 0.3744701147079468, "Top": 0.19915227591991425}, "Polygon": [{"X": 0.3744701147079468, "Y": 0.1991523802280426}, {"X": 0.46068626642227173, "Y": 0.19915227591991425}, {"X": 0.46068626642227173, "Y": 0.21016184985637665}, {"X": 0.3744701147079468, "Y": 0.2101619690656662}]}, "Id": "9cdebee7-0738-4aa8-a1b1-05350a455b9a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Deals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04521114006638527, "Height": 0.010936052538454533, "Left": 0.46625006198883057, "Top": 0.19918932020664215}, "Polygon": [{"X": 0.46625009179115295, "Y": 0.19918937981128693}, {"X": 0.5114611983299255, "Y": 0.19918932020664215}, {"X": 0.5114611983299255, "Y": 0.21012532711029053}, {"X": 0.46625006198883057, "Y": 0.2101253867149353}]}, "Id": "0d5f8b22-c894-4a68-b2c3-d5efc96c9730"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Totals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05161131173372269, "Height": 0.011102463118731976, "Left": 0.10968312621116638, "Top": 0.24648213386535645}, "Polygon": [{"X": 0.10968312621116638, "Y": 0.24648219347000122}, {"X": 0.16129440069198608, "Y": 0.24648213386535645}, {"X": 0.16129443049430847, "Y": 0.2575845420360565}, {"X": 0.10968315601348877, "Y": 0.2575846016407013}]}, "Id": "ee664f63-9e33-4285-89b8-f07aa06ae419"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03781477361917496, "Height": 0.011112128384411335, "Left": 0.1098165512084961, "Top": 0.26486292481422424}, "Polygon": [{"X": 0.1098165512084961, "Y": 0.26486295461654663}, {"X": 0.14763128757476807, "Y": 0.26486292481422424}, {"X": 0.14763131737709045, "Y": 0.27597498893737793}, {"X": 0.10981658101081848, "Y": 0.2759750485420227}]}, "Id": "f16b7bfc-3c02-4fc1-aa00-b7b9671322c3"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05993150919675827, "Height": 0.013707414269447327, "Left": 0.15283361077308655, "Top": 0.26494428515434265}, "Polygon": [{"X": 0.15283361077308655, "Y": 0.2649443447589874}, {"X": 0.21276509761810303, "Y": 0.26494428515434265}, {"X": 0.21276512742042542, "Y": 0.2786516547203064}, {"X": 0.15283365547657013, "Y": 0.27865171432495117}]}, "Id": "f0c64bc7-940b-4b3e-9675-84a211419888"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "280", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029715221375226974, "Height": 0.011156111024320126, "Left": 0.3737349510192871, "Top": 0.2649824917316437}, "Polygon": [{"X": 0.3737349510192871, "Y": 0.26498252153396606}, {"X": 0.4034501612186432, "Y": 0.2649824917316437}, {"X": 0.4034501612186432, "Y": 0.276138573884964}, {"X": 0.3737349510192871, "Y": 0.2761386036872864}]}, "Id": "ebc4e61a-bfe0-4d4d-989e-2dbfefb5f37f"}, {"BlockType": "WORD", "Confidence": 94.94100952148438, "Text": "lbs", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02242872677743435, "Height": 0.010742166079580784, "Left": 0.4090889096260071, "Top": 0.26517224311828613}, "Polygon": [{"X": 0.4090889096260071, "Y": 0.26517224311828613}, {"X": 0.431517630815506, "Y": 0.26517224311828613}, {"X": 0.431517630815506, "Y": 0.27591437101364136}, {"X": 0.4090889096260071, "Y": 0.27591440081596375}]}, "Id": "67bc086d-452e-4cc4-8c3a-cf8ad4c4ba31"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037389758974313736, "Height": 0.010948002338409424, "Left": 0.10999516397714615, "Top": 0.28347983956336975}, "Polygon": [{"X": 0.10999516397714615, "Y": 0.28347986936569214}, {"X": 0.1473848968744278, "Y": 0.28347983956336975}, {"X": 0.14738492667675018, "Y": 0.2944277822971344}, {"X": 0.10999520123004913, "Y": 0.2944278419017792}]}, "Id": "c7d8ca73-c197-4b4c-a176-6ca7764adbe7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Rated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0467238649725914, "Height": 0.010807320475578308, "Left": 0.15381984412670135, "Top": 0.2835935056209564}, "Polygon": [{"X": 0.15381984412670135, "Y": 0.2835935354232788}, {"X": 0.20054367184638977, "Y": 0.2835935056209564}, {"X": 0.20054370164871216, "Y": 0.29440078139305115}, {"X": 0.15381987392902374, "Y": 0.29440081119537354}]}, "Id": "698f6d2e-6937-4763-a93f-5f4cf50569ec"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Dimensions:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09696175903081894, "Height": 0.010912691242992878, "Left": 0.20703019201755524, "Top": 0.2834863066673279}, "Polygon": [{"X": 0.20703019201755524, "Y": 0.28348639607429504}, {"X": 0.3039919435977936, "Y": 0.2834863066673279}, {"X": 0.3039919435977936, "Y": 0.2943989038467407}, {"X": 0.20703020691871643, "Y": 0.2943989932537079}]}, "Id": "387e7f2c-c076-4abe-b54a-bc901729a6c8"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "48", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019447343423962593, "Height": 0.010942968539893627, "Left": 0.3737664222717285, "Top": 0.2835685610771179}, "Polygon": [{"X": 0.3737664222717285, "Y": 0.2835685610771179}, {"X": 0.39321374893188477, "Y": 0.2835685610771179}, {"X": 0.39321374893188477, "Y": 0.29451149702072144}, {"X": 0.3737664222717285, "Y": 0.2945115268230438}]}, "Id": "26c03897-6344-4398-ada1-cf89d04829c7"}, {"BlockType": "WORD", "Confidence": 99.29448699951172, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009671926498413086, "Height": 0.011005237698554993, "Left": 0.39923742413520813, "Top": 0.28343790769577026}, "Polygon": [{"X": 0.39923742413520813, "Y": 0.28343793749809265}, {"X": 0.4089093506336212, "Y": 0.28343790769577026}, {"X": 0.4089093506336212, "Y": 0.29444316029548645}, {"X": 0.39923742413520813, "Y": 0.29444316029548645}]}, "Id": "c179fb14-6ab0-404a-8ca7-b163bb34fe1f"}, {"BlockType": "WORD", "Confidence": 97.75450134277344, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012429571710526943, "Height": 0.01106173824518919, "Left": 0.4135148823261261, "Top": 0.28337886929512024}, "Polygon": [{"X": 0.4135148823261261, "Y": 0.28337886929512024}, {"X": 0.4259444773197174, "Y": 0.28337886929512024}, {"X": 0.425944447517395, "Y": 0.2944405972957611}, {"X": 0.4135148823261261, "Y": 0.2944405972957611}]}, "Id": "03704f62-1b37-4d50-adce-5730dc5464b2"}, {"BlockType": "WORD", "Confidence": 99.609375, "Text": "40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02034015953540802, "Height": 0.010836265981197357, "Left": 0.43034833669662476, "Top": 0.28360316157341003}, "Polygon": [{"X": 0.43034836649894714, "Y": 0.28360316157341003}, {"X": 0.45068851113319397, "Y": 0.28360316157341003}, {"X": 0.45068851113319397, "Y": 0.2944394052028656}, {"X": 0.43034833669662476, "Y": 0.294439435005188}]}, "Id": "881cf4ab-3cf8-4d53-a53d-69860a7cd6aa"}, {"BlockType": "WORD", "Confidence": 99.55735778808594, "Text": "W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01783633418381214, "Height": 0.010819087736308575, "Left": 0.45510393381118774, "Top": 0.2834729850292206}, "Polygon": [{"X": 0.45510393381118774, "Y": 0.28347301483154297}, {"X": 0.47294026613235474, "Y": 0.2834729850292206}, {"X": 0.47294026613235474, "Y": 0.29429206252098083}, {"X": 0.45510393381118774, "Y": 0.29429206252098083}]}, "Id": "709176ad-b2ce-4736-9f35-3a54fdd1f74b"}, {"BlockType": "WORD", "Confidence": 98.1287841796875, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012423158623278141, "Height": 0.011155807413160801, "Left": 0.4775581657886505, "Top": 0.2832983136177063}, "Polygon": [{"X": 0.4775581657886505, "Y": 0.2832983434200287}, {"X": 0.4899813234806061, "Y": 0.2832983136177063}, {"X": 0.4899812936782837, "Y": 0.2944541275501251}, {"X": 0.4775581657886505, "Y": 0.2944541275501251}]}, "Id": "18218852-b999-47f0-93b4-4d46a73f4135"}, {"BlockType": "WORD", "Confidence": 99.365234375, "Text": "16", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018461085855960846, "Height": 0.01099309977144003, "Left": 0.4959329068660736, "Top": 0.2835597097873688}, "Polygon": [{"X": 0.495932936668396, "Y": 0.2835597097873688}, {"X": 0.5143939852714539, "Y": 0.2835597097873688}, {"X": 0.5143939852714539, "Y": 0.2945527732372284}, {"X": 0.4959329068660736, "Y": 0.2945528030395508}]}, "Id": "732750de-7fbd-423d-9cc0-dd598ab7a030"}, {"BlockType": "WORD", "Confidence": 97.8260498046875, "Text": "H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011625777930021286, "Height": 0.010800197720527649, "Left": 0.520375669002533, "Top": 0.28358784317970276}, "Polygon": [{"X": 0.5203757286071777, "Y": 0.28358784317970276}, {"X": 0.5320014953613281, "Y": 0.28358784317970276}, {"X": 0.5320014357566833, "Y": 0.2943880259990692}, {"X": 0.520375669002533, "Y": 0.2943880259990692}]}, "Id": "4c6d5c13-91c5-4d8e-8dd4-46abc802c22e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037486713379621506, "Height": 0.010951882228255272, "Left": 0.10994478315114975, "Top": 0.3016364276409149}, "Polygon": [{"X": 0.10994478315114975, "Y": 0.3016364574432373}, {"X": 0.14743146300315857, "Y": 0.3016364276409149}, {"X": 0.14743149280548096, "Y": 0.31258827447891235}, {"X": 0.10994482040405273, "Y": 0.31258830428123474}]}, "Id": "cde69f79-079b-4371-b960-bbd8ec15a907"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Mileage:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06645472347736359, "Height": 0.013722969219088554, "Left": 0.15342703461647034, "Top": 0.30169516801834106}, "Polygon": [{"X": 0.15342703461647034, "Y": 0.30169522762298584}, {"X": 0.21988174319267273, "Y": 0.30169516801834106}, {"X": 0.21988175809383392, "Y": 0.3154180943965912}, {"X": 0.15342707931995392, "Y": 0.3154181241989136}]}, "Id": "cbb32bfc-f56e-4b06-9ea2-862d58b550f4"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "1,900", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043364640325307846, "Height": 0.01263413019478321, "Left": 0.3750159740447998, "Top": 0.3016931712627411}, "Polygon": [{"X": 0.3750159740447998, "Y": 0.30169323086738586}, {"X": 0.41838061809539795, "Y": 0.3016931712627411}, {"X": 0.41838061809539795, "Y": 0.31432726979255676}, {"X": 0.3750159740447998, "Y": 0.31432729959487915}]}, "Id": "66856221-718b-4ba3-80de-88d885885732"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041378434747457504, "Height": 0.010960670188069344, "Left": 0.42406877875328064, "Top": 0.30161741375923157}, "Polygon": [{"X": 0.42406877875328064, "Y": 0.30161744356155396}, {"X": 0.46544721722602844, "Y": 0.30161741375923157}, {"X": 0.46544718742370605, "Y": 0.3125780522823334}, {"X": 0.42406877875328064, "Y": 0.31257808208465576}]}, "Id": "57a04f7c-6cfa-4d5a-9d4c-ef76746b7098"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03749297559261322, "Height": 0.011030840687453747, "Left": 0.10994534194469452, "Top": 0.32013416290283203}, "Polygon": [{"X": 0.10994534194469452, "Y": 0.3201342225074768}, {"X": 0.14743828773498535, "Y": 0.32013416290283203}, {"X": 0.14743831753730774, "Y": 0.33116498589515686}, {"X": 0.1099453791975975, "Y": 0.33116501569747925}]}, "Id": "a44fb839-74fe-49cf-bd4b-92b3dc2ed9fb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Rated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046675823628902435, "Height": 0.010875381529331207, "Left": 0.15378113090991974, "Top": 0.3201565742492676}, "Polygon": [{"X": 0.15378113090991974, "Y": 0.32015663385391235}, {"X": 0.20045693218708038, "Y": 0.3201565742492676}, {"X": 0.20045696198940277, "Y": 0.3310319185256958}, {"X": 0.15378116071224213, "Y": 0.3310319483280182}]}, "Id": "a09b9864-c242-4127-9b18-10596b485890"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Cube:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046871162950992584, "Height": 0.010979341343045235, "Left": 0.20633211731910706, "Top": 0.3201426863670349}, "Polygon": [{"X": 0.20633211731910706, "Y": 0.3201427161693573}, {"X": 0.25320327281951904, "Y": 0.3201426863670349}, {"X": 0.25320327281951904, "Y": 0.3311219811439514}, {"X": 0.20633214712142944, "Y": 0.3311220109462738}]}, "Id": "20fc59c2-ef4b-48dc-aede-c284b559b560"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "17.78", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043415866792201996, "Height": 0.01112113706767559, "Left": 0.3750312924385071, "Top": 0.32007166743278503}, "Polygon": [{"X": 0.3750312924385071, "Y": 0.3200717270374298}, {"X": 0.4184471666812897, "Y": 0.32007166743278503}, {"X": 0.4184471666812897, "Y": 0.3311927914619446}, {"X": 0.3750312924385071, "Y": 0.33119282126426697}]}, "Id": "400f7e5a-8c13-44ec-bb8b-0e41311e4496"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Cubic", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045745305716991425, "Height": 0.010862244293093681, "Left": 0.4241652488708496, "Top": 0.3200869858264923}, "Polygon": [{"X": 0.424165278673172, "Y": 0.3200870454311371}, {"X": 0.46991056203842163, "Y": 0.3200869858264923}, {"X": 0.46991056203842163, "Y": 0.33094921708106995}, {"X": 0.4241652488708496, "Y": 0.33094924688339233}]}, "Id": "741733ba-b2f9-4eb2-8f40-e5e3773792da"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Feet", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035785503685474396, "Height": 0.010813392698764801, "Left": 0.47561249136924744, "Top": 0.3203466832637787}, "Polygon": [{"X": 0.4756125211715698, "Y": 0.3203467130661011}, {"X": 0.5113980174064636, "Y": 0.3203466832637787}, {"X": 0.5113979578018188, "Y": 0.3311600387096405}, {"X": 0.47561249136924744, "Y": 0.3311600685119629}]}, "Id": "aefe98e6-9392-4bbe-b4aa-35def03cd6f8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037358466535806656, "Height": 0.011022203601896763, "Left": 0.11006525903940201, "Top": 0.3383297622203827}, "Polygon": [{"X": 0.11006525903940201, "Y": 0.3383297920227051}, {"X": 0.14742369949817657, "Y": 0.3383297622203827}, {"X": 0.14742372930049896, "Y": 0.3493519425392151}, {"X": 0.1100652888417244, "Y": 0.3493519723415375}]}, "Id": "b0a87e78-0da5-4b98-914e-6065d512f0b0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Rated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04731443151831627, "Height": 0.011002207174897194, "Left": 0.1534612625837326, "Top": 0.3382006585597992}, "Polygon": [{"X": 0.1534612625837326, "Y": 0.33820071816444397}, {"X": 0.20077568292617798, "Y": 0.3382006585597992}, {"X": 0.20077569782733917, "Y": 0.34920284152030945}, {"X": 0.153461292386055, "Y": 0.34920287132263184}]}, "Id": "f82fa5f0-bc48-40ac-b001-e9acf6f61062"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Density:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06358332931995392, "Height": 0.013477646745741367, "Left": 0.20670011639595032, "Top": 0.3383499085903168}, "Polygon": [{"X": 0.20670011639595032, "Y": 0.33834993839263916}, {"X": 0.27028343081474304, "Y": 0.3383499085903168}, {"X": 0.27028343081474304, "Y": 0.3518275022506714}, {"X": 0.2067001461982727, "Y": 0.35182756185531616}]}, "Id": "a92b4123-be85-4353-8224-57993911a272"}, {"BlockType": "WORD", "Confidence": 99.169921875, "Text": "15.748", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05368179827928543, "Height": 0.011004846543073654, "Left": 0.37491366267204285, "Top": 0.33839553594589233}, "Polygon": [{"X": 0.37491366267204285, "Y": 0.3383955657482147}, {"X": 0.4285954535007477, "Y": 0.33839553594589233}, {"X": 0.4285954535007477, "Y": 0.3494003415107727}, {"X": 0.37491366267204285, "Y": 0.3494003713130951}]}, "Id": "194d65ad-d76b-4070-8eab-cf6936ccf1ba"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "PCF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03431317210197449, "Height": 0.010867071337997913, "Left": 0.4346364140510559, "Top": 0.33837810158729553}, "Polygon": [{"X": 0.4346364140510559, "Y": 0.3383781313896179}, {"X": 0.468949556350708, "Y": 0.33837810158729553}, {"X": 0.468949556350708, "Y": 0.3492451608181}, {"X": 0.4346364140510559, "Y": 0.3492451608181}]}, "Id": "bdc891d9-517f-4034-b452-595f9408cc39"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05493469163775444, "Height": 0.010954484343528748, "Left": 0.10968217998743057, "Top": 0.38593506813049316}, "Polygon": [{"X": 0.10968217998743057, "Y": 0.38593509793281555}, {"X": 0.16461683809757233, "Y": 0.38593506813049316}, {"X": 0.16461686789989471, "Y": 0.3968895375728607}, {"X": 0.10968221724033356, "Y": 0.3968895673751831}]}, "Id": "ca5aad0e-d4ac-45d3-82fd-850de900ab9c"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Measurements", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12466094642877579, "Height": 0.011061456054449081, "Left": 0.16977132856845856, "Top": 0.3858943283557892}, "Polygon": [{"X": 0.16977132856845856, "Y": 0.38589441776275635}, {"X": 0.29443225264549255, "Y": 0.3858943283557892}, {"X": 0.29443228244781494, "Y": 0.3969557285308838}, {"X": 0.16977135837078094, "Y": 0.39695578813552856}]}, "Id": "87dd67f9-2f21-4af7-9320-ad945848167c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Done", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04429704695940018, "Height": 0.011081278324127197, "Left": 0.30051279067993164, "Top": 0.3859160840511322}, "Polygon": [{"X": 0.30051279067993164, "Y": 0.3859161138534546}, {"X": 0.3448098301887512, "Y": 0.3859160840511322}, {"X": 0.3448098301887512, "Y": 0.396997332572937}, {"X": 0.30051279067993164, "Y": 0.3969973623752594}]}, "Id": "22c43499-7520-434d-8849-2c2acaba5710"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "By", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02273612469434738, "Height": 0.013727016746997833, "Left": 0.35059094429016113, "Top": 0.38583382964134216}, "Polygon": [{"X": 0.35059094429016113, "Y": 0.38583385944366455}, {"X": 0.3733270466327667, "Y": 0.38583382964134216}, {"X": 0.3733270466327667, "Y": 0.3995608389377594}, {"X": 0.35059094429016113, "Y": 0.3995608687400818}]}, "Id": "dc5ae060-51af-4290-85f2-2c076526e6c2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04351583123207092, "Height": 0.010826352052390575, "Left": 0.378557950258255, "Top": 0.3859325647354126}, "Polygon": [{"X": 0.378557950258255, "Y": 0.385932594537735}, {"X": 0.42207375168800354, "Y": 0.3859325647354126}, {"X": 0.42207375168800354, "Y": 0.39675888419151306}, {"X": 0.378557950258255, "Y": 0.39675891399383545}]}, "Id": "10ea4207-1915-4695-977e-0dd9fe328a26"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Name", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04676304757595062, "Height": 0.010930419899523258, "Left": 0.21485596895217896, "Top": 0.40411847829818726}, "Polygon": [{"X": 0.21485596895217896, "Y": 0.40411850810050964}, {"X": 0.2616190016269684, "Y": 0.40411847829818726}, {"X": 0.2616190016269684, "Y": 0.41504886746406555}, {"X": 0.21485598385334015, "Y": 0.41504889726638794}]}, "Id": "6d199827-dccf-435d-83c4-21c7ec5b7e91"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Station", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05527513474225998, "Height": 0.010969388298690319, "Left": 0.47340425848960876, "Top": 0.4040168523788452}, "Polygon": [{"X": 0.47340428829193115, "Y": 0.4040168821811676}, {"X": 0.528679370880127, "Y": 0.4040168523788452}, {"X": 0.528679370880127, "Y": 0.4149862229824066}, {"X": 0.47340425848960876, "Y": 0.414986252784729}]}, "Id": "2f00cc70-439c-42d2-9cd7-43e8b5f41ff9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036862775683403015, "Height": 0.010849637910723686, "Left": 0.7453412413597107, "Top": 0.4041246771812439}, "Polygon": [{"X": 0.7453413009643555, "Y": 0.4041246771812439}, {"X": 0.7822040319442749, "Y": 0.4041246771812439}, {"X": 0.7822039723396301, "Y": 0.41497430205345154}, {"X": 0.7453412413597107, "Y": 0.41497430205345154}]}, "Id": "4268b4ea-4acb-46c9-88f6-348bc935d3dd"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05721929296851158, "Height": 0.010888570919632912, "Left": 0.17404647171497345, "Top": 0.42260444164276123}, "Polygon": [{"X": 0.17404647171497345, "Y": 0.4226044714450836}, {"X": 0.23126573860645294, "Y": 0.42260444164276123}, {"X": 0.23126575350761414, "Y": 0.4334929883480072}, {"X": 0.17404650151729584, "Y": 0.4334930181503296}]}, "Id": "082fb6c4-55a9-4dd7-b71d-8956c7f97396"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06599061191082001, "Height": 0.01345018856227398, "Left": 0.23633751273155212, "Top": 0.42266547679901123}, "Polygon": [{"X": 0.23633751273155212, "Y": 0.4226655066013336}, {"X": 0.30232810974121094, "Y": 0.42266547679901123}, {"X": 0.30232810974121094, "Y": 0.4361156225204468}, {"X": 0.23633752763271332, "Y": 0.43611565232276917}]}, "Id": "31d30088-1c39-40e7-a52c-c82bbcf9ab8d"}, {"BlockType": "WORD", "Confidence": 99.53702545166016, "Text": "352", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0326036661863327, "Height": 0.010998429730534554, "Left": 0.4350687265396118, "Top": 0.4225357472896576}, "Polygon": [{"X": 0.4350687265396118, "Y": 0.42253577709198}, {"X": 0.4676723778247833, "Y": 0.4225357472896576}, {"X": 0.4676723778247833, "Y": 0.433534175157547}, {"X": 0.4350687265396118, "Y": 0.433534175157547}]}, "Id": "87c23390-67ea-4ead-871b-0cdcfa99531d"}, {"BlockType": "WORD", "Confidence": 89.35746002197266, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005877962335944176, "Height": 0.0023722618352621794, "Left": 0.46965745091438293, "Top": 0.4283265471458435}, "Polygon": [{"X": 0.46965745091438293, "Y": 0.4283265471458435}, {"X": 0.47553542256355286, "Y": 0.4283265471458435}, {"X": 0.47553539276123047, "Y": 0.43069881200790405}, {"X": 0.46965745091438293, "Y": 0.43069881200790405}]}, "Id": "6844f5a6-4c9d-4455-9e3a-9df0d1e3ec49"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "Avenel,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05921238660812378, "Height": 0.012877210043370724, "Left": 0.4804132282733917, "Top": 0.42263272404670715}, "Polygon": [{"X": 0.4804132580757141, "Y": 0.42263275384902954}, {"X": 0.5396256446838379, "Y": 0.42263272404670715}, {"X": 0.5396255850791931, "Y": 0.43550989031791687}, {"X": 0.4804132282733917, "Y": 0.43550992012023926}]}, "Id": "c3814d12-0af1-4eb2-9e86-51a5042c4785"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "NJ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021399417892098427, "Height": 0.011089809238910675, "Left": 0.5457770824432373, "Top": 0.422454297542572}, "Polygon": [{"X": 0.5457770824432373, "Y": 0.422454297542572}, {"X": 0.5671764612197876, "Y": 0.422454297542572}, {"X": 0.5671764612197876, "Y": 0.4335440993309021}, {"X": 0.5457770824432373, "Y": 0.4335440993309021}]}, "Id": "706bc274-7a35-45eb-8c24-00b831f44249"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "07/16/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08965527266263962, "Height": 0.01118924468755722, "Left": 0.6825687885284424, "Top": 0.4224686622619629}, "Polygon": [{"X": 0.6825687885284424, "Y": 0.4224686920642853}, {"X": 0.7722240686416626, "Y": 0.4224686622619629}, {"X": 0.7722240090370178, "Y": 0.43365785479545593}, {"X": 0.6825687885284424, "Y": 0.4336578845977783}]}, "Id": "47948d9b-d3f9-4e00-af07-1f0df150165e"}, {"BlockType": "WORD", "Confidence": 99.5703125, "Text": "2:10", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03440781310200691, "Height": 0.011252599768340588, "Left": 0.7777166962623596, "Top": 0.42250335216522217}, "Polygon": [{"X": 0.7777166962623596, "Y": 0.42250338196754456}, {"X": 0.812124490737915, "Y": 0.42250335216522217}, {"X": 0.8121244311332703, "Y": 0.43375593423843384}, {"X": 0.7777166962623596, "Y": 0.4337559640407562}]}, "Id": "406da5e7-db5f-44be-be07-658c42b90ba0"}, {"BlockType": "WORD", "Confidence": 99.638671875, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026347104460000992, "Height": 0.010879220440983772, "Left": 0.8182988166809082, "Top": 0.42276719212532043}, "Polygon": [{"X": 0.818298876285553, "Y": 0.42276719212532043}, {"X": 0.8446459174156189, "Y": 0.42276719212532043}, {"X": 0.8446458578109741, "Y": 0.43364638090133667}, {"X": 0.8182988166809082, "Y": 0.43364641070365906}]}, "Id": "3763978f-9afa-4bd3-af3a-e63b5ad35b61"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Dimensions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07157628983259201, "Height": 0.00893399864435196, "Left": 0.08117885142564774, "Top": 0.46921271085739136}, "Polygon": [{"X": 0.08117885142564774, "Y": 0.46921274065971375}, {"X": 0.15275512635707855, "Y": 0.46921271085739136}, {"X": 0.15275514125823975, "Y": 0.4781467020511627}, {"X": 0.08117888867855072, "Y": 0.4781467318534851}]}, "Id": "695b0333-3d3a-4b29-a43a-e2b2eca643ad"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "outlined", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04930208623409271, "Height": 0.008868868462741375, "Left": 0.1567339450120926, "Top": 0.4691767692565918}, "Polygon": [{"X": 0.1567339450120926, "Y": 0.4691767692565918}, {"X": 0.2060360163450241, "Y": 0.4691767692565918}, {"X": 0.2060360312461853, "Y": 0.47804561257362366}, {"X": 0.15673397481441498, "Y": 0.47804564237594604}]}, "Id": "92ad9247-0e8d-4a99-9a3d-6d9b635740c6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "above", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03599059209227562, "Height": 0.008840699680149555, "Left": 0.20941726863384247, "Top": 0.4692149758338928}, "Polygon": [{"X": 0.20941726863384247, "Y": 0.4692149758338928}, {"X": 0.245407834649086, "Y": 0.4692149758338928}, {"X": 0.2454078495502472, "Y": 0.4780556559562683}, {"X": 0.20941728353500366, "Y": 0.4780556857585907}]}, "Id": "fc4bc1a2-4e51-4005-9f74-0d801669cd57"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018370358273386955, "Height": 0.0064223636873066425, "Left": 0.24891775846481323, "Top": 0.47159072756767273}, "Polygon": [{"X": 0.24891775846481323, "Y": 0.47159072756767273}, {"X": 0.26728811860084534, "Y": 0.47159072756767273}, {"X": 0.26728811860084534, "Y": 0.4780130684375763}, {"X": 0.24891777336597443, "Y": 0.4780130982398987}]}, "Id": "b1137d3f-11e6-4d9e-a3c7-5f22e6f82fd2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "actual", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035669948905706406, "Height": 0.009075846523046494, "Left": 0.27097612619400024, "Top": 0.46904173493385315}, "Polygon": [{"X": 0.27097612619400024, "Y": 0.46904173493385315}, {"X": 0.30664607882499695, "Y": 0.46904173493385315}, {"X": 0.30664607882499695, "Y": 0.47811755537986755}, {"X": 0.27097615599632263, "Y": 0.47811755537986755}]}, "Id": "9576a8d4-fd3b-43ca-b736-09f3343c237b"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "dimensions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06803827732801437, "Height": 0.009089900180697441, "Left": 0.31057408452033997, "Top": 0.46901392936706543}, "Polygon": [{"X": 0.31057408452033997, "Y": 0.4690139591693878}, {"X": 0.37861236929893494, "Y": 0.46901392936706543}, {"X": 0.37861236929893494, "Y": 0.4781038165092468}, {"X": 0.31057408452033997, "Y": 0.4781038463115692}]}, "Id": "3e331638-a575-4a45-b9df-f1e0bbadde11"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02175445482134819, "Height": 0.008926015347242355, "Left": 0.3823860287666321, "Top": 0.4691341817378998}, "Polygon": [{"X": 0.3823860287666321, "Y": 0.4691341817378998}, {"X": 0.4041404724121094, "Y": 0.4691341817378998}, {"X": 0.4041404724121094, "Y": 0.47806018590927124}, {"X": 0.3823860287666321, "Y": 0.47806018590927124}]}, "Id": "7ec01ec1-3afd-4ff2-a2a4-e852eb3dc26d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02625599503517151, "Height": 0.008708211593329906, "Left": 0.4076123833656311, "Top": 0.4717337191104889}, "Polygon": [{"X": 0.4076123833656311, "Y": 0.4717337191104889}, {"X": 0.4338683784008026, "Y": 0.4717337191104889}, {"X": 0.4338683784008026, "Y": 0.4804419279098511}, {"X": 0.4076123833656311, "Y": 0.4804419279098511}]}, "Id": "b0119c4d-6dc7-459a-8364-a89eca326bce"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014591105282306671, "Height": 0.008896342478692532, "Left": 0.43713271617889404, "Top": 0.4691045582294464}, "Polygon": [{"X": 0.43713271617889404, "Y": 0.4691045582294464}, {"X": 0.4517238140106201, "Y": 0.4691045582294464}, {"X": 0.4517238140106201, "Y": 0.4780009090900421}, {"X": 0.43713271617889404, "Y": 0.4780009090900421}]}, "Id": "12885301-35bb-4223-a832-7bc2629db1e0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "subject", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04300111532211304, "Height": 0.011463496834039688, "Left": 0.4551527500152588, "Top": 0.4690726101398468}, "Polygon": [{"X": 0.4551527500152588, "Y": 0.4690726101398468}, {"X": 0.4981538653373718, "Y": 0.4690726101398468}, {"X": 0.49815383553504944, "Y": 0.4805360734462738}, {"X": 0.4551527500152588, "Y": 0.4805361032485962}]}, "Id": "2689b1d3-e1fb-4388-a7cd-d2d8de35ddbd"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011559096165001392, "Height": 0.007351268548518419, "Left": 0.5015245079994202, "Top": 0.47057628631591797}, "Polygon": [{"X": 0.5015245676040649, "Y": 0.47057628631591797}, {"X": 0.5130836367607117, "Y": 0.47057628631591797}, {"X": 0.5130836367607117, "Y": 0.4779275357723236}, {"X": 0.5015245079994202, "Y": 0.4779275357723236}]}, "Id": "83630195-f12a-401e-a788-ed40086819b2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027772855013608932, "Height": 0.008696167729794979, "Left": 0.517153799533844, "Top": 0.46937596797943115}, "Polygon": [{"X": 0.517153799533844, "Y": 0.46937596797943115}, {"X": 0.544926643371582, "Y": 0.46937596797943115}, {"X": 0.544926643371582, "Y": 0.4780721068382263}, {"X": 0.517153799533844, "Y": 0.4780721068382263}]}, "Id": "3987433a-0076-4a57-a1ed-511820d35365"}, {"BlockType": "WORD", "Confidence": 99.8046875, "Text": "110", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021057212725281715, "Height": 0.008832442574203014, "Left": 0.5495284795761108, "Top": 0.4692605435848236}, "Polygon": [{"X": 0.5495284795761108, "Y": 0.4692605435848236}, {"X": 0.5705856680870056, "Y": 0.4692605435848236}, {"X": 0.5705856680870056, "Y": 0.4780929982662201}, {"X": 0.5495284795761108, "Y": 0.4780929982662201}]}, "Id": "7f446de7-436c-499f-a1e7-7ab9dbf2b402"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "paragraph", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06059238314628601, "Height": 0.011177766136825085, "Left": 0.5738162994384766, "Top": 0.4694291353225708}, "Polygon": [{"X": 0.5738162994384766, "Y": 0.4694291353225708}, {"X": 0.6344086527824402, "Y": 0.4694291353225708}, {"X": 0.6344086527824402, "Y": 0.48060688376426697}, {"X": 0.5738162994384766, "Y": 0.48060688376426697}]}, "Id": "45da0e5d-50a9-45ff-91f6-b3ce0daee096"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014015301130712032, "Height": 0.008785919286310673, "Left": 0.6388260722160339, "Top": 0.4692036509513855}, "Polygon": [{"X": 0.6388260722160339, "Y": 0.4692036807537079}, {"X": 0.6528413891792297, "Y": 0.4692036509513855}, {"X": 0.652841329574585, "Y": 0.4779895842075348}, {"X": 0.6388260722160339, "Y": 0.4779895842075348}]}, "Id": "949d48b5-e367-4bda-91f3-125a0ba2c592"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017698094248771667, "Height": 0.008883691392838955, "Left": 0.6567361950874329, "Top": 0.46923065185546875}, "Polygon": [{"X": 0.6567361950874329, "Y": 0.46923065185546875}, {"X": 0.6744343042373657, "Y": 0.46923065185546875}, {"X": 0.674434244632721, "Y": 0.4781143367290497}, {"X": 0.6567361950874329, "Y": 0.4781143367290497}]}, "Id": "f15f44c0-bd97-4d9c-9a70-66509683b574"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "calculating", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06592697650194168, "Height": 0.011492665857076645, "Left": 0.08120424300432205, "Top": 0.48342055082321167}, "Polygon": [{"X": 0.08120424300432205, "Y": 0.48342058062553406}, {"X": 0.14713118970394135, "Y": 0.48342055082321167}, {"X": 0.14713121950626373, "Y": 0.4949132204055786}, {"X": 0.08120428025722504, "Y": 0.4949132204055786}]}, "Id": "95ec6614-9e13-4d5e-9509-2abe908249ef"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "effective", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052391793578863144, "Height": 0.009175362065434456, "Left": 0.15061166882514954, "Top": 0.4832383096218109}, "Polygon": [{"X": 0.15061166882514954, "Y": 0.4832383394241333}, {"X": 0.2030034363269806, "Y": 0.4832383096218109}, {"X": 0.20300345122814178, "Y": 0.4924136698246002}, {"X": 0.15061168372631073, "Y": 0.4924136698246002}]}, "Id": "528beaeb-a1ab-46aa-8c8d-09ca0eb773fe"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "occupancy.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06605710089206696, "Height": 0.00875705387443304, "Left": 0.2067718803882599, "Top": 0.4859817624092102}, "Polygon": [{"X": 0.2067718803882599, "Y": 0.4859817922115326}, {"X": 0.27282896637916565, "Y": 0.4859817624092102}, {"X": 0.27282899618148804, "Y": 0.4947388172149658}, {"X": 0.20677189528942108, "Y": 0.4947388172149658}]}, "Id": "8e844f4d-36f9-41fd-bb1b-120ee498b1bf"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "a0d57291-810b-4459-a186-d467f610742f", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "a0d57291-810b-4459-a186-d467f610742f", "content-type": "application/x-amz-json-1.1", "content-length": "60415", "date": "Wed, 24 Sep 2025 07:19:42 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "41106cf5-a402-436d-9e97-cdc2cf757b85", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 07:19:45 GMT", "content-type": "application/json", "content-length": "1389", "connection": "keep-alive", "x-amzn-requestid": "41106cf5-a402-436d-9e97-cdc2cf757b85"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. We have one page. It looks like a Freight Certificate, not a standard doc type in list. There's no invoice, BOL, POD, etc. It's a freight certificate, likely a \"weight_and_inspection_cert\"? The content: dimension/cube, weight, total weight 280 lbs, total rated dimensions, etc. That looks like weight and inspection cert. The doc type \"weight_and_inspection_cert\" is defined for certificate/report issued by authority confirming weight with length, width, height. Keywords include \"Certificate of Analysis\", \"Measurement Certificate\", \"Weight Certificate\", \"Inspection certificate\", \"W&I\", \"Calibration Certificate\". This doc has \"Freight Certificate\" and includes weight, dimensions. Might be weight_and_inspection_cert. The nmfc_cert is for NMFC classification certificate, not relevant. So classify as weight_and_inspection_cert.\n\nWe must output tool call classify_logistics_doc_type with documents array containing one object: page_no: 1, doc_type: \"weight_and_inspection_cert\".\n\nLet's do that."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"weight_and_inspection_cert\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2577, "outputTokens": 238, "totalTokens": 2815}, "metrics": {"latencyMs": 1272}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/aabb68b2_W_XCJLXZK140FUS8020ZAG.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination, keywords indication and key fields/structure are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight with length, width and height.\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. If weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: This will include keywords like fright (As described vs As found) or (Original vs inspection) or Corrected class or Correction information\n        Other keywords indication:  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n®\nABF\nDIMENSION/CUBE\nFreight\nCERTIFICATE\nAn ArcBest Company\nPronumber:\n352309638\nTrailer No: CITY 410498\nShipper:\nHeadstone Deals\nTotals\nTotal Weight:\n280 lbs\nTotal Rated Dimensions:\n48 L X 40 W X 16 H\nTotal Mileage:\n1,900 Miles\nTotal Rated Cube:\n17.78 Cubic Feet\nTotal Rated Density:\n15.748 PCF\nTrailer Measurements Done By Dock\nName\nStation\nDate\nLouinel Auguste\n352 - Avenel, NJ\n07/16/2025 2:10 PM\nDimensions outlined above are actual dimensions and may be subject to Item 110 paragraph 14 for\ncalculating effective occupancy.\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}