{"classification_result": {"documents": [{"page_no": 1, "doc_type": "log"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 9.278808283852413e-05}, {"X": 1.0, "Y": 1.0}, {"X": 5.838295464855037e-07, "Y": 1.0}]}, "Id": "6a6f16d8-4868-4d28-a9ce-bc56e05cf96e", "Relationships": [{"Type": "CHILD", "Ids": ["c56cd3c2-afe3-4169-8ce3-1f1b9575cd6e", "61452e4a-c65a-4d81-bf3e-31189a9ef7a2", "c2bc9ff3-0524-4034-b25b-d172959e6025", "e1ec0184-082e-4eaf-ab35-9407cd43eb39", "a44f4998-736f-4cb7-9c4a-85feae8ba42f", "389b3bbf-59fb-4763-aef7-33e9f14eaeb1", "6439caad-0240-460f-ad47-ae6520d2e4d6", "1fbcaf1e-2952-416e-9d8c-441dffba7ac4", "bb9fa12b-2886-4706-9bb5-32b72019be81", "c99e7628-4ba8-464f-9662-85f93bfb86e7", "cab62c0b-fb16-41d5-964f-1f8efef9ecd6", "3d9dbe7a-7d9b-4dbb-96c7-6937381b0e78", "212cadc3-7f1a-4b2f-96e7-81e40a02a95d", "364b80f3-79b2-41ac-a7c6-b06df76e7f6e", "9661312c-13fe-4a2f-a73e-7cc7937a20cc", "32c17abd-b294-40eb-a235-f2c7a2951a4b", "5cfc65de-3b24-433a-8554-fdbcebc18400", "31ad275b-6867-4be3-96a8-a252028a5012", "39bef7a6-0dde-4dcf-8159-aea41e6f587e", "ece98d6d-7fae-4eff-882a-ec4fd4f3af0b", "4834cb1f-a050-4e3d-85d2-7e98bdca08bd", "e7fe776b-c4d7-485b-b31d-e9ed5bc89398", "329d853a-bcb8-416a-b34d-a7700fbfb8b5", "59462925-9656-4e97-9d6d-cb1b31072bea", "7bba3b7d-9193-4881-b91f-3d1b9072f8d5", "f02c80f6-6ee1-4962-a132-62780f9778bc", "85073e67-1906-4500-a965-793bcadf00e3", "e301f9c4-b948-4b4c-8b42-ebeda718211f", "301ee95b-2dcf-432e-b4ec-e0b08c32149e", "6b114c44-c2d9-49ea-b287-8fd4254d97c6", "0d86aad7-2623-44dc-b3ca-488586820b97", "58f699d6-2150-4950-936b-70f3de419065", "667bec83-463a-4381-a72c-f83bed1a8bfa", "0ef73f28-02c8-4589-8fff-f27611a857f8", "a6865cba-9845-4d46-a57f-4f63bdd123e1", "fc101c49-da02-443d-b4a2-9403b1a2721c", "269b0f79-253a-4bbb-8237-a9936fc3bd93"]}]}, {"BlockType": "LINE", "Confidence": 99.48406982421875, "Text": "DATE: 16-Jun Metal Finishing", "Geometry": {"BoundingBox": {"Width": 0.5784920454025269, "Height": 0.04258476570248604, "Left": 0.24103447794914246, "Top": 0.044791046530008316}, "Polygon": [{"X": 0.24103447794914246, "Y": 0.044791046530008316}, {"X": 0.8194988369941711, "Y": 0.04757129028439522}, {"X": 0.8195264935493469, "Y": 0.08737581223249435}, {"X": 0.24106118083000183, "Y": 0.08472535014152527}]}, "Id": "c56cd3c2-afe3-4169-8ce3-1f1b9575cd6e", "Relationships": [{"Type": "CHILD", "Ids": ["88264f10-3ef9-4a57-abdd-3e0b9d6b49bf", "ffb5c5a1-0e78-4306-9115-f0fa50db78ee", "d22052aa-a8a0-4234-b516-f9e1c7cf0586", "5d4d07e7-9df8-4d9e-b494-9bcbd466dfcf"]}]}, {"BlockType": "LINE", "Confidence": 94.455810546875, "Text": "UNIT #: 6063/283420 Receipt", "Geometry": {"BoundingBox": {"Width": 0.43151766061782837, "Height": 0.023051977157592773, "Left": 0.2222989797592163, "Top": 0.09197241812944412}, "Polygon": [{"X": 0.2222989797592163, "Y": 0.09197241812944412}, {"X": 0.6538021564483643, "Y": 0.09393174201250076}, {"X": 0.6538166403770447, "Y": 0.1150243952870369}, {"X": 0.22231309115886688, "Y": 0.1131163239479065}]}, "Id": "61452e4a-c65a-4d81-bf3e-31189a9ef7a2", "Relationships": [{"Type": "CHILD", "Ids": ["c2eb5a13-f2f2-4bff-9fed-fe7d1dd0fa89", "d93b91ad-6160-4550-8f07-5c70d25f2a0a", "f1bf640a-7b8e-4adf-bcd0-93acd3a7f1ba", "d65feff6-916e-4586-a9f2-05b0d0ba33f0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Truck Ready Time:", "Geometry": {"BoundingBox": {"Width": 0.18300969898700714, "Height": 0.01806115172803402, "Left": 0.4778715670108795, "Top": 0.11589237302541733}, "Polygon": [{"X": 0.4778715670108795, "Y": 0.11589237302541733}, {"X": 0.6608694195747375, "Y": 0.11669988185167313}, {"X": 0.6608812808990479, "Y": 0.1339535266160965}, {"X": 0.47788330912590027, "Y": 0.13316379487514496}]}, "Id": "c2bc9ff3-0524-4034-b25b-d172959e6025", "Relationships": [{"Type": "CHILD", "Ids": ["c29a0230-4379-491c-8926-51ac9a1c8daf", "9869ce92-861b-4c10-8137-0859a764de71", "c7d3dc5d-bdd5-44c6-990b-5959e71982a4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.18543614447116852, "Height": 0.014749633148312569, "Left": 0.21138796210289001, "Top": 0.13396279513835907}, "Polygon": [{"X": 0.21138796210289001, "Y": 0.13396279513835907}, {"X": 0.3968146741390228, "Y": 0.13476097583770752}, {"X": 0.3968241214752197, "Y": 0.1487124264240265}, {"X": 0.21139727532863617, "Y": 0.14792880415916443}]}, "Id": "e1ec0184-082e-4eaf-ab35-9407cd43eb39", "Relationships": [{"Type": "CHILD", "Ids": ["e6f20dc5-6407-4ee6-9d41-4a7f1287a43f", "20514297-3ba2-415d-9bff-e5012106ac79"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Truck Departure:", "Geometry": {"BoundingBox": {"Width": 0.16578447818756104, "Height": 0.017503205686807632, "Left": 0.47829118371009827, "Top": 0.15436407923698425}, "Polygon": [{"X": 0.47829118371009827, "Y": 0.15436407923698425}, {"X": 0.6440641283988953, "Y": 0.15505969524383545}, {"X": 0.6440756320953369, "Y": 0.1718672811985016}, {"X": 0.47830259799957275, "Y": 0.17118734121322632}]}, "Id": "a44f4998-736f-4cb7-9c4a-85feae8ba42f", "Relationships": [{"Type": "CHILD", "Ids": ["663dd8b0-1da1-4ed4-b356-99296104a9e4", "edff4dc8-2720-474e-92f4-60b46e82fc33"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.26424476504325867, "Height": 0.015759959816932678, "Left": 0.10690374672412872, "Top": 0.17184032499790192}, "Polygon": [{"X": 0.10690374672412872, "Y": 0.17184032499790192}, {"X": 0.3711386024951935, "Y": 0.17292088270187378}, {"X": 0.3711484968662262, "Y": 0.1876002848148346}, {"X": 0.1069134846329689, "Y": 0.18654154241085052}]}, "Id": "389b3bbf-59fb-4763-aef7-33e9f14eaeb1", "Relationships": [{"Type": "CHILD", "Ids": ["8339718e-bea2-4481-9f05-ab1b125869bd", "79f99c06-1f8a-478b-a9d3-6634076df462", "62e597e4-ff5b-498a-a881-a9b5cda7334e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "PICK", "Geometry": {"BoundingBox": {"Width": 0.04759252443909645, "Height": 0.012118658982217312, "Left": 0.34071293473243713, "Top": 0.2065531313419342}, "Polygon": [{"X": 0.34071293473243713, "Y": 0.2065531313419342}, {"X": 0.3882974088191986, "Y": 0.2067386955022812}, {"X": 0.3883054554462433, "Y": 0.2186717987060547}, {"X": 0.3407209813594818, "Y": 0.21848942339420319}]}, "Id": "6439caad-0240-460f-ad47-ae6520d2e4d6", "Relationships": [{"Type": "CHILD", "Ids": ["f7f39ed1-8dfb-402e-9271-af7693a67987"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "UP DEL", "Geometry": {"BoundingBox": {"Width": 0.10688525438308716, "Height": 0.01430320180952549, "Left": 0.35068249702453613, "Top": 0.22607578337192535}, "Polygon": [{"X": 0.35068249702453613, "Y": 0.22607578337192535}, {"X": 0.4575583040714264, "Y": 0.2264808565378189}, {"X": 0.4575677514076233, "Y": 0.2403789907693863}, {"X": 0.35069185495376587, "Y": 0.23998227715492249}]}, "Id": "1fbcaf1e-2952-416e-9d8c-441dffba7ac4", "Relationships": [{"Type": "CHILD", "Ids": ["4837bfd5-2fbb-4ffe-91bb-d332029cd7d1", "cf83032c-c148-4cf9-adb1-1c534681d2ae"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.1077544167637825, "Height": 0.013697322458028793, "Left": 0.8886743187904358, "Top": 0.22138719260692596}, "Polygon": [{"X": 0.8886743187904358, "Y": 0.22138719260692596}, {"X": 0.996419370174408, "Y": 0.22179964184761047}, {"X": 0.9964287281036377, "Y": 0.23508451879024506}, {"X": 0.888683557510376, "Y": 0.2346801459789276}]}, "Id": "bb9fa12b-2886-4706-9bb5-32b72019be81", "Relationships": [{"Type": "CHILD", "Ids": ["c912eade-c883-462c-94f6-a811bb8179d9", "b7ba118b-280a-48b4-a5c9-af046abc74cc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.11353284120559692, "Height": 0.01488695852458477, "Left": 0.14350344240665436, "Top": 0.22728955745697021}, "Polygon": [{"X": 0.14350344240665436, "Y": 0.22728955745697021}, {"X": 0.2570266127586365, "Y": 0.2277185320854187}, {"X": 0.2570362985134125, "Y": 0.24217650294303894}, {"X": 0.1435130536556244, "Y": 0.24175673723220825}]}, "Id": "c99e7628-4ba8-464f-9662-85f93bfb86e7", "Relationships": [{"Type": "CHILD", "Ids": ["efa2dbf7-f2c2-4d4f-bc18-cca035c2203a"]}]}, {"BlockType": "LINE", "Confidence": 99.86653900146484, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.162194162607193, "Height": 0.014337273314595222, "Left": 0.5946463942527771, "Top": 0.22804084420204163}, "Polygon": [{"X": 0.5946463942527771, "Y": 0.22804084420204163}, {"X": 0.7568311095237732, "Y": 0.22865459322929382}, {"X": 0.7568405866622925, "Y": 0.2423781156539917}, {"X": 0.5946558117866516, "Y": 0.24177691340446472}]}, "Id": "cab62c0b-fb16-41d5-964f-1f8efef9ecd6", "Relationships": [{"Type": "CHILD", "Ids": ["bec07f8d-0c9a-4e8d-8591-062c411bbbe7", "d95388ad-4317-479b-b7c5-06a28c40277b", "fbca7029-ef04-41e9-bab9-93acbefd59e3"]}]}, {"BlockType": "LINE", "Confidence": 94.54081726074219, "Text": "ISK", "Geometry": {"BoundingBox": {"Width": 0.06304258108139038, "Height": 0.026615964248776436, "Left": 0.4711299538612366, "Top": 0.2555736005306244}, "Polygon": [{"X": 0.4711299538612366, "Y": 0.2555736005306244}, {"X": 0.5341545343399048, "Y": 0.255802184343338}, {"X": 0.534172534942627, "Y": 0.2821895480155945}, {"X": 0.4711478650569916, "Y": 0.281970351934433}]}, "Id": "3d9dbe7a-7d9b-4dbb-96c7-6937381b0e78", "Relationships": [{"Type": "CHILD", "Ids": ["d746c21e-d155-4723-8aea-e4fc200ef6e1"]}]}, {"BlockType": "LINE", "Confidence": 99.77140808105469, "Text": "1020", "Geometry": {"BoundingBox": {"Width": 0.07413194328546524, "Height": 0.01809423230588436, "Left": 0.8994511961936951, "Top": 0.25593405961990356}, "Polygon": [{"X": 0.8994511961936951, "Y": 0.25593405961990356}, {"X": 0.9735706448554993, "Y": 0.25620338320732117}, {"X": 0.9735831618309021, "Y": 0.27402830123901367}, {"X": 0.8994636535644531, "Y": 0.2737664580345154}]}, "Id": "212cadc3-7f1a-4b2f-96e7-81e40a02a95d", "Relationships": [{"Type": "CHILD", "Ids": ["22faaff9-4811-4cf1-a1aa-63644f48cfee"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.03494357317686081, "Height": 0.0365309901535511, "Left": 0.012326158583164215, "Top": 0.26877906918525696}, "Polygon": [{"X": 0.012326158583164215, "Y": 0.26877906918525696}, {"X": 0.04724571481347084, "Y": 0.26890280842781067}, {"X": 0.047269731760025024, "Y": 0.30531007051467896}, {"X": 0.01235012337565422, "Y": 0.3051934838294983}]}, "Id": "364b80f3-79b2-41ac-a7c6-b06df76e7f6e", "Relationships": [{"Type": "CHILD", "Ids": ["93dd08f0-624b-4ee5-96db-781ca78934cf"]}]}, {"BlockType": "LINE", "Confidence": 76.5587158203125, "Text": "1sm Box", "Geometry": {"BoundingBox": {"Width": 0.17747357487678528, "Height": 0.029952935874462128, "Left": 0.09863375127315521, "Top": 0.263430118560791}, "Polygon": [{"X": 0.09863375127315521, "Y": 0.263430118560791}, {"X": 0.2760876715183258, "Y": 0.2640645205974579}, {"X": 0.2761073112487793, "Y": 0.29338306188583374}, {"X": 0.09865318238735199, "Y": 0.2927778959274292}]}, "Id": "9661312c-13fe-4a2f-a73e-7cc7937a20cc", "Relationships": [{"Type": "CHILD", "Ids": ["d4ad37ef-60ec-4173-8702-fe9ca3e8f613", "9150cafc-9e12-4a11-8a0f-211fa2cb679f"]}]}, {"BlockType": "LINE", "Confidence": 99.8396987915039, "Text": "Automation & Modular", "Geometry": {"BoundingBox": {"Width": 0.22368553280830383, "Height": 0.01609792374074459, "Left": 0.07606939226388931, "Top": 0.28632867336273193}, "Polygon": [{"X": 0.07606939226388931, "Y": 0.28632867336273193}, {"X": 0.2997446358203888, "Y": 0.2870994508266449}, {"X": 0.29975494742393494, "Y": 0.30242660641670227}, {"X": 0.07607953995466232, "Y": 0.30167508125305176}]}, "Id": "32c17abd-b294-40eb-a235-f2c7a2951a4b", "Relationships": [{"Type": "CHILD", "Ids": ["1318c2cc-4470-40eb-b3e8-46242f3dd2d3", "961f4d55-718b-46ce-b262-bfa486ac89a2", "508fe631-584f-4672-ad83-270930e2c707"]}]}, {"BlockType": "LINE", "Confidence": 97.45296478271484, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.012313039042055607, "Height": 0.011039203964173794, "Left": 0.3577520251274109, "Top": 0.2902805209159851}, "Polygon": [{"X": 0.3577520251274109, "Y": 0.2902805209159851}, {"X": 0.3700576424598694, "Y": 0.2903227210044861}, {"X": 0.3700650632381439, "Y": 0.3013197183609009}, {"X": 0.35775941610336304, "Y": 0.301278293132782}]}, "Id": "5cfc65de-3b24-433a-8554-fdbcebc18400", "Relationships": [{"Type": "CHILD", "Ids": ["f2dd4566-ded0-4cf7-a80d-f1b381999ba5"]}]}, {"BlockType": "LINE", "Confidence": 97.02716827392578, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.15388645231723785, "Height": 0.013512854464352131, "Left": 0.43144533038139343, "Top": 0.2887952923774719}, "Polygon": [{"X": 0.43144533038139343, "Y": 0.2887952923774719}, {"X": 0.585322916507721, "Y": 0.28932449221611023}, {"X": 0.5853317975997925, "Y": 0.3023081421852112}, {"X": 0.4314541518688202, "Y": 0.3017902076244354}]}, "Id": "31ad275b-6867-4be3-96a8-a252028a5012", "Relationships": [{"Type": "CHILD", "Ids": ["990607f2-dfab-4fa2-8fe8-ffca43cfaeb6", "fda10214-5616-4eaa-987d-f2ace2f7a3a2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "1030", "Geometry": {"BoundingBox": {"Width": 0.07263769209384918, "Height": 0.019073421135544777, "Left": 0.8952206969261169, "Top": 0.3086911737918854}, "Polygon": [{"X": 0.8952206969261169, "Y": 0.3086911737918854}, {"X": 0.9678452014923096, "Y": 0.30893343687057495}, {"X": 0.9678583741188049, "Y": 0.3277646005153656}, {"X": 0.8952338695526123, "Y": 0.32753005623817444}]}, "Id": "39bef7a6-0dde-4dcf-8159-aea41e6f587e", "Relationships": [{"Type": "CHILD", "Ids": ["f39233a3-eb88-4043-b215-a517c9336a98"]}]}, {"BlockType": "LINE", "Confidence": 66.3318099975586, "Text": "Ism SR", "Geometry": {"BoundingBox": {"Width": 0.14051853120326996, "Height": 0.04179235175251961, "Left": 0.10696327686309814, "Top": 0.3491291403770447}, "Polygon": [{"X": 0.10696327686309814, "Y": 0.3491291403770447}, {"X": 0.24745413661003113, "Y": 0.3495638370513916}, {"X": 0.2474817931652069, "Y": 0.3909215033054352}, {"X": 0.10699068754911423, "Y": 0.39051946997642517}]}, "Id": "ece98d6d-7fae-4eff-882a-ec4fd4f3af0b", "Relationships": [{"Type": "CHILD", "Ids": ["c0ad3d23-1640-423d-a5b4-2f6ad786082e", "281e15a5-df65-4e3e-920b-5674851bb71f"]}]}, {"BlockType": "LINE", "Confidence": 42.938255310058594, "Text": "Isk", "Geometry": {"BoundingBox": {"Width": 0.06696676462888718, "Height": 0.03149253502488136, "Left": 0.4840385615825653, "Top": 0.35507461428642273}, "Polygon": [{"X": 0.4840385615825653, "Y": 0.35507461428642273}, {"X": 0.5509839653968811, "Y": 0.35527995228767395}, {"X": 0.5510053038597107, "Y": 0.3865671455860138}, {"X": 0.4840598404407501, "Y": 0.3863736093044281}]}, "Id": "4834cb1f-a050-4e3d-85d2-7e98bdca08bd", "Relationships": [{"Type": "CHILD", "Ids": ["14c885a9-8b64-42b3-9cd7-32954a26179f"]}]}, {"BlockType": "LINE", "Confidence": 99.79093933105469, "Text": "1105", "Geometry": {"BoundingBox": {"Width": 0.06733477860689163, "Height": 0.020313508808612823, "Left": 0.897701621055603, "Top": 0.36111822724342346}, "Polygon": [{"X": 0.897701621055603, "Y": 0.36111822724342346}, {"X": 0.9650223255157471, "Y": 0.36132287979125977}, {"X": 0.9650363922119141, "Y": 0.3814317286014557}, {"X": 0.89771568775177, "Y": 0.38123467564582825}]}, "Id": "e7fe776b-c4d7-485b-b31d-e9ed5bc89398", "Relationships": [{"Type": "CHILD", "Ids": ["73c32551-95c9-4f4c-bfef-2b912b8a8a7c"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "3", "Geometry": {"BoundingBox": {"Width": 0.029292328283190727, "Height": 0.03867295756936073, "Left": 0.015774134546518326, "Top": 0.37743908166885376}, "Polygon": [{"X": 0.015774134546518326, "Y": 0.37743908166885376}, {"X": 0.04504100978374481, "Y": 0.3775249421596527}, {"X": 0.045066460967063904, "Y": 0.4161120355129242}, {"X": 0.015799539163708687, "Y": 0.4160325229167938}]}, "Id": "329d853a-bcb8-416a-b34d-a7700fbfb8b5", "Relationships": [{"Type": "CHILD", "Ids": ["0547605e-ced8-4818-a605-7f4784ae99da"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON>", "Geometry": {"BoundingBox": {"Width": 0.17783896625041962, "Height": 0.01794452965259552, "Left": 0.0775209292769432, "Top": 0.3900987505912781}, "Polygon": [{"X": 0.0775209292769432, "Y": 0.3900987505912781}, {"X": 0.25534823536872864, "Y": 0.3906079828739166}, {"X": 0.2553598880767822, "Y": 0.4080432653427124}, {"X": 0.07753246277570724, "Y": 0.40755146741867065}]}, "Id": "59462925-9656-4e97-9d6d-cb1b31072bea", "Relationships": [{"Type": "CHILD", "Ids": ["18f50383-c7b9-4449-8de9-6de82a092abf", "2a77d0bd-4fcd-4dcb-9d09-6745d935caaf"]}]}, {"BlockType": "LINE", "Confidence": 93.89568328857422, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.012405311688780785, "Height": 0.01091103907674551, "Left": 0.3587411046028137, "Top": 0.3937020003795624}, "Polygon": [{"X": 0.3587411046028137, "Y": 0.3937020003795624}, {"X": 0.3711390793323517, "Y": 0.3937372863292694}, {"X": 0.37114641070365906, "Y": 0.40461301803588867}, {"X": 0.3587484359741211, "Y": 0.40457847714424133}]}, "Id": "7bba3b7d-9193-4881-b91f-3d1b9072f8d5", "Relationships": [{"Type": "CHILD", "Ids": ["d4fc24a5-5d9d-4267-a997-87d2c09deb0b"]}]}, {"BlockType": "LINE", "Confidence": 97.9308853149414, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.15276016294956207, "Height": 0.013504955917596817, "Left": 0.43235671520233154, "Top": 0.3918451964855194}, "Polygon": [{"X": 0.43235671520233154, "Y": 0.3918451964855194}, {"X": 0.5851079225540161, "Y": 0.39228197932243347}, {"X": 0.5851168632507324, "Y": 0.4053501486778259}, {"X": 0.4323655664920807, "Y": 0.404924601316452}]}, "Id": "f02c80f6-6ee1-4962-a132-62780f9778bc", "Relationships": [{"Type": "CHILD", "Ids": ["0078fc06-bc0c-4199-b119-19c1c9c5cc23", "7d4bae39-956c-4863-b398-6d5fbc2dc0ac"]}]}, {"BlockType": "LINE", "Confidence": 99.392333984375, "Text": "1110", "Geometry": {"BoundingBox": {"Width": 0.06288564950227737, "Height": 0.02031942456960678, "Left": 0.8937227129936218, "Top": 0.4221348166465759}, "Polygon": [{"X": 0.8937227129936218, "Y": 0.4221348166465759}, {"X": 0.9565942287445068, "Y": 0.42230433225631714}, {"X": 0.9566083550453186, "Y": 0.4424542486667633}, {"X": 0.8937367796897888, "Y": 0.44229188561439514}]}, "Id": "85073e67-1906-4500-a965-793bcadf00e3", "Relationships": [{"Type": "CHILD", "Ids": ["5ca5372f-45e6-4170-a311-828c6047d69e"]}]}, {"BlockType": "LINE", "Confidence": 61.700416564941406, "Text": "ISK", "Geometry": {"BoundingBox": {"Width": 0.06083182618021965, "Height": 0.027103105559945107, "Left": 0.49607476592063904, "Top": 0.4672451317310333}, "Polygon": [{"X": 0.49607476592063904, "Y": 0.4672451317310333}, {"X": 0.556888222694397, "Y": 0.46739327907562256}, {"X": 0.5569065809249878, "Y": 0.4943482279777527}, {"X": 0.49609312415122986, "Y": 0.49420931935310364}]}, "Id": "e301f9c4-b948-4b4c-8b42-ebeda718211f", "Relationships": [{"Type": "CHILD", "Ids": ["2c016710-850d-47a5-b1fd-1d942b2f4dec"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "0920", "Geometry": {"BoundingBox": {"Width": 0.07250090688467026, "Height": 0.015798991546034813, "Left": 0.9011507034301758, "Top": 0.4685002863407135}, "Polygon": [{"X": 0.9011507034301758, "Y": 0.4685002863407135}, {"X": 0.9736406207084656, "Y": 0.46867677569389343}, {"X": 0.9736515879631042, "Y": 0.48429927229881287}, {"X": 0.9011616110801697, "Y": 0.4841291606426239}]}, "Id": "301ee95b-2dcf-432e-b4ec-e0b08c32149e", "Relationships": [{"Type": "CHILD", "Ids": ["acb7c501-5c8e-4ba4-9760-42d944c54c54"]}]}, {"BlockType": "LINE", "Confidence": 91.32672882080078, "Text": "D", "Geometry": {"BoundingBox": {"Width": 0.0748872384428978, "Height": 0.03442820534110069, "Left": 0.14307034015655518, "Top": 0.4735470414161682}, "Polygon": [{"X": 0.14307034015655518, "Y": 0.4735470414161682}, {"X": 0.2179347276687622, "Y": 0.47372642159461975}, {"X": 0.21795758605003357, "Y": 0.5079752802848816}, {"X": 0.14309309422969818, "Y": 0.5078102946281433}]}, "Id": "6b114c44-c2d9-49ea-b287-8fd4254d97c6", "Relationships": [{"Type": "CHILD", "Ids": ["ff56c074-4251-40a6-bb63-29f83b5f889c"]}]}, {"BlockType": "LINE", "Confidence": 53.952484130859375, "Text": "I", "Geometry": {"BoundingBox": {"Width": 0.01868954673409462, "Height": 0.02958555333316326, "Left": 0.018603550270199776, "Top": 0.4993377923965454}, "Polygon": [{"X": 0.018603550270199776, "Y": 0.4993377923965454}, {"X": 0.037273623049259186, "Y": 0.49937981367111206}, {"X": 0.037293098866939545, "Y": 0.5289233326911926}, {"X": 0.018623003736138344, "Y": 0.5288844704627991}]}, "Id": "0d86aad7-2623-44dc-b3ca-488586820b97", "Relationships": [{"Type": "CHILD", "Ids": ["93a32cc5-af88-4ee4-b2e8-032ec06d5c3f"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Ranger Products", "Geometry": {"BoundingBox": {"Width": 0.16239435970783234, "Height": 0.018141280859708786, "Left": 0.08001936227083206, "Top": 0.5012462139129639}, "Polygon": [{"X": 0.08001936227083206, "Y": 0.5012462139129639}, {"X": 0.2424018383026123, "Y": 0.5016099214553833}, {"X": 0.242413729429245, "Y": 0.5193874835968018}, {"X": 0.0800311267375946, "Y": 0.5190399885177612}]}, "Id": "58f699d6-2150-4950-936b-70f3de419065", "Relationships": [{"Type": "CHILD", "Ids": ["63822cde-ba2a-451f-a146-092e5d1692b9", "c44c7025-748e-4b55-bcd0-baa5639d8c67"]}]}, {"BlockType": "LINE", "Confidence": 95.47791290283203, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.15327268838882446, "Height": 0.014440145343542099, "Left": 0.43216419219970703, "Top": 0.500661313533783}, "Polygon": [{"X": 0.43216419219970703, "Y": 0.500661313533783}, {"X": 0.5854272246360779, "Y": 0.5010057687759399}, {"X": 0.5854368805885315, "Y": 0.515101432800293}, {"X": 0.4321737587451935, "Y": 0.5147691965103149}]}, "Id": "667bec83-463a-4381-a72c-f83bed1a8bfa", "Relationships": [{"Type": "CHILD", "Ids": ["820b0cf0-8bec-438b-9935-a6a8a00d2bdf", "f3a79a6b-738e-490d-bb52-e2509a6b17b3"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "0925", "Geometry": {"BoundingBox": {"Width": 0.08217073976993561, "Height": 0.0181865356862545, "Left": 0.89959317445755, "Top": 0.5201439261436462}, "Polygon": [{"X": 0.89959317445755, "Y": 0.5201439261436462}, {"X": 0.9817513227462769, "Y": 0.5203200578689575}, {"X": 0.9817639589309692, "Y": 0.5383304953575134}, {"X": 0.8996057510375977, "Y": 0.5381627082824707}]}, "Id": "0ef73f28-02c8-4589-8fff-f27611a857f8", "Relationships": [{"Type": "CHILD", "Ids": ["157b5f97-7f33-4c35-b295-3c38342be2c5"]}]}, {"BlockType": "LINE", "Confidence": 88.05464172363281, "Text": "Number", "Geometry": {"BoundingBox": {"Width": 0.09842003136873245, "Height": 0.02030169405043125, "Left": 0.10205579549074173, "Top": 0.5365812182426453}, "Polygon": [{"X": 0.10205579549074173, "Y": 0.5365812182426453}, {"X": 0.2004624307155609, "Y": 0.5367821455001831}, {"X": 0.20047582685947418, "Y": 0.556882917881012}, {"X": 0.10206911712884903, "Y": 0.5566931366920471}]}, "Id": "a6865cba-9845-4d46-a57f-4f63bdd123e1", "Relationships": [{"Type": "CHILD", "Ids": ["2ec46945-0abc-4c69-b523-5b25dbfc6f67"]}]}, {"BlockType": "LINE", "Confidence": 89.17888641357422, "Text": "Received", "Geometry": {"BoundingBox": {"Width": 0.12128117680549622, "Height": 0.02038852497935295, "Left": 0.09971673041582108, "Top": 0.5894327759742737}, "Polygon": [{"X": 0.09971673041582108, "Y": 0.5894327759742737}, {"X": 0.22098444402217865, "Y": 0.5896443724632263}, {"X": 0.2209979146718979, "Y": 0.6098212599754333}, {"X": 0.09973010420799255, "Y": 0.6096234321594238}]}, "Id": "fc101c49-da02-443d-b4a2-9403b1a2721c", "Relationships": [{"Type": "CHILD", "Ids": ["889c20fd-ecf6-4d1a-81ee-0417bcfbfc4d"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Detroit", "Geometry": {"BoundingBox": {"Width": 0.0661182627081871, "Height": 0.013595602475106716, "Left": 0.49973756074905396, "Top": 0.9587586522102356}, "Polygon": [{"X": 0.49973756074905396, "Y": 0.9587804079055786}, {"X": 0.5658465623855591, "Y": 0.9587586522102356}, {"X": 0.5658558011054993, "Y": 0.9723274111747742}, {"X": 0.49974679946899414, "Y": 0.9723542332649231}]}, "Id": "269b0f79-253a-4bbb-8237-a9936fc3bd93", "Relationships": [{"Type": "CHILD", "Ids": ["47325101-2a03-4516-b464-253e62ce4bad"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06306497007608414, "Height": 0.014264742843806744, "Left": 0.2410423904657364, "Top": 0.056620508432388306}, "Polygon": [{"X": 0.2410423904657364, "Y": 0.056620508432388306}, {"X": 0.304097980260849, "Y": 0.05691937729716301}, {"X": 0.3041073679924011, "Y": 0.07088524848222733}, {"X": 0.24105173349380493, "Y": 0.07059133052825928}]}, "Id": "88264f10-3ef9-4a57-abdd-3e0b9d6b49bf"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "16-Jun", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06518691033124924, "Height": 0.01432514376938343, "Left": 0.32997673749923706, "Top": 0.057563357055187225}, "Polygon": [{"X": 0.32997673749923706, "Y": 0.057563357055187225}, {"X": 0.3951541781425476, "Y": 0.057872094213962555}, {"X": 0.3951636254787445, "Y": 0.0718884989619255}, {"X": 0.3299861550331116, "Y": 0.07158490270376205}]}, "Id": "ffb5c5a1-0e78-4306-9115-f0fa50db78ee"}, {"BlockType": "WORD", "Confidence": 98.61985778808594, "Text": "Metal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13313019275665283, "Height": 0.029575800523161888, "Left": 0.4547988772392273, "Top": 0.04649011418223381}, "Polygon": [{"X": 0.4547988772392273, "Y": 0.04649011418223381}, {"X": 0.5879092812538147, "Y": 0.047129370272159576}, {"X": 0.5879290699958801, "Y": 0.07606591284275055}, {"X": 0.4548185169696808, "Y": 0.07544833421707153}]}, "Id": "d22052aa-a8a0-4234-b516-f9e1c7cf0586"}, {"BlockType": "WORD", "Confidence": 99.423828125, "Text": "Finishing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2208331972360611, "Height": 0.04086576774716377, "Left": 0.598693311214447, "Top": 0.04651004448533058}, "Polygon": [{"X": 0.598693311214447, "Y": 0.04651004448533058}, {"X": 0.8194988369941711, "Y": 0.04757129028439522}, {"X": 0.8195264935493469, "Y": 0.08737581223249435}, {"X": 0.5987206101417542, "Y": 0.08636410534381866}]}, "Id": "5d4d07e7-9df8-4d9e-b494-9bcbd466dfcf"}, {"BlockType": "WORD", "Confidence": 99.63787078857422, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0550232008099556, "Height": 0.014172817580401897, "Left": 0.22230124473571777, "Top": 0.09536857157945633}, "Polygon": [{"X": 0.22230124473571777, "Y": 0.09536857157945633}, {"X": 0.2773151099681854, "Y": 0.09561731666326523}, {"X": 0.2773244380950928, "Y": 0.1095413863658905}, {"X": 0.22231054306030273, "Y": 0.10929694026708603}]}, "Id": "c2eb5a13-f2f2-4bff-9fed-fe7d1dd0fa89"}, {"BlockType": "WORD", "Confidence": 99.482421875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023738034069538116, "Height": 0.013923609629273415, "Left": 0.2802562117576599, "Top": 0.09558170288801193}, "Polygon": [{"X": 0.2802562117576599, "Y": 0.09558170288801193}, {"X": 0.30398496985435486, "Y": 0.09568899869918823}, {"X": 0.30399423837661743, "Y": 0.1095053106546402}, {"X": 0.2802654802799225, "Y": 0.10939985513687134}]}, "Id": "d93b91ad-6160-4550-8f07-5c70d25f2a0a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "6063/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1311330497264862, "Height": 0.016931181773543358, "Left": 0.328524649143219, "Top": 0.09573021531105042}, "Polygon": [{"X": 0.328524649143219, "Y": 0.09573021531105042}, {"X": 0.459646612405777, "Y": 0.09632318466901779}, {"X": 0.4596576690673828, "Y": 0.11266139149665833}, {"X": 0.32853564620018005, "Y": 0.11208047717809677}]}, "Id": "f1bf640a-7b8e-4adf-bcd0-93acd3a7f1ba"}, {"BlockType": "WORD", "Confidence": 78.73226165771484, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12082139402627945, "Height": 0.02164120227098465, "Left": 0.5329952239990234, "Top": 0.09338319301605225}, "Polygon": [{"X": 0.5329952239990234, "Y": 0.09338319301605225}, {"X": 0.6538021564483643, "Y": 0.09393174201250076}, {"X": 0.6538166403770447, "Y": 0.1150243952870369}, {"X": 0.5330096483230591, "Y": 0.11449019610881805}]}, "Id": "d65feff6-916e-4586-a9f2-05b0d0ba33f0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054961513727903366, "Height": 0.014361806213855743, "Left": 0.4778715670108795, "Top": 0.11589861661195755}, "Polygon": [{"X": 0.4778715670108795, "Y": 0.11589861661195755}, {"X": 0.5328234434127808, "Y": 0.11614110320806503}, {"X": 0.5328330993652344, "Y": 0.1302604228258133}, {"X": 0.47788116335868835, "Y": 0.1300223171710968}]}, "Id": "c29a0230-4379-491c-8926-51ac9a1c8daf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06326800584793091, "Height": 0.0175386443734169, "Left": 0.5371868014335632, "Top": 0.11615411192178726}, "Polygon": [{"X": 0.5371868014335632, "Y": 0.11615411192178726}, {"X": 0.6004430055618286, "Y": 0.11643324047327042}, {"X": 0.6004548072814941, "Y": 0.13369275629520416}, {"X": 0.5371986031532288, "Y": 0.13341978192329407}]}, "Id": "9869ce92-861b-4c10-8137-0859a764de71"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05460979789495468, "Height": 0.013918382115662098, "Left": 0.606269359588623, "Top": 0.11698733270168304}, "Polygon": [{"X": 0.606269359588623, "Y": 0.11698733270168304}, {"X": 0.6608697772026062, "Y": 0.11722810566425323}, {"X": 0.6608791947364807, "Y": 0.13090571761131287}, {"X": 0.6062787175178528, "Y": 0.13066914677619934}]}, "Id": "c7d3dc5d-bdd5-44c6-990b-5959e71982a4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08578747510910034, "Height": 0.014140261337161064, "Left": 0.21138809621334076, "Top": 0.13415104150772095}, "Polygon": [{"X": 0.21138809621334076, "Y": 0.13415104150772095}, {"X": 0.2971663177013397, "Y": 0.13452018797397614}, {"X": 0.2971755564212799, "Y": 0.14829130470752716}, {"X": 0.21139727532863617, "Y": 0.14792880415916443}]}, "Id": "e6f20dc5-6407-4ee6-9d41-4a7f1287a43f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06762655079364777, "Height": 0.013936483301222324, "Left": 0.32919734716415405, "Top": 0.1344699114561081}, "Polygon": [{"X": 0.32919734716415405, "Y": 0.1344699114561081}, {"X": 0.3968146741390228, "Y": 0.13476097583770752}, {"X": 0.396823912858963, "Y": 0.14840640127658844}, {"X": 0.32920652627944946, "Y": 0.14812052249908447}]}, "Id": "20514297-3ba2-415d-9bff-e5012106ac79"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054722610861063004, "Height": 0.014246786013245583, "Left": 0.47829118371009827, "Top": 0.15436407923698425}, "Polygon": [{"X": 0.47829118371009827, "Y": 0.15436407923698425}, {"X": 0.5330042243003845, "Y": 0.15459366142749786}, {"X": 0.5330138206481934, "Y": 0.1686108559370041}, {"X": 0.47830069065093994, "Y": 0.1683855801820755}]}, "Id": "663dd8b0-1da1-4ed4-b356-99296104a9e4"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10634840279817581, "Height": 0.0170254185795784, "Left": 0.5377272367477417, "Top": 0.1548418551683426}, "Polygon": [{"X": 0.5377272367477417, "Y": 0.1548418551683426}, {"X": 0.6440642476081848, "Y": 0.1552879363298416}, {"X": 0.6440756320953369, "Y": 0.1718672811985016}, {"X": 0.537738561630249, "Y": 0.17143112421035767}]}, "Id": "edff4dc8-2720-474e-92f4-60b46e82fc33"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12694238126277924, "Height": 0.015209810808300972, "Left": 0.10690374672412872, "Top": 0.17184032499790192}, "Polygon": [{"X": 0.10690374672412872, "Y": 0.17184032499790192}, {"X": 0.2338363081216812, "Y": 0.1723594069480896}, {"X": 0.23384612798690796, "Y": 0.18705014884471893}, {"X": 0.1069134846329689, "Y": 0.18654154241085052}]}, "Id": "8339718e-bea2-4481-9f05-ab1b125869bd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05687609314918518, "Height": 0.013824240304529667, "Left": 0.2397363930940628, "Top": 0.17291611433029175}, "Polygon": [{"X": 0.2397363930940628, "Y": 0.17291611433029175}, {"X": 0.29660338163375854, "Y": 0.1731484979391098}, {"X": 0.2966125011444092, "Y": 0.18674035370349884}, {"X": 0.23974548280239105, "Y": 0.1865123212337494}]}, "Id": "79f99c06-1f8a-478b-a9d3-6634076df462"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04241609573364258, "Height": 0.01403665728867054, "Left": 0.3287319242954254, "Top": 0.17282900214195251}, "Polygon": [{"X": 0.3287319242954254, "Y": 0.17282900214195251}, {"X": 0.37113866209983826, "Y": 0.17300240695476532}, {"X": 0.371148020029068, "Y": 0.1868656575679779}, {"X": 0.32874125242233276, "Y": 0.18669557571411133}]}, "Id": "62e597e4-ff5b-498a-a881-a9b5cda7334e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PICK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04759252443909645, "Height": 0.012118658982217312, "Left": 0.34071293473243713, "Top": 0.2065531313419342}, "Polygon": [{"X": 0.34071293473243713, "Y": 0.2065531313419342}, {"X": 0.3882974088191986, "Y": 0.2067386955022812}, {"X": 0.3883054554462433, "Y": 0.2186717987060547}, {"X": 0.3407209813594818, "Y": 0.21848942339420319}]}, "Id": "f7f39ed1-8dfb-402e-9271-af7693a67987"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02702987566590309, "Height": 0.013917472213506699, "Left": 0.35068249702453613, "Top": 0.22607578337192535}, "Polygon": [{"X": 0.35068249702453613, "Y": 0.22607578337192535}, {"X": 0.3777030408382416, "Y": 0.22617819905281067}, {"X": 0.3777123689651489, "Y": 0.23999325931072235}, {"X": 0.3506917953491211, "Y": 0.23989294469356537}]}, "Id": "4837bfd5-2fbb-4ffe-91bb-d332029cd7d1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039036087691783905, "Height": 0.013744252733886242, "Left": 0.4185316562652588, "Top": 0.22663474082946777}, "Polygon": [{"X": 0.4185316562652588, "Y": 0.22663474082946777}, {"X": 0.4575585126876831, "Y": 0.22678259015083313}, {"X": 0.4575677514076233, "Y": 0.2403789907693863}, {"X": 0.4185408651828766, "Y": 0.2402341216802597}]}, "Id": "cf83032c-c148-4cf9-adb1-1c534681d2ae"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05175027623772621, "Height": 0.013383576646447182, "Left": 0.8886743783950806, "Top": 0.2214907556772232}, "Polygon": [{"X": 0.8886743783950806, "Y": 0.2214907556772232}, {"X": 0.9404154419898987, "Y": 0.22168879210948944}, {"X": 0.9404246807098389, "Y": 0.23487433791160583}, {"X": 0.888683557510376, "Y": 0.2346801459789276}]}, "Id": "c912eade-c883-462c-94f6-a811bb8179d9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05237412080168724, "Height": 0.0130130834877491, "Left": 0.9440542459487915, "Top": 0.22159919142723083}, "Polygon": [{"X": 0.9440542459487915, "Y": 0.22159919142723083}, {"X": 0.996419370174408, "Y": 0.22179964184761047}, {"X": 0.996428370475769, "Y": 0.23461227118968964}, {"X": 0.9440632462501526, "Y": 0.23441560566425323}]}, "Id": "b7ba118b-280a-48b4-a5c9-af046abc74cc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11353284120559692, "Height": 0.01488695852458477, "Left": 0.14350344240665436, "Top": 0.22728955745697021}, "Polygon": [{"X": 0.14350344240665436, "Y": 0.22728955745697021}, {"X": 0.2570266127586365, "Y": 0.2277185320854187}, {"X": 0.2570362985134125, "Y": 0.24217650294303894}, {"X": 0.1435130536556244, "Y": 0.24175673723220825}]}, "Id": "efa2dbf7-f2c2-4d4f-bc18-cca035c2203a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06397752463817596, "Height": 0.013774982653558254, "Left": 0.5946465730667114, "Top": 0.2282390594482422}, "Polygon": [{"X": 0.5946465730667114, "Y": 0.2282390594482422}, {"X": 0.6586147546768188, "Y": 0.22848105430603027}, {"X": 0.6586240530014038, "Y": 0.2420140504837036}, {"X": 0.5946558117866516, "Y": 0.24177691340446472}]}, "Id": "bec07f8d-0c9a-4e8d-8591-062c411bbbe7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07157399505376816, "Height": 0.013838260434567928, "Left": 0.6646826267242432, "Top": 0.22842003405094147}, "Polygon": [{"X": 0.6646826267242432, "Y": 0.22842003405094147}, {"X": 0.7362472414970398, "Y": 0.22869080305099487}, {"X": 0.7362566590309143, "Y": 0.24225829541683197}, {"X": 0.6646919846534729, "Y": 0.2419929951429367}]}, "Id": "d95388ad-4317-479b-b7c5-06a28c40277b"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014006056822836399, "Height": 0.013430176302790642, "Left": 0.7428342700004578, "Top": 0.2286016196012497}, "Polygon": [{"X": 0.7428342700004578, "Y": 0.2286016196012497}, {"X": 0.7568311095237732, "Y": 0.22865459322929382}, {"X": 0.7568403482437134, "Y": 0.24203179776668549}, {"X": 0.742843508720398, "Y": 0.24197988212108612}]}, "Id": "fbca7029-ef04-41e9-bab9-93acbefd59e3"}, {"BlockType": "WORD", "Confidence": 94.54081726074219, "Text": "ISK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06304258108139038, "Height": 0.026615964248776436, "Left": 0.4711299538612366, "Top": 0.2555736005306244}, "Polygon": [{"X": 0.4711299538612366, "Y": 0.2555736005306244}, {"X": 0.5341545343399048, "Y": 0.255802184343338}, {"X": 0.534172534942627, "Y": 0.2821895480155945}, {"X": 0.4711478650569916, "Y": 0.281970351934433}]}, "Id": "d746c21e-d155-4723-8aea-e4fc200ef6e1"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "1020", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07413194328546524, "Height": 0.01809423230588436, "Left": 0.8994511961936951, "Top": 0.25593405961990356}, "Polygon": [{"X": 0.8994511961936951, "Y": 0.25593405961990356}, {"X": 0.9735706448554993, "Y": 0.25620338320732117}, {"X": 0.9735831618309021, "Y": 0.27402830123901367}, {"X": 0.8994636535644531, "Y": 0.2737664580345154}]}, "Id": "22faaff9-4811-4cf1-a1aa-63644f48cfee"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "2", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03494357317686081, "Height": 0.0365309901535511, "Left": 0.012326158583164215, "Top": 0.26877906918525696}, "Polygon": [{"X": 0.012326158583164215, "Y": 0.26877906918525696}, {"X": 0.04724571481347084, "Y": 0.26890280842781067}, {"X": 0.047269731760025024, "Y": 0.30531007051467896}, {"X": 0.01235012337565422, "Y": 0.3051934838294983}]}, "Id": "93dd08f0-624b-4ee5-96db-781ca78934cf"}, {"BlockType": "WORD", "Confidence": 55.59450912475586, "Text": "1sm", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08360423147678375, "Height": 0.029632816091179848, "Left": 0.09863375127315521, "Top": 0.263430118560791}, "Polygon": [{"X": 0.09863375127315521, "Y": 0.263430118560791}, {"X": 0.18221844732761383, "Y": 0.26372894644737244}, {"X": 0.18223798274993896, "Y": 0.2930629253387451}, {"X": 0.09865318238735199, "Y": 0.2927778959274292}]}, "Id": "d4ad37ef-60ec-4173-8702-fe9ca3e8f613"}, {"BlockType": "WORD", "Confidence": 97.52291870117188, "Text": "Box", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07984447479248047, "Height": 0.02683817595243454, "Left": 0.19626189768314362, "Top": 0.2651192247867584}, "Polygon": [{"X": 0.19626189768314362, "Y": 0.2651192247867584}, {"X": 0.27608856558799744, "Y": 0.26540398597717285}, {"X": 0.2761063575744629, "Y": 0.29195737838745117}, {"X": 0.1962796002626419, "Y": 0.29168450832366943}]}, "Id": "9150cafc-9e12-4a11-8a0f-211fa2cb679f"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Automation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11445844918489456, "Height": 0.01437823660671711, "Left": 0.07607028633356094, "Top": 0.28768137097358704}, "Polygon": [{"X": 0.07607028633356094, "Y": 0.28768137097358704}, {"X": 0.19051942229270935, "Y": 0.28807491064071655}, {"X": 0.1905287355184555, "Y": 0.3020596206188202}, {"X": 0.07607953995466232, "Y": 0.30167508125305176}]}, "Id": "1318c2cc-4470-40eb-b3e8-46242f3dd2d3"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016658298671245575, "Height": 0.01389613002538681, "Left": 0.1974191516637802, "Top": 0.28755438327789307}, "Polygon": [{"X": 0.1974191516637802, "Y": 0.28755438327789307}, {"X": 0.214068204164505, "Y": 0.2876116931438446}, {"X": 0.2140774428844452, "Y": 0.3014505207538605}, {"X": 0.1974283754825592, "Y": 0.301394522190094}]}, "Id": "961f4d55-718b-46ce-b262-bfa486ac89a2"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "Modular", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07967866957187653, "Height": 0.014451124705374241, "Left": 0.22007548809051514, "Top": 0.28682491183280945}, "Polygon": [{"X": 0.22007548809051514, "Y": 0.28682491183280945}, {"X": 0.2997446358203888, "Y": 0.2870994508266449}, {"X": 0.29975417256355286, "Y": 0.3012760579586029}, {"X": 0.22008496522903442, "Y": 0.30100783705711365}]}, "Id": "508fe631-584f-4672-ad83-270930e2c707"}, {"BlockType": "WORD", "Confidence": 97.45296478271484, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012313039042055607, "Height": 0.011039203964173794, "Left": 0.3577520251274109, "Top": 0.2902805209159851}, "Polygon": [{"X": 0.3577520251274109, "Y": 0.2902805209159851}, {"X": 0.3700576424598694, "Y": 0.2903227210044861}, {"X": 0.3700650632381439, "Y": 0.3013197183609009}, {"X": 0.35775941610336304, "Y": 0.301278293132782}]}, "Id": "f2dd4566-ded0-4cf7-a80d-f1b381999ba5"}, {"BlockType": "WORD", "Confidence": 94.6619873046875, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012255001813173294, "Height": 0.01109025627374649, "Left": 0.4314466714859009, "Top": 0.2907411754131317}, "Polygon": [{"X": 0.4314466714859009, "Y": 0.2907411754131317}, {"X": 0.44369417428970337, "Y": 0.290783166885376}, {"X": 0.4437016546726227, "Y": 0.3018314242362976}, {"X": 0.4314541518688202, "Y": 0.3017902076244354}]}, "Id": "990607f2-dfab-4fa2-8fe8-ffca43cfaeb6"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11055707931518555, "Height": 0.01222818810492754, "Left": 0.47477394342422485, "Top": 0.2889443039894104}, "Polygon": [{"X": 0.47477394342422485, "Y": 0.2889443039894104}, {"X": 0.585322916507721, "Y": 0.28932449221611023}, {"X": 0.5853310227394104, "Y": 0.30117249488830566}, {"X": 0.47478199005126953, "Y": 0.300799697637558}]}, "Id": "fda10214-5616-4eaa-987d-f2ace2f7a3a2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "1030", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07263769209384918, "Height": 0.019073421135544777, "Left": 0.8952206969261169, "Top": 0.3086911737918854}, "Polygon": [{"X": 0.8952206969261169, "Y": 0.3086911737918854}, {"X": 0.9678452014923096, "Y": 0.30893343687057495}, {"X": 0.9678583741188049, "Y": 0.3277646005153656}, {"X": 0.8952338695526123, "Y": 0.32753005623817444}]}, "Id": "f39233a3-eb88-4043-b215-a517c9336a98"}, {"BlockType": "WORD", "Confidence": 78.18897247314453, "Text": "Ism", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06915627419948578, "Height": 0.041588135063648224, "Left": 0.10696327686309814, "Top": 0.3491291403770447}, "Polygon": [{"X": 0.10696327686309814, "Y": 0.3491291403770447}, {"X": 0.1760920137166977, "Y": 0.34934303164482117}, {"X": 0.17611955106258392, "Y": 0.3907172977924347}, {"X": 0.10699068754911423, "Y": 0.39051946997642517}]}, "Id": "c0ad3d23-1640-423d-a5b4-2f6ad786082e"}, {"BlockType": "WORD", "Confidence": 54.47465133666992, "Text": "SR", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05944705754518509, "Height": 0.03462570160627365, "Left": 0.18803462386131287, "Top": 0.3561254143714905}, "Polygon": [{"X": 0.18803462386131287, "Y": 0.3561254143714905}, {"X": 0.24745865166187286, "Y": 0.3563070297241211}, {"X": 0.24748168885707855, "Y": 0.3907511234283447}, {"X": 0.1880575716495514, "Y": 0.39058101177215576}]}, "Id": "281e15a5-df65-4e3e-920b-5674851bb71f"}, {"BlockType": "WORD", "Confidence": 42.938255310058594, "Text": "Isk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06696676462888718, "Height": 0.03149253502488136, "Left": 0.4840385615825653, "Top": 0.35507461428642273}, "Polygon": [{"X": 0.4840385615825653, "Y": 0.35507461428642273}, {"X": 0.5509839653968811, "Y": 0.35527995228767395}, {"X": 0.5510053038597107, "Y": 0.3865671455860138}, {"X": 0.4840598404407501, "Y": 0.3863736093044281}]}, "Id": "14c885a9-8b64-42b3-9cd7-32954a26179f"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "1105", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06733477860689163, "Height": 0.020313508808612823, "Left": 0.897701621055603, "Top": 0.36111822724342346}, "Polygon": [{"X": 0.897701621055603, "Y": 0.36111822724342346}, {"X": 0.9650223255157471, "Y": 0.36132287979125977}, {"X": 0.9650363922119141, "Y": 0.3814317286014557}, {"X": 0.89771568775177, "Y": 0.38123467564582825}]}, "Id": "73c32551-95c9-4f4c-bfef-2b912b8a8a7c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "3", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.029292328283190727, "Height": 0.03867295756936073, "Left": 0.015774134546518326, "Top": 0.37743908166885376}, "Polygon": [{"X": 0.015774134546518326, "Y": 0.37743908166885376}, {"X": 0.04504100978374481, "Y": 0.3775249421596527}, {"X": 0.045066460967063904, "Y": 0.4161120355129242}, {"X": 0.015799539163708687, "Y": 0.4160325229167938}]}, "Id": "0547605e-ced8-4818-a605-7f4784ae99da"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08305372297763824, "Height": 0.014071094803512096, "Left": 0.07752134650945663, "Top": 0.3907388150691986}, "Polygon": [{"X": 0.07752134650945663, "Y": 0.3907388150691986}, {"X": 0.16056586802005768, "Y": 0.39097633957862854}, {"X": 0.16057507693767548, "Y": 0.4048099219799042}, {"X": 0.07753049582242966, "Y": 0.4045788645744324}]}, "Id": "18f50383-c7b9-4449-8de9-6de82a092abf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gunsight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08847473561763763, "Height": 0.017688628286123276, "Left": 0.1668851524591446, "Top": 0.3903546631336212}, "Polygon": [{"X": 0.1668851524591446, "Y": 0.3903546631336212}, {"X": 0.25534823536872864, "Y": 0.3906079828739166}, {"X": 0.2553598880767822, "Y": 0.4080432653427124}, {"X": 0.1668967604637146, "Y": 0.4077986180782318}]}, "Id": "2a77d0bd-4fcd-4dcb-9d09-6745d935caaf"}, {"BlockType": "WORD", "Confidence": 93.89568328857422, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012405311688780785, "Height": 0.01091103907674551, "Left": 0.3587411046028137, "Top": 0.3937020003795624}, "Polygon": [{"X": 0.3587411046028137, "Y": 0.3937020003795624}, {"X": 0.3711390793323517, "Y": 0.3937372863292694}, {"X": 0.37114641070365906, "Y": 0.40461301803588867}, {"X": 0.3587484359741211, "Y": 0.40457847714424133}]}, "Id": "d4fc24a5-5d9d-4267-a997-87d2c09deb0b"}, {"BlockType": "WORD", "Confidence": 96.62886810302734, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012235797010362148, "Height": 0.010933266021311283, "Left": 0.4323582053184509, "Top": 0.39402541518211365}, "Polygon": [{"X": 0.4323582053184509, "Y": 0.39402541518211365}, {"X": 0.4445866048336029, "Y": 0.3940602242946625}, {"X": 0.44459399580955505, "Y": 0.40495866537094116}, {"X": 0.4323655664920807, "Y": 0.404924601316452}]}, "Id": "0078fc06-bc0c-4199-b119-19c1c9c5cc23"}, {"BlockType": "WORD", "Confidence": 99.23290252685547, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10945416241884232, "Height": 0.012276295572519302, "Left": 0.475661963224411, "Top": 0.3919690251350403}, "Polygon": [{"X": 0.475661963224411, "Y": 0.3919690251350403}, {"X": 0.5851079225540161, "Y": 0.39228197932243347}, {"X": 0.5851161479949951, "Y": 0.4042453169822693}, {"X": 0.47567006945610046, "Y": 0.40393972396850586}]}, "Id": "7d4bae39-956c-4863-b398-6d5fbc2dc0ac"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "1110", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06288564950227737, "Height": 0.02031942456960678, "Left": 0.8937227129936218, "Top": 0.4221348166465759}, "Polygon": [{"X": 0.8937227129936218, "Y": 0.4221348166465759}, {"X": 0.9565942287445068, "Y": 0.42230433225631714}, {"X": 0.9566083550453186, "Y": 0.4424542486667633}, {"X": 0.8937367796897888, "Y": 0.44229188561439514}]}, "Id": "5ca5372f-45e6-4170-a311-828c6047d69e"}, {"BlockType": "WORD", "Confidence": 61.700416564941406, "Text": "ISK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06083182618021965, "Height": 0.027103105559945107, "Left": 0.49607476592063904, "Top": 0.4672451317310333}, "Polygon": [{"X": 0.49607476592063904, "Y": 0.4672451317310333}, {"X": 0.556888222694397, "Y": 0.46739327907562256}, {"X": 0.5569065809249878, "Y": 0.4943482279777527}, {"X": 0.49609312415122986, "Y": 0.49420931935310364}]}, "Id": "2c016710-850d-47a5-b1fd-1d942b2f4dec"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "0920", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07250090688467026, "Height": 0.015798991546034813, "Left": 0.9011507034301758, "Top": 0.4685002863407135}, "Polygon": [{"X": 0.9011507034301758, "Y": 0.4685002863407135}, {"X": 0.9736406207084656, "Y": 0.46867677569389343}, {"X": 0.9736515879631042, "Y": 0.48429927229881287}, {"X": 0.9011616110801697, "Y": 0.4841291606426239}]}, "Id": "acb7c501-5c8e-4ba4-9760-42d944c54c54"}, {"BlockType": "WORD", "Confidence": 91.32672882080078, "Text": "D", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0748872384428978, "Height": 0.03442820534110069, "Left": 0.14307034015655518, "Top": 0.4735470414161682}, "Polygon": [{"X": 0.14307034015655518, "Y": 0.4735470414161682}, {"X": 0.2179347276687622, "Y": 0.47372642159461975}, {"X": 0.21795758605003357, "Y": 0.5079752802848816}, {"X": 0.14309309422969818, "Y": 0.5078102946281433}]}, "Id": "ff56c074-4251-40a6-bb63-29f83b5f889c"}, {"BlockType": "WORD", "Confidence": 53.952484130859375, "Text": "I", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.01868954673409462, "Height": 0.02958555333316326, "Left": 0.018603550270199776, "Top": 0.4993377923965454}, "Polygon": [{"X": 0.018603550270199776, "Y": 0.4993377923965454}, {"X": 0.037273623049259186, "Y": 0.49937981367111206}, {"X": 0.037293098866939545, "Y": 0.5289233326911926}, {"X": 0.018623003736138344, "Y": 0.5288844704627991}]}, "Id": "93a32cc5-af88-4ee4-b2e8-032ec06d5c3f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07123996317386627, "Height": 0.017066454514861107, "Left": 0.08001994341611862, "Top": 0.5021259784698486}, "Polygon": [{"X": 0.08001994341611862, "Y": 0.5021259784698486}, {"X": 0.15124867856502533, "Y": 0.5022851228713989}, {"X": 0.1512598991394043, "Y": 0.5191923975944519}, {"X": 0.0800311267375946, "Y": 0.5190399885177612}]}, "Id": "63822cde-ba2a-451f-a146-092e5d1692b9"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Products", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08344366401433945, "Height": 0.01432795636355877, "Left": 0.15896762907505035, "Top": 0.5014230012893677}, "Polygon": [{"X": 0.15896762907505035, "Y": 0.5014230012893677}, {"X": 0.2424018383026123, "Y": 0.5016099214553833}, {"X": 0.2424112856388092, "Y": 0.5157510042190552}, {"X": 0.15897703170776367, "Y": 0.5155707597732544}]}, "Id": "c44c7025-748e-4b55-bcd0-baa5639d8c67"}, {"BlockType": "WORD", "Confidence": 91.60335540771484, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014586550183594227, "Height": 0.014139457605779171, "Left": 0.43216419219970703, "Top": 0.500661313533783}, "Polygon": [{"X": 0.43216419219970703, "Y": 0.500661313533783}, {"X": 0.4467411935329437, "Y": 0.5006940960884094}, {"X": 0.4467507302761078, "Y": 0.5148007869720459}, {"X": 0.4321737587451935, "Y": 0.5147691965103149}]}, "Id": "820b0cf0-8bec-438b-9935-a6a8a00d2bdf"}, {"BlockType": "WORD", "Confidence": 99.35247802734375, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10901995003223419, "Height": 0.01177925430238247, "Left": 0.4764157235622406, "Top": 0.5015275478363037}, "Polygon": [{"X": 0.4764157235622406, "Y": 0.5015275478363037}, {"X": 0.5854277610778809, "Y": 0.5017720460891724}, {"X": 0.585435688495636, "Y": 0.5133067965507507}, {"X": 0.47642356157302856, "Y": 0.5130693316459656}]}, "Id": "f3a79a6b-738e-490d-bb52-e2509a6b17b3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "0925", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08217073976993561, "Height": 0.0181865356862545, "Left": 0.89959317445755, "Top": 0.5201439261436462}, "Polygon": [{"X": 0.89959317445755, "Y": 0.5201439261436462}, {"X": 0.9817513227462769, "Y": 0.5203200578689575}, {"X": 0.9817639589309692, "Y": 0.5383304953575134}, {"X": 0.8996057510375977, "Y": 0.5381627082824707}]}, "Id": "157b5f97-7f33-4c35-b295-3c38342be2c5"}, {"BlockType": "WORD", "Confidence": 88.05464172363281, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09842003136873245, "Height": 0.02030169405043125, "Left": 0.10205579549074173, "Top": 0.5365812182426453}, "Polygon": [{"X": 0.10205579549074173, "Y": 0.5365812182426453}, {"X": 0.2004624307155609, "Y": 0.5367821455001831}, {"X": 0.20047582685947418, "Y": 0.556882917881012}, {"X": 0.10206911712884903, "Y": 0.5566931366920471}]}, "Id": "2ec46945-0abc-4c69-b523-5b25dbfc6f67"}, {"BlockType": "WORD", "Confidence": 89.17888641357422, "Text": "Received", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12128117680549622, "Height": 0.02038852497935295, "Left": 0.09971673041582108, "Top": 0.5894327759742737}, "Polygon": [{"X": 0.09971673041582108, "Y": 0.5894327759742737}, {"X": 0.22098444402217865, "Y": 0.5896443724632263}, {"X": 0.2209979146718979, "Y": 0.6098212599754333}, {"X": 0.09973010420799255, "Y": 0.6096234321594238}]}, "Id": "889c20fd-ecf6-4d1a-81ee-0417bcfbfc4d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Detroit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0661182627081871, "Height": 0.013595602475106716, "Left": 0.49973756074905396, "Top": 0.9587586522102356}, "Polygon": [{"X": 0.49973756074905396, "Y": 0.9587804079055786}, {"X": 0.5658465623855591, "Y": 0.9587586522102356}, {"X": 0.5658558011054993, "Y": 0.9723274111747742}, {"X": 0.49974679946899414, "Y": 0.9723542332649231}]}, "Id": "47325101-2a03-4516-b464-253e62ce4bad"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "b5d68f0a-f0ef-469f-864a-d077fe442eef", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "b5d68f0a-f0ef-469f-864a-d077fe442eef", "content-type": "application/x-amz-json-1.1", "content-length": "52093", "date": "<PERSON><PERSON>, 23 Sep 2025 18:38:20 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "1a8bb393-1f8d-4b1c-a778-7308f69b2c5b", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "<PERSON><PERSON>, 23 Sep 2025 18:38:24 GMT", "content-type": "application/json", "content-length": "2210", "connection": "keep-alive", "x-amzn-requestid": "1a8bb393-1f8d-4b1c-a778-7308f69b2c5b"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. We have only page1. The content seems like a work order or loading list? It says \"WORK ORDER #\", \"***********\" etc. Likely a loading dock sheet? Could be a \"work order\" used for loading. Not exactly any of the defined types. It might be a \"log\" of docking times. The title says \"DATE: 16-Jun Metal Finishing UNIT #: 6063/283420 Receipt\". Possibly an \"ingate\" or \"outgate\"? It lists truck ready time, driver, departure time, pick up, dock time. Might be an \"ingate\" or \"outgate\" document? The fields: Truck Ready Time, Driver, Departure Time, Pick Up, Dock Time. Outgate typically has departure, exit time. Ingate would have arrival. But this seems like a schedule? Might be \"ingate\" or \"outgate\"? But no explicit header \"Ingate\" or \"Outgate\". However it includes \"Truck Departure\" and \"Departure Time\". That indicates it might be an \"outgate\" record. But \"Outgate\" usually has \"Outgate\" header and \"Gate out\", \"Release stamp\". Here we have just times. Could be a \"work order\" sheet used by loading dock. Not one of the categories. So classify as \"other\". But check if any category matches: \"log\" might fit: a log of dock times. \"Log\" includes driver log, logbook, hours of service, activity, timestamp, driver/vehicle IDs. This page has driver and times. Could be a \"log\". But the term \"log\" is generic; but likely it's a \"log\" page. The description: \"1. Activity record (driver log, tracking log) with dock time for operational/regulatory use\". Indeed this page lists \"Truck Ready Time\", \"Driver\", \"Departure Time\", \"Dock Time\". So yes, it's a log. So we classify as \"log\". There's no continuation or previous page. So output one object.\n\nWe need to use the tool classify_logistics_doc_type. The input expects documents: array of objects with page_no and doc_type. So for page 1, doc_type \"log\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"log\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2408, "outputTokens": 479, "totalTokens": 2887}, "metrics": {"latencyMs": 2272}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/e58fa179_DTA5S55B66Z5U1H1GDK5.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination, keywords indication and key fields/structure are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nDATE: 16-Jun Metal Finishing\nUNIT #: 6063/283420 Receipt\nTruck Ready Time:\nDRIVER: KARRA\nTruck Departure:\nDEPARTURE TIME: 5am\nPICK\nUP DEL\nDock Time\nCUSTOMER\nWORK ORDER #\nISK\n1020\n2\n1sm Box\nAutomation & Modular\nX\nX ***********\n1030\nIsm SR\nIsk\n1105\n3\nWilliams Gunsight\nX\nX ***********\n1110\nISK\n0920\nD\nI\nRanger Products\nX ***********\n0925\nNumber\nReceived\nDetroit\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}