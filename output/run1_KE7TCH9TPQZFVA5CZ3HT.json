{"classification_result": {"documents": [{"page_no": 1, "doc_type": "log"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 4.585596258266378e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "77b461e0-f08b-4436-b02b-045b87cf80ff", "Relationships": [{"Type": "CHILD", "Ids": ["9f9b08d7-b101-4530-b910-8938f1751c01", "ad168dc8-1f6b-4e6e-9958-370e0cbfa7e0", "4d837b40-ab7d-4996-a8d1-bbf7c65eefe0", "d23edd12-dc7b-4eea-b1ff-f685519566b1", "1b2fae90-8f8e-4d94-9e50-94bcd369fe86", "099d9ddc-4fd6-403c-9320-d327dda28175", "773f03cb-e8d8-4c5f-9062-d25f7ff1da1d", "ca6c14b5-6e53-4564-8f7a-cc9e15cca8a4", "91214627-3ca9-4b51-8fcc-0746cb6bdcd2", "c393ace3-62e9-4c46-ba70-6f8392437831", "d464ce8a-f540-4443-a7c9-5f6fbb288878", "ad352427-8911-4568-ae0f-8dd6ef68db03", "25ee438d-d53b-4986-a52a-10b3e81a7e01", "09a73d68-71c9-42df-ac25-d6845d6991aa", "d7c5d9a1-08da-4185-b228-b3dfe679ece2", "68f3b208-14ba-48f9-b739-c1135a218d23", "8f78f272-628e-42c9-a16a-c4173e71c1aa", "6ad1507b-7647-4aa6-957f-5bd134de8ad4", "bacbf532-60d1-4f4a-8ef3-b95f02f17116", "db271f36-18a9-4d40-a0ea-182d74e649b1", "d0167093-80a7-4597-af6c-81d09330dddd", "17c5fe65-707c-4bd0-a6be-77d2ea74de32", "6b8bc9b1-cf7f-4623-8136-0721fc415fb2", "4bad8b31-acd8-4811-8c73-6388831e5db6", "5fff30f7-9794-4f9f-876a-8de565a55a97", "d2bc7fa3-2f1f-4a33-a41c-4ec6c102d3fc", "81e84f55-a213-4b46-8d35-527eb0072c5c", "f50a3d50-5e10-4acd-a136-ebe7f7951f1c", "6dc39ec7-a1a8-446a-94e1-7acaa1658df9", "d832f450-c54e-4ceb-a331-d448c9a685f7", "ce46c18f-8ae8-40c0-9141-2c569d89e492", "5a82c406-d7b9-4781-9666-9375d0ea4d15", "acb5c6bd-f4eb-42a1-8607-e3a93c3a7602", "e8623530-8d20-425d-bd0f-157e3d3b0411"]}]}, {"BlockType": "LINE", "Confidence": 98.46856689453125, "Text": "W&I Cert #1 ; RECVD: 5/25/2025 8:06:20 AM", "Geometry": {"BoundingBox": {"Width": 0.28120678663253784, "Height": 0.010260075330734253, "Left": 0.7022016644477844, "Top": 0.013394437730312347}, "Polygon": [{"X": 0.7022016644477844, "Y": 0.013395016081631184}, {"X": 0.9834084510803223, "Y": 0.013394437730312347}, {"X": 0.9834083914756775, "Y": 0.023653944954276085}, {"X": 0.7022016644477844, "Y": 0.0236545130610466}]}, "Id": "9f9b08d7-b101-4530-b910-8938f1751c01", "Relationships": [{"Type": "CHILD", "Ids": ["cd83268d-d8b7-4f36-8ead-2e42d3a00038", "7ad9f890-5fad-4382-899c-070044191b36", "ac620f22-eb38-46f0-b244-2240cdaba724", "4289c008-4c77-4584-9f08-e3e7d32a0bac", "c4f16e5d-13e8-43cc-8fa7-833e263030b8", "99ff052d-38f7-492c-9e00-d18d469d3c86", "06a57e82-3caa-458c-8dc1-5ff3141ad2c8", "bf8408c7-e138-4fdc-9d7f-28171303dbaa"]}]}, {"BlockType": "LINE", "Confidence": 96.86832427978516, "Text": "CENTRAL TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.4654563367366791, "Height": 0.05830059573054314, "Left": 0.1370563805103302, "Top": 0.06627605855464935}, "Polygon": [{"X": 0.13705644011497498, "Y": 0.06627693772315979}, {"X": 0.6025127172470093, "Y": 0.06627605855464935}, {"X": 0.6025125980377197, "Y": 0.12457586079835892}, {"X": 0.1370563805103302, "Y": 0.12457665801048279}]}, "Id": "ad168dc8-1f6b-4e6e-9958-370e0cbfa7e0", "Relationships": [{"Type": "CHILD", "Ids": ["6f6328e1-a20b-4f17-9554-9de352c9dc6e", "4d0211bb-6afb-4ba2-aff7-9aeec07894c7"]}]}, {"BlockType": "LINE", "Confidence": 99.9867172241211, "Text": "Driver Detention Certificate", "Geometry": {"BoundingBox": {"Width": 0.18704365193843842, "Height": 0.009667066857218742, "Left": 0.13847605884075165, "Top": 0.15300403535366058}, "Polygon": [{"X": 0.13847607374191284, "Y": 0.15300433337688446}, {"X": 0.32551971077919006, "Y": 0.15300403535366058}, {"X": 0.32551971077919006, "Y": 0.1626708060503006}, {"X": 0.13847605884075165, "Y": 0.16267110407352448}]}, "Id": "4d837b40-ab7d-4996-a8d1-bbf7c65eefe0", "Relationships": [{"Type": "CHILD", "Ids": ["1c8efbc1-c505-42b0-9327-421e45d550ff", "b7426860-de1e-4396-af6f-fd4c85e41782", "b85401ef-b34a-4c88-9d67-19ff6e2267cf"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Customer Name", "Geometry": {"BoundingBox": {"Width": 0.10951869189739227, "Height": 0.009137741290032864, "Left": 0.1382559835910797, "Top": 0.20617221295833588}, "Polygon": [{"X": 0.1382559835910797, "Y": 0.20617236196994781}, {"X": 0.2477746605873108, "Y": 0.20617221295833588}, {"X": 0.2477746605873108, "Y": 0.21530979871749878}, {"X": 0.1382559835910797, "Y": 0.21530994772911072}]}, "Id": "d23edd12-dc7b-4eea-b1ff-f685519566b1", "Relationships": [{"Type": "CHILD", "Ids": ["362a05f5-e0f7-421c-90ca-3738c8c7a893", "acf70e3f-1196-4bb3-b8d8-86bca73f41ad"]}]}, {"BlockType": "LINE", "Confidence": 99.54264831542969, "Text": "ILLING PACKAGING % ENVOY LOGISTICS", "Geometry": {"BoundingBox": {"Width": 0.3165134787559509, "Height": 0.010158941149711609, "Left": 0.35967087745666504, "Top": 0.2040078490972519}, "Polygon": [{"X": 0.3596709072589874, "Y": 0.2040083110332489}, {"X": 0.676184356212616, "Y": 0.2040078490972519}, {"X": 0.676184356212616, "Y": 0.21416634321212769}, {"X": 0.35967087745666504, "Y": 0.2141667902469635}]}, "Id": "1b2fae90-8f8e-4d94-9e50-94bcd369fe86", "Relationships": [{"Type": "CHILD", "Ids": ["707b825c-2221-4ae8-bd9a-9e404789701c", "1ae60e88-b48f-4ab2-b61e-c1cd67b1077c", "28c0dccf-6e4b-45f0-96c1-b2d5a0a13197", "3e1a3b0b-f0bd-4caf-b968-0ef101bfb4a1", "bfcbbf44-4ea6-4f89-840c-afdea123b66e"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Customer Number", "Geometry": {"BoundingBox": {"Width": 0.12544049322605133, "Height": 0.009529013186693192, "Left": 0.1381930410861969, "Top": 0.24328768253326416}, "Polygon": [{"X": 0.1381930410861969, "Y": 0.2432878464460373}, {"X": 0.26363351941108704, "Y": 0.24328768253326416}, {"X": 0.26363351941108704, "Y": 0.2528165280818939}, {"X": 0.1381930410861969, "Y": 0.25281670689582825}]}, "Id": "099d9ddc-4fd6-403c-9320-d327dda28175", "Relationships": [{"Type": "CHILD", "Ids": ["8d6831d5-4120-4fa5-b2e5-88c3f2bd7ce9", "ee55ffc5-3dd9-457a-ba04-7554ea91d2e4"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "54903000114", "Geometry": {"BoundingBox": {"Width": 0.09015718847513199, "Height": 0.009875117801129818, "Left": 0.35939961671829224, "Top": 0.2423078566789627}, "Polygon": [{"X": 0.35939961671829224, "Y": 0.24230797588825226}, {"X": 0.44955679774284363, "Y": 0.2423078566789627}, {"X": 0.44955676794052124, "Y": 0.25218287110328674}, {"X": 0.35939961671829224, "Y": 0.2521829605102539}]}, "Id": "773f03cb-e8d8-4c5f-9062-d25f7ff1da1d", "Relationships": [{"Type": "CHILD", "Ids": ["c5c1da05-e305-4145-8090-0056f76b288c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Detention Company", "Geometry": {"BoundingBox": {"Width": 0.13638369739055634, "Height": 0.011349259875714779, "Left": 0.13845989108085632, "Top": 0.28082454204559326}, "Polygon": [{"X": 0.13845990598201752, "Y": 0.2808247208595276}, {"X": 0.27484360337257385, "Y": 0.28082454204559326}, {"X": 0.27484357357025146, "Y": 0.2921736538410187}, {"X": 0.13845989108085632, "Y": 0.2921738028526306}]}, "Id": "ca6c14b5-6e53-4564-8f7a-cc9e15cca8a4", "Relationships": [{"Type": "CHILD", "Ids": ["af0fe203-cd5b-478e-90f2-f9d871db6076", "80b15e1a-29d7-4cb6-9191-6c6855268c3f"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "ILLING COMPANY", "Geometry": {"BoundingBox": {"Width": 0.13764987885951996, "Height": 0.009847871959209442, "Left": 0.3595375716686249, "Top": 0.27862489223480225}, "Polygon": [{"X": 0.3595375716686249, "Y": 0.2786250710487366}, {"X": 0.49718743562698364, "Y": 0.27862489223480225}, {"X": 0.49718743562698364, "Y": 0.28847262263298035}, {"X": 0.3595375716686249, "Y": 0.2884727716445923}]}, "Id": "91214627-3ca9-4b51-8fcc-0746cb6bdcd2", "Relationships": [{"Type": "CHILD", "Ids": ["14f80e05-9967-4775-b1e6-97a6b0713dc4", "c5fe3e08-be9d-4f0c-afc8-7c193822d251"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Address", "Geometry": {"BoundingBox": {"Width": 0.05505753308534622, "Height": 0.009535741060972214, "Left": 0.13767777383327484, "Top": 0.31806784868240356}, "Polygon": [{"X": 0.13767778873443604, "Y": 0.31806790828704834}, {"X": 0.19273531436920166, "Y": 0.31806784868240356}, {"X": 0.19273529946804047, "Y": 0.3276035487651825}, {"X": 0.13767777383327484, "Y": 0.32760360836982727}]}, "Id": "c393ace3-62e9-4c46-ba70-6f8392437831", "Relationships": [{"Type": "CHILD", "Ids": ["5f57f774-d073-48b0-b432-5ef0c60f65dc"]}]}, {"BlockType": "LINE", "Confidence": 99.16423797607422, "Text": "W204 N13125 GOLDENDALE RD", "Geometry": {"BoundingBox": {"Width": 0.24124713242053986, "Height": 0.009906723164021969, "Left": 0.3594009280204773, "Top": 0.3168410658836365}, "Polygon": [{"X": 0.3594009280204773, "Y": 0.3168413043022156}, {"X": 0.600648045539856, "Y": 0.3168410658836365}, {"X": 0.600648045539856, "Y": 0.32674750685691833}, {"X": 0.3594009280204773, "Y": 0.3267477750778198}]}, "Id": "d464ce8a-f540-4443-a7c9-5f6fbb288878", "Relationships": [{"Type": "CHILD", "Ids": ["95184a68-11ac-4752-a537-f8f708e2df70", "89cea876-999f-4bba-b20b-2a8e6aa4e30a", "23447cfa-a827-497a-bf05-12c3c7577a30", "79cbbcc0-aa9a-4d54-a085-52987f9952e0"]}]}, {"BlockType": "LINE", "Confidence": 99.39306640625, "Text": "RICHFIELD, WI 53076", "Geometry": {"BoundingBox": {"Width": 0.15855909883975983, "Height": 0.011036435142159462, "Left": 0.35937365889549255, "Top": 0.3368082642555237}, "Polygon": [{"X": 0.35937365889549255, "Y": 0.3368084132671356}, {"X": 0.5179327726364136, "Y": 0.3368082642555237}, {"X": 0.5179327130317688, "Y": 0.34784454107284546}, {"X": 0.35937365889549255, "Y": 0.3478446900844574}]}, "Id": "ad352427-8911-4568-ae0f-8dd6ef68db03", "Relationships": [{"Type": "CHILD", "Ids": ["62aa6861-e6b3-45a0-9600-dc096c4dea48", "5b7f45b4-223d-4ac5-b0e7-fee371789f51", "b59742a6-9061-4d78-ab85-c38bfe490531"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Trailer", "Geometry": {"BoundingBox": {"Width": 0.04498463124036789, "Height": 0.009144456125795841, "Left": 0.13734956085681915, "Top": 0.3702443242073059}, "Polygon": [{"X": 0.13734956085681915, "Y": 0.3702443540096283}, {"X": 0.18233418464660645, "Y": 0.3702443242073059}, {"X": 0.18233416974544525, "Y": 0.3793887495994568}, {"X": 0.13734956085681915, "Y": 0.3793887794017792}]}, "Id": "25ee438d-d53b-4986-a52a-10b3e81a7e01", "Relationships": [{"Type": "CHILD", "Ids": ["f81404dc-de60-4830-acd3-1d5e699dc966"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "2400970", "Geometry": {"BoundingBox": {"Width": 0.057124074548482895, "Height": 0.009701789356768131, "Left": 0.35928699374198914, "Top": 0.3710048198699951}, "Polygon": [{"X": 0.3592870235443115, "Y": 0.3710048794746399}, {"X": 0.41641107201576233, "Y": 0.3710048198699951}, {"X": 0.41641107201576233, "Y": 0.3807065486907959}, {"X": 0.35928699374198914, "Y": 0.3807066082954407}]}, "Id": "09a73d68-71c9-42df-ac25-d6845d6991aa", "Relationships": [{"Type": "CHILD", "Ids": ["5e0eadf8-ab81-445d-8d8f-a2ac262d3fa5"]}]}, {"BlockType": "LINE", "Confidence": 99.99349212646484, "Text": "Date of Detention", "Geometry": {"BoundingBox": {"Width": 0.12066872417926788, "Height": 0.009538189508020878, "Left": 0.13857406377792358, "Top": 0.42407968640327454}, "Polygon": [{"X": 0.13857406377792358, "Y": 0.4240797758102417}, {"X": 0.2592427730560303, "Y": 0.42407968640327454}, {"X": 0.2592427730560303, "Y": 0.43361780047416687}, {"X": 0.13857406377792358, "Y": 0.43361788988113403}]}, "Id": "d7c5d9a1-08da-4185-b228-b3dfe679ece2", "Relationships": [{"Type": "CHILD", "Ids": ["f14665ad-7c26-4a93-afd9-3595686317b0", "e32a8dba-1d4d-42f6-a4e5-2794460cc984", "8e60cc34-28e2-456f-8467-632064c0d8b4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "05/20/2025", "Geometry": {"BoundingBox": {"Width": 0.07385528832674026, "Height": 0.00978498812764883, "Left": 0.35925760865211487, "Top": 0.424184113740921}, "Polygon": [{"X": 0.35925760865211487, "Y": 0.4241841733455658}, {"X": 0.43311288952827454, "Y": 0.424184113740921}, {"X": 0.43311285972595215, "Y": 0.43396905064582825}, {"X": 0.35925760865211487, "Y": 0.433969110250473}]}, "Id": "68f3b208-14ba-48f9-b739-c1135a218d23", "Relationships": [{"Type": "CHILD", "Ids": ["7d58425e-afec-4529-be89-e5c7d563cb01"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Free Time", "Geometry": {"BoundingBox": {"Width": 0.06641670316457748, "Height": 0.009168814867734909, "Left": 0.542034387588501, "Top": 0.42443886399269104}, "Polygon": [{"X": 0.5420344471931458, "Y": 0.4244389235973358}, {"X": 0.6084511280059814, "Y": 0.42443886399269104}, {"X": 0.6084511280059814, "Y": 0.43360763788223267}, {"X": 0.542034387588501, "Y": 0.43360769748687744}]}, "Id": "8f78f272-628e-42c9-a16a-c4173e71c1aa", "Relationships": [{"Type": "CHILD", "Ids": ["a182c646-a4bc-4def-add0-e178d4ed9346", "2eecd6e3-355a-4841-8b95-ba8a5293f2b2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "30", "Geometry": {"BoundingBox": {"Width": 0.016433531418442726, "Height": 0.00953955389559269, "Left": 0.7678049206733704, "Top": 0.4244562089443207}, "Polygon": [{"X": 0.7678049802780151, "Y": 0.4244562089443207}, {"X": 0.7842384576797485, "Y": 0.4244562089443207}, {"X": 0.7842384576797485, "Y": 0.4339957535266876}, {"X": 0.7678049206733704, "Y": 0.4339957535266876}]}, "Id": "6ad1507b-7647-4aa6-957f-5bd134de8ad4", "Relationships": [{"Type": "CHILD", "Ids": ["9b7285fb-29c8-4f58-b39c-67f432c2e2b7"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "Pro Number", "Geometry": {"BoundingBox": {"Width": 0.08240625262260437, "Height": 0.00921056792140007, "Left": 0.17208631336688995, "Top": 0.4575645625591278}, "Polygon": [{"X": 0.17208631336688995, "Y": 0.4575646221637726}, {"X": 0.25449255108833313, "Y": 0.4575645625591278}, {"X": 0.25449255108833313, "Y": 0.4667750895023346}, {"X": 0.17208631336688995, "Y": 0.466775119304657}]}, "Id": "bacbf532-60d1-4f4a-8ef3-b95f02f17116", "Relationships": [{"Type": "CHILD", "Ids": ["2132385c-a973-4429-9a12-42fe43bd5c8d", "44835849-75aa-460f-a348-fdccdc8fdfcf"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Weight", "Geometry": {"BoundingBox": {"Width": 0.04921639338135719, "Height": 0.011375298723578453, "Left": 0.4001304507255554, "Top": 0.457475870847702}, "Polygon": [{"X": 0.4001304507255554, "Y": 0.4574759006500244}, {"X": 0.4493468403816223, "Y": 0.457475870847702}, {"X": 0.4493468105792999, "Y": 0.46885111927986145}, {"X": 0.4001304507255554, "Y": 0.46885114908218384}]}, "Id": "db271f36-18a9-4d40-a0ea-182d74e649b1", "Relationships": [{"Type": "CHILD", "Ids": ["836d3cad-2152-4eb1-8c15-84f40a5e5524"]}]}, {"BlockType": "LINE", "Confidence": 99.74330139160156, "Text": "Time In", "Geometry": {"BoundingBox": {"Width": 0.050708264112472534, "Height": 0.00896165519952774, "Left": 0.5585670471191406, "Top": 0.4578540027141571}, "Polygon": [{"X": 0.5585670471191406, "Y": 0.4578540623188019}, {"X": 0.6092752814292908, "Y": 0.4578540027141571}, {"X": 0.6092752814292908, "Y": 0.46681565046310425}, {"X": 0.5585670471191406, "Y": 0.46681568026542664}]}, "Id": "d0167093-80a7-4597-af6c-81d09330dddd", "Relationships": [{"Type": "CHILD", "Ids": ["0de78813-f954-42ba-8ee3-021e724c16f4", "ebb00785-8210-4bbb-a793-13694cfef538"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "Time Out", "Geometry": {"BoundingBox": {"Width": 0.06355907022953033, "Height": 0.009128347970545292, "Left": 0.6421647071838379, "Top": 0.45776909589767456}, "Polygon": [{"X": 0.6421647071838379, "Y": 0.45776912569999695}, {"X": 0.705723762512207, "Y": 0.45776909589767456}, {"X": 0.705723762512207, "Y": 0.4668973982334137}, {"X": 0.6421647071838379, "Y": 0.4668974280357361}]}, "Id": "17c5fe65-707c-4bd0-a6be-77d2ea74de32", "Relationships": [{"Type": "CHILD", "Ids": ["8e3bcd9f-1355-44d4-9335-126da6ae207a", "831cd8c3-c5b4-4ddc-a907-b5a6f5bc4155"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Detention Time", "Geometry": {"BoundingBox": {"Width": 0.10446517169475555, "Height": 0.009148729965090752, "Left": 0.7713633179664612, "Top": 0.4577257037162781}, "Polygon": [{"X": 0.7713633179664612, "Y": 0.45772576332092285}, {"X": 0.8758284449577332, "Y": 0.4577257037162781}, {"X": 0.8758284449577332, "Y": 0.466874361038208}, {"X": 0.7713633179664612, "Y": 0.46687445044517517}]}, "Id": "6b8bc9b1-cf7f-4623-8136-0721fc415fb2", "Relationships": [{"Type": "CHILD", "Ids": ["0c0c8832-1987-43ed-ade2-20e2cc7ce54e", "a22a5c2a-df18-4986-a017-1cc113b2313e"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "212123517", "Geometry": {"BoundingBox": {"Width": 0.07323504239320755, "Height": 0.0097573297098279, "Left": 0.1389329433441162, "Top": 0.472911536693573}, "Polygon": [{"X": 0.1389329433441162, "Y": 0.4729115962982178}, {"X": 0.21216797828674316, "Y": 0.472911536693573}, {"X": 0.21216797828674316, "Y": 0.48266884684562683}, {"X": 0.1389329433441162, "Y": 0.4826688766479492}]}, "Id": "4bad8b31-acd8-4811-8c73-6388831e5db6", "Relationships": [{"Type": "CHILD", "Ids": ["f953b68c-71bf-4594-9486-1c301b7a6c95"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "2229", "Geometry": {"BoundingBox": {"Width": 0.0323796346783638, "Height": 0.009609190747141838, "Left": 0.3591700494289398, "Top": 0.4730161726474762}, "Polygon": [{"X": 0.3591700494289398, "Y": 0.4730162024497986}, {"X": 0.391549676656723, "Y": 0.4730161726474762}, {"X": 0.391549676656723, "Y": 0.4826253354549408}, {"X": 0.3591700494289398, "Y": 0.4826253652572632}]}, "Id": "5fff30f7-9794-4f9f-876a-8de565a55a97", "Relationships": [{"Type": "CHILD", "Ids": ["46e163e5-bd2e-450d-af18-0bbc66c7af8f"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "13:41", "Geometry": {"BoundingBox": {"Width": 0.033566903322935104, "Height": 0.00940698105841875, "Left": 0.5485408306121826, "Top": 0.47315138578414917}, "Polygon": [{"X": 0.5485408306121826, "Y": 0.47315141558647156}, {"X": 0.5821077227592468, "Y": 0.47315138578414917}, {"X": 0.5821077227592468, "Y": 0.48255836963653564}, {"X": 0.5485408306121826, "Y": 0.48255836963653564}]}, "Id": "d2bc7fa3-2f1f-4a33-a41c-4ec6c102d3fc", "Relationships": [{"Type": "CHILD", "Ids": ["2d395088-fbb5-422b-a5a1-aa8ed35f6b4c"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "14:34", "Geometry": {"BoundingBox": {"Width": 0.035402726382017136, "Height": 0.009517006576061249, "Left": 0.6384938359260559, "Top": 0.4730910062789917}, "Polygon": [{"X": 0.6384938955307007, "Y": 0.4730910360813141}, {"X": 0.6738966107368469, "Y": 0.4730910062789917}, {"X": 0.6738965511322021, "Y": 0.48260802030563354}, {"X": 0.6384938359260559, "Y": 0.48260802030563354}]}, "Id": "81e84f55-a213-4b46-8d35-527eb0072c5c", "Relationships": [{"Type": "CHILD", "Ids": ["f016cee4-d25b-45fd-b416-c829c2012df4"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "53", "Geometry": {"BoundingBox": {"Width": 0.01501556858420372, "Height": 0.00930704828351736, "Left": 0.7681207060813904, "Top": 0.47314777970314026}, "Polygon": [{"X": 0.7681207656860352, "Y": 0.47314777970314026}, {"X": 0.7831363081932068, "Y": 0.47314777970314026}, {"X": 0.783136248588562, "Y": 0.4824548065662384}, {"X": 0.7681207060813904, "Y": 0.4824548363685608}]}, "Id": "f50a3d50-5e10-4acd-a136-ebe7f7951f1c", "Relationships": [{"Type": "CHILD", "Ids": ["c5fde56e-4760-4f88-b90e-166fb9d5d8e4"]}]}, {"BlockType": "LINE", "Confidence": 99.99674224853516, "Text": "Total Billable Time", "Geometry": {"BoundingBox": {"Width": 0.12584924697875977, "Height": 0.009360568597912788, "Left": 0.1373981088399887, "Top": 0.7407608032226562}, "Polygon": [{"X": 0.1373981088399887, "Y": 0.7407608032226562}, {"X": 0.2632473409175873, "Y": 0.740760862827301}, {"X": 0.2632473409175873, "Y": 0.7501213550567627}, {"X": 0.1373981088399887, "Y": 0.7501213550567627}]}, "Id": "6dc39ec7-a1a8-446a-94e1-7acaa1658df9", "Relationships": [{"Type": "CHILD", "Ids": ["b40340fd-953d-45d5-a81c-49b6d6920a4c", "765320bd-f79b-4944-9af2-f9ebc4fb976d", "dc2c685e-e045-4a75-b0cd-684468440c5a"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "23", "Geometry": {"BoundingBox": {"Width": 0.015582477673888206, "Height": 0.00932667963206768, "Left": 0.359360009431839, "Top": 0.7396042346954346}, "Polygon": [{"X": 0.359360009431839, "Y": 0.7396042346954346}, {"X": 0.37494248151779175, "Y": 0.7396042346954346}, {"X": 0.37494248151779175, "Y": 0.7489309310913086}, {"X": 0.359360009431839, "Y": 0.7489308714866638}]}, "Id": "d832f450-c54e-4ceb-a331-d448c9a685f7", "Relationships": [{"Type": "CHILD", "Ids": ["7308cfec-5b6e-4f0f-9b7a-81934e501e8e"]}]}, {"BlockType": "LINE", "Confidence": 99.9353256225586, "Text": "Total Weight", "Geometry": {"BoundingBox": {"Width": 0.08801272511482239, "Height": 0.011379706673324108, "Left": 0.13744521141052246, "Top": 0.7573843598365784}, "Polygon": [{"X": 0.13744522631168365, "Y": 0.7573843598365784}, {"X": 0.22545793652534485, "Y": 0.7573843598365784}, {"X": 0.22545792162418365, "Y": 0.7687640190124512}, {"X": 0.13744521141052246, "Y": 0.7687640190124512}]}, "Id": "ce46c18f-8ae8-40c0-9141-2c569d89e492", "Relationships": [{"Type": "CHILD", "Ids": ["3487a217-7342-4d4a-a2c5-4d7d7d32602e", "7af0cdbc-20b1-48bd-a376-5c2d98e6e291"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "2229", "Geometry": {"BoundingBox": {"Width": 0.03243493288755417, "Height": 0.009528819471597672, "Left": 0.3590348958969116, "Top": 0.758657693862915}, "Polygon": [{"X": 0.3590348958969116, "Y": 0.758657693862915}, {"X": 0.391469806432724, "Y": 0.758657693862915}, {"X": 0.391469806432724, "Y": 0.7681865096092224}, {"X": 0.3590348958969116, "Y": 0.7681865096092224}]}, "Id": "5a82c406-d7b9-4781-9666-9375d0ea4d15", "Relationships": [{"Type": "CHILD", "Ids": ["497fc180-b9c2-4461-a51d-7090222acdbc"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Driver Certification:", "Geometry": {"BoundingBox": {"Width": 0.13253214955329895, "Height": 0.009525356814265251, "Left": 0.13845294713974, "Top": 0.7906875610351562}, "Polygon": [{"X": 0.13845294713974, "Y": 0.7906875610351562}, {"X": 0.27098509669303894, "Y": 0.790687620639801}, {"X": 0.27098509669303894, "Y": 0.8002129197120667}, {"X": 0.13845294713974, "Y": 0.8002128601074219}]}, "Id": "acb5c6bd-f4eb-42a1-8607-e3a93c3a7602", "Relationships": [{"Type": "CHILD", "Ids": ["4d01ca94-8b13-4519-834f-b1b7ab8530d6", "862ee2c0-9c71-49c7-9d74-02a9233d5f79"]}]}, {"BlockType": "LINE", "Confidence": 99.97921752929688, "Text": "Time required for live pickup exceeds free time allowed per stop.", "Geometry": {"BoundingBox": {"Width": 0.37318363785743713, "Height": 0.010517187416553497, "Left": 0.14995381236076355, "Top": 0.8108997344970703}, "Polygon": [{"X": 0.14995381236076355, "Y": 0.8108997344970703}, {"X": 0.5231374502182007, "Y": 0.8108999133110046}, {"X": 0.5231374502182007, "Y": 0.821416974067688}, {"X": 0.14995381236076355, "Y": 0.8214167952537537}]}, "Id": "e8623530-8d20-425d-bd0f-157e3d3b0411", "Relationships": [{"Type": "CHILD", "Ids": ["786047ff-ff94-4846-b332-e01c2e556637", "b697901f-0219-4aa7-8753-a72d764b7779", "d725809f-5f13-436a-bd84-7e470674ff8a", "6edf0a28-fb64-447a-b012-34bf8ad594e6", "e339fbb9-7ec6-4643-b47b-7fd8c5a7897e", "9592c9e7-8685-47e3-898a-558ba31a7b8d", "5f7bd8e8-5c08-4cd0-b925-04467515cdc2", "d9da1c9b-34ff-49b7-b53f-86d5dbaf4a9e", "3793d729-fd0c-4c2b-b8ec-52e991b6b902", "f34b6b35-cd69-4bbe-a2eb-0e3057f45c80", "4282a516-63b1-4374-a15d-2d0b14340814"]}]}, {"BlockType": "WORD", "Confidence": 99.57769012451172, "Text": "W&I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0297834612429142, "Height": 0.00893917866051197, "Left": 0.7022016644477844, "Top": 0.01340173464268446}, "Polygon": [{"X": 0.7022016644477844, "Y": 0.013401796109974384}, {"X": 0.7319850921630859, "Y": 0.01340173464268446}, {"X": 0.7319850921630859, "Y": 0.02234085276722908}, {"X": 0.7022016644477844, "Y": 0.022340914234519005}]}, "Id": "cd83268d-d8b7-4f36-8ead-2e42d3a00038"}, {"BlockType": "WORD", "Confidence": 98.01438903808594, "Text": "Cert", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025478797033429146, "Height": 0.008906317874789238, "Left": 0.7359829545021057, "Top": 0.013443064875900745}, "Polygon": [{"X": 0.7359829545021057, "Y": 0.013443117029964924}, {"X": 0.7614617347717285, "Y": 0.013443064875900745}, {"X": 0.7614617347717285, "Y": 0.02234933152794838}, {"X": 0.7359829545021057, "Y": 0.02234938181936741}]}, "Id": "7ad9f890-5fad-4382-899c-070044191b36"}, {"BlockType": "WORD", "Confidence": 98.447265625, "Text": "#1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01354778278619051, "Height": 0.00884051900357008, "Left": 0.7646529078483582, "Top": 0.013400515541434288}, "Polygon": [{"X": 0.7646529078483582, "Y": 0.013400543481111526}, {"X": 0.7782006859779358, "Y": 0.013400515541434288}, {"X": 0.778200626373291, "Y": 0.022241007536649704}, {"X": 0.7646529078483582, "Y": 0.022241033613681793}]}, "Id": "ac620f22-eb38-46f0-b244-2240cdaba724"}, {"BlockType": "WORD", "Confidence": 96.32254028320312, "Text": ";", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0034048338420689106, "Height": 0.00792714487761259, "Left": 0.7836134433746338, "Top": 0.015727203339338303}, "Polygon": [{"X": 0.7836134433746338, "Y": 0.0157272107899189}, {"X": 0.7870182394981384, "Y": 0.015727203339338303}, {"X": 0.7870182394981384, "Y": 0.02365434169769287}, {"X": 0.7836134433746338, "Y": 0.023654349148273468}]}, "Id": "4289c008-4c77-4584-9f08-e3e7d32a0bac"}, {"BlockType": "WORD", "Confidence": 99.61834716796875, "Text": "RECVD:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05326240509748459, "Height": 0.009004643186926842, "Left": 0.7908110618591309, "Top": 0.013451770879328251}, "Polygon": [{"X": 0.7908110618591309, "Y": 0.013451880775392056}, {"X": 0.8440734148025513, "Y": 0.013451770879328251}, {"X": 0.8440734148025513, "Y": 0.022456306964159012}, {"X": 0.7908110618591309, "Y": 0.022456414997577667}]}, "Id": "c4f16e5d-13e8-43cc-8fa7-833e263030b8"}, {"BlockType": "WORD", "Confidence": 99.5703125, "Text": "5/25/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05872606113553047, "Height": 0.008996949531137943, "Left": 0.8489769697189331, "Top": 0.013429684564471245}, "Polygon": [{"X": 0.8489769697189331, "Y": 0.01342980470508337}, {"X": 0.9077030420303345, "Y": 0.013429684564471245}, {"X": 0.9077029824256897, "Y": 0.022426513954997063}, {"X": 0.8489769697189331, "Y": 0.022426633164286613}]}, "Id": "99ff052d-38f7-492c-9e00-d18d469d3c86"}, {"BlockType": "WORD", "Confidence": 98.974609375, "Text": "8:06:20", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04509780928492546, "Height": 0.008960798382759094, "Left": 0.9117971658706665, "Top": 0.013440685346722603}, "Polygon": [{"X": 0.9117971658706665, "Y": 0.01344077754765749}, {"X": 0.9568949341773987, "Y": 0.013440685346722603}, {"X": 0.9568949341773987, "Y": 0.022401392459869385}, {"X": 0.9117971658706665, "Y": 0.022401483729481697}]}, "Id": "06a57e82-3caa-458c-8dc1-5ff3141ad2c8"}, {"BlockType": "WORD", "Confidence": 97.22337341308594, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02346748486161232, "Height": 0.008883563801646233, "Left": 0.9599409699440002, "Top": 0.013394437730312347}, "Polygon": [{"X": 0.9599409699440002, "Y": 0.013394486159086227}, {"X": 0.9834084510803223, "Y": 0.013394437730312347}, {"X": 0.9834083914756775, "Y": 0.02227795496582985}, {"X": 0.9599409699440002, "Y": 0.02227800153195858}]}, "Id": "bf8408c7-e138-4fdc-9d7f-28171303dbaa"}, {"BlockType": "WORD", "Confidence": 93.76594543457031, "Text": "CENTRAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.25737401843070984, "Height": 0.0583002008497715, "Left": 0.1370563805103302, "Top": 0.06627645343542099}, "Polygon": [{"X": 0.13705644011497498, "Y": 0.06627693772315979}, {"X": 0.39443039894104004, "Y": 0.06627645343542099}, {"X": 0.3944303095340729, "Y": 0.12457621842622757}, {"X": 0.1370563805103302, "Y": 0.12457665801048279}]}, "Id": "6f6328e1-a20b-4f17-9554-9de352c9dc6e"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20073926448822021, "Height": 0.035947129130363464, "Left": 0.4017733931541443, "Top": 0.08824140578508377}, "Polygon": [{"X": 0.40177345275878906, "Y": 0.08824177086353302}, {"X": 0.6025126576423645, "Y": 0.08824140578508377}, {"X": 0.6025125980377197, "Y": 0.12418819218873978}, {"X": 0.4017733931541443, "Y": 0.12418853491544724}]}, "Id": "4d0211bb-6afb-4ba2-aff7-9aeec07894c7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041430070996284485, "Height": 0.009238414466381073, "Left": 0.13847605884075165, "Top": 0.1533268839120865}, "Polygon": [{"X": 0.13847607374191284, "Y": 0.15332694351673126}, {"X": 0.17990612983703613, "Y": 0.1533268839120865}, {"X": 0.17990611493587494, "Y": 0.1625652313232422}, {"X": 0.13847605884075165, "Y": 0.16256529092788696}]}, "Id": "1c8efbc1-c505-42b0-9327-421e45d550ff"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Detention", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06799887120723724, "Height": 0.009239924140274525, "Left": 0.18368008732795715, "Top": 0.15324880182743073}, "Polygon": [{"X": 0.18368010222911835, "Y": 0.15324892103672028}, {"X": 0.2516789734363556, "Y": 0.15324880182743073}, {"X": 0.2516789436340332, "Y": 0.16248862445354462}, {"X": 0.18368008732795715, "Y": 0.16248872876167297}]}, "Id": "b7426860-de1e-4396-af6f-fd4c85e41782"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Certificate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06991881132125854, "Height": 0.00966688059270382, "Left": 0.2556008994579315, "Top": 0.15300403535366058}, "Polygon": [{"X": 0.2556009292602539, "Y": 0.15300415456295013}, {"X": 0.32551971077919006, "Y": 0.15300403535366058}, {"X": 0.32551971077919006, "Y": 0.1626708060503006}, {"X": 0.2556008994579315, "Y": 0.16267092525959015}]}, "Id": "b85401ef-b34a-4c88-9d67-19ff6e2267cf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06587287783622742, "Height": 0.00899930577725172, "Left": 0.1382559835910797, "Top": 0.2062142938375473}, "Polygon": [{"X": 0.1382559835910797, "Y": 0.20621439814567566}, {"X": 0.20412886142730713, "Y": 0.2062142938375473}, {"X": 0.20412884652614594, "Y": 0.21521350741386414}, {"X": 0.1382559835910797, "Y": 0.2152136117219925}]}, "Id": "362a05f5-e0f7-421c-90ca-3738c8c7a893"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Name", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039688605815172195, "Height": 0.009137642569839954, "Left": 0.2080860584974289, "Top": 0.20617221295833588}, "Polygon": [{"X": 0.2080860733985901, "Y": 0.20617227256298065}, {"X": 0.2477746605873108, "Y": 0.20617221295833588}, {"X": 0.2477746605873108, "Y": 0.21530979871749878}, {"X": 0.2080860584974289, "Y": 0.21530985832214355}]}, "Id": "acf70e3f-1196-4bb3-b8d8-86bca73f41ad"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "ILLING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053520381450653076, "Height": 0.009820757433772087, "Left": 0.35967087745666504, "Top": 0.2040877342224121}, "Polygon": [{"X": 0.3596709072589874, "Y": 0.20408780872821808}, {"X": 0.4131912589073181, "Y": 0.2040877342224121}, {"X": 0.4131912589073181, "Y": 0.21390841901302338}, {"X": 0.35967087745666504, "Y": 0.21390849351882935}]}, "Id": "707b825c-2221-4ae8-bd9a-9e404789701c"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "PACKAGING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09322622418403625, "Height": 0.009720115922391415, "Left": 0.4176799952983856, "Top": 0.20410381257534027}, "Polygon": [{"X": 0.417680025100708, "Y": 0.20410394668579102}, {"X": 0.5109062194824219, "Y": 0.20410381257534027}, {"X": 0.5109062194824219, "Y": 0.21382379531860352}, {"X": 0.4176799952983856, "Y": 0.21382392942905426}]}, "Id": "1ae60e88-b48f-4ab2-b61e-c1cd67b1077c"}, {"BlockType": "WORD", "Confidence": 98.01677703857422, "Text": "%", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014295033179223537, "Height": 0.010158506222069263, "Left": 0.5148254632949829, "Top": 0.2040080726146698}, "Polygon": [{"X": 0.5148255228996277, "Y": 0.204008087515831}, {"X": 0.5291205048561096, "Y": 0.2040080726146698}, {"X": 0.5291205048561096, "Y": 0.2141665518283844}, {"X": 0.5148254632949829, "Y": 0.2141665816307068}]}, "Id": "28c0dccf-6e4b-45f0-96c1-b2d5a0a13197"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "ENVOY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055118903517723083, "Height": 0.009679808281362057, "Left": 0.5330032110214233, "Top": 0.20417673885822296}, "Polygon": [{"X": 0.5330032110214233, "Y": 0.20417682826519012}, {"X": 0.5881221294403076, "Y": 0.20417673885822296}, {"X": 0.5881220698356628, "Y": 0.21385647356510162}, {"X": 0.5330032110214233, "Y": 0.2138565480709076}]}, "Id": "3e1a3b0b-f0bd-4caf-b968-0ef101bfb4a1"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "LOGISTICS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08361372351646423, "Height": 0.009855000302195549, "Left": 0.5925706028938293, "Top": 0.20412583649158478}, "Polygon": [{"X": 0.5925706624984741, "Y": 0.20412595570087433}, {"X": 0.676184356212616, "Y": 0.20412583649158478}, {"X": 0.676184356212616, "Y": 0.21398071944713593}, {"X": 0.5925706028938293, "Y": 0.21398083865642548}]}, "Id": "bfcbbf44-4ea6-4f89-840c-afdea123b66e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06590796262025833, "Height": 0.009104047901928425, "Left": 0.1381930410861969, "Top": 0.2437126487493515}, "Polygon": [{"X": 0.1381930410861969, "Y": 0.24371273815631866}, {"X": 0.20410099625587463, "Y": 0.2437126487493515}, {"X": 0.20410098135471344, "Y": 0.2528166174888611}, {"X": 0.1381930410861969, "Y": 0.25281670689582825}]}, "Id": "8d6831d5-4120-4fa5-b2e5-88c3f2bd7ce9"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05552450194954872, "Height": 0.009461557492613792, "Left": 0.2081090211868286, "Top": 0.24328768253326416}, "Polygon": [{"X": 0.2081090360879898, "Y": 0.24328775703907013}, {"X": 0.26363351941108704, "Y": 0.24328768253326416}, {"X": 0.26363351941108704, "Y": 0.25274917483329773}, {"X": 0.2081090211868286, "Y": 0.2527492344379425}]}, "Id": "ee55ffc5-3dd9-457a-ba04-7554ea91d2e4"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "54903000114", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09015718847513199, "Height": 0.009875117801129818, "Left": 0.35939961671829224, "Top": 0.2423078566789627}, "Polygon": [{"X": 0.35939961671829224, "Y": 0.24230797588825226}, {"X": 0.44955679774284363, "Y": 0.2423078566789627}, {"X": 0.44955676794052124, "Y": 0.25218287110328674}, {"X": 0.35939961671829224, "Y": 0.2521829605102539}]}, "Id": "c5c1da05-e305-4145-8090-0056f76b288c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Detention", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06778458505868912, "Height": 0.009235045872628689, "Left": 0.13845989108085632, "Top": 0.2808246314525604}, "Polygon": [{"X": 0.13845990598201752, "Y": 0.2808247208595276}, {"X": 0.20624448359012604, "Y": 0.2808246314525604}, {"X": 0.20624446868896484, "Y": 0.2900595963001251}, {"X": 0.13845989108085632, "Y": 0.2900596559047699}]}, "Id": "af0fe203-cd5b-478e-90f2-f9d871db6076"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Company", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06479659676551819, "Height": 0.011235754936933517, "Left": 0.21004699170589447, "Top": 0.28093796968460083}, "Polygon": [{"X": 0.21004700660705566, "Y": 0.2809380292892456}, {"X": 0.27484360337257385, "Y": 0.28093796968460083}, {"X": 0.27484357357025146, "Y": 0.2921736538410187}, {"X": 0.21004699170589447, "Y": 0.29217371344566345}]}, "Id": "80b15e1a-29d7-4cb6-9191-6c6855268c3f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "ILLING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05368999019265175, "Height": 0.009847769513726234, "Left": 0.3595375716686249, "Top": 0.2786250114440918}, "Polygon": [{"X": 0.3595375716686249, "Y": 0.2786250710487366}, {"X": 0.41322755813598633, "Y": 0.2786250114440918}, {"X": 0.41322752833366394, "Y": 0.2884727120399475}, {"X": 0.3595375716686249, "Y": 0.2884727716445923}]}, "Id": "14f80e05-9967-4775-b1e6-97a6b0713dc4"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "COMPANY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07956581562757492, "Height": 0.00975794903934002, "Left": 0.4176216125488281, "Top": 0.27862876653671265}, "Polygon": [{"X": 0.4176216423511505, "Y": 0.2786288559436798}, {"X": 0.49718743562698364, "Y": 0.27862876653671265}, {"X": 0.49718743562698364, "Y": 0.28838661313056946}, {"X": 0.4176216125488281, "Y": 0.288386732339859}]}, "Id": "c5fe3e08-be9d-4f0c-afc8-7c193822d251"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Address", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05505753308534622, "Height": 0.009535741060972214, "Left": 0.13767777383327484, "Top": 0.31806784868240356}, "Polygon": [{"X": 0.13767778873443604, "Y": 0.31806790828704834}, {"X": 0.19273531436920166, "Y": 0.31806784868240356}, {"X": 0.19273529946804047, "Y": 0.3276035487651825}, {"X": 0.13767777383327484, "Y": 0.32760360836982727}]}, "Id": "5f57f774-d073-48b0-b432-5ef0c60f65dc"}, {"BlockType": "WORD", "Confidence": 99.34331512451172, "Text": "W204", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039892278611660004, "Height": 0.009792307391762733, "Left": 0.3594009280204773, "Top": 0.3169058561325073}, "Polygon": [{"X": 0.3594009280204773, "Y": 0.3169059157371521}, {"X": 0.3992931842803955, "Y": 0.3169058561325073}, {"X": 0.3992931842803955, "Y": 0.3266981244087219}, {"X": 0.3594009280204773, "Y": 0.3266981840133667}]}, "Id": "95184a68-11ac-4752-a537-f8f708e2df70"}, {"BlockType": "WORD", "Confidence": 97.4609375, "Text": "N13125", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052624162286520004, "Height": 0.009733770042657852, "Left": 0.4029446244239807, "Top": 0.316898375749588}, "Polygon": [{"X": 0.4029446542263031, "Y": 0.3168984353542328}, {"X": 0.455568790435791, "Y": 0.316898375749588}, {"X": 0.45556876063346863, "Y": 0.3266320824623108}, {"X": 0.4029446244239807, "Y": 0.32663214206695557}]}, "Id": "89cea876-999f-4bba-b20b-2a8e6aa4e30a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "GOLDENDALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11006508022546768, "Height": 0.009891359135508537, "Left": 0.4640495479106903, "Top": 0.31684109568595886}, "Polygon": [{"X": 0.4640495479106903, "Y": 0.3168412148952484}, {"X": 0.5741146206855774, "Y": 0.31684109568595886}, {"X": 0.5741146206855774, "Y": 0.3267323076725006}, {"X": 0.4640495479106903, "Y": 0.32673242688179016}]}, "Id": "23447cfa-a827-497a-bf05-12c3c7577a30"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "RD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022499164566397667, "Height": 0.009785782545804977, "Left": 0.5781489014625549, "Top": 0.31696176528930664}, "Polygon": [{"X": 0.5781489014625549, "Y": 0.31696179509162903}, {"X": 0.600648045539856, "Y": 0.31696176528930664}, {"X": 0.600648045539856, "Y": 0.32674750685691833}, {"X": 0.5781489014625549, "Y": 0.3267475366592407}]}, "Id": "79cbbcc0-aa9a-4d54-a085-52987f9952e0"}, {"BlockType": "WORD", "Confidence": 98.80421447753906, "Text": "RICHFIELD,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08903852105140686, "Height": 0.010875481180846691, "Left": 0.35937365889549255, "Top": 0.33696919679641724}, "Polygon": [{"X": 0.35937365889549255, "Y": 0.3369692862033844}, {"X": 0.448412150144577, "Y": 0.33696919679641724}, {"X": 0.448412150144577, "Y": 0.34784460067749023}, {"X": 0.35937365889549255, "Y": 0.3478446900844574}]}, "Id": "62aa6861-e6b3-45a0-9600-dc096c4dea48"}, {"BlockType": "WORD", "Confidence": 99.501953125, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020436430349946022, "Height": 0.0097899055108428, "Left": 0.4526374638080597, "Top": 0.33680829405784607}, "Polygon": [{"X": 0.4526374936103821, "Y": 0.33680832386016846}, {"X": 0.47307389974594116, "Y": 0.33680829405784607}, {"X": 0.4730738699436188, "Y": 0.34659817814826965}, {"X": 0.4526374638080597, "Y": 0.34659820795059204}]}, "Id": "5b7f45b4-223d-4ac5-b0e7-fee371789f51"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "53076", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04059736430644989, "Height": 0.009822673164308071, "Left": 0.4773353934288025, "Top": 0.336964875459671}, "Polygon": [{"X": 0.4773353934288025, "Y": 0.3369649052619934}, {"X": 0.5179327726364136, "Y": 0.336964875459671}, {"X": 0.5179327130317688, "Y": 0.3467874825000763}, {"X": 0.4773353934288025, "Y": 0.34678754210472107}]}, "Id": "b59742a6-9061-4d78-ab85-c38bfe490531"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04498463124036789, "Height": 0.009144456125795841, "Left": 0.13734956085681915, "Top": 0.3702443242073059}, "Polygon": [{"X": 0.13734956085681915, "Y": 0.3702443540096283}, {"X": 0.18233418464660645, "Y": 0.3702443242073059}, {"X": 0.18233416974544525, "Y": 0.3793887495994568}, {"X": 0.13734956085681915, "Y": 0.3793887794017792}]}, "Id": "f81404dc-de60-4830-acd3-1d5e699dc966"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "2400970", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057124074548482895, "Height": 0.009701789356768131, "Left": 0.35928699374198914, "Top": 0.3710048198699951}, "Polygon": [{"X": 0.3592870235443115, "Y": 0.3710048794746399}, {"X": 0.41641107201576233, "Y": 0.3710048198699951}, {"X": 0.41641107201576233, "Y": 0.3807065486907959}, {"X": 0.35928699374198914, "Y": 0.3807066082954407}]}, "Id": "5e0eadf8-ab81-445d-8d8f-a2ac262d3fa5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03160345181822777, "Height": 0.009125305339694023, "Left": 0.13857406377792358, "Top": 0.4244808852672577}, "Polygon": [{"X": 0.13857406377792358, "Y": 0.4244809150695801}, {"X": 0.17017751932144165, "Y": 0.4244808852672577}, {"X": 0.17017750442028046, "Y": 0.43360617756843567}, {"X": 0.13857406377792358, "Y": 0.43360620737075806}]}, "Id": "f14665ad-7c26-4a93-afd9-3595686317b0"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01447932980954647, "Height": 0.009374033659696579, "Left": 0.17393553256988525, "Top": 0.4240797460079193}, "Polygon": [{"X": 0.17393554747104645, "Y": 0.4240797460079193}, {"X": 0.18841487169265747, "Y": 0.4240797460079193}, {"X": 0.18841485679149628, "Y": 0.433453768491745}, {"X": 0.17393553256988525, "Y": 0.433453768491745}]}, "Id": "e32a8dba-1d4d-42f6-a4e5-2794460cc984"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Detention", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06755256652832031, "Height": 0.009233402088284492, "Left": 0.19169022142887115, "Top": 0.4243844449520111}, "Polygon": [{"X": 0.19169023633003235, "Y": 0.4243844747543335}, {"X": 0.2592427730560303, "Y": 0.4243844449520111}, {"X": 0.2592427730560303, "Y": 0.43361780047416687}, {"X": 0.19169022142887115, "Y": 0.43361783027648926}]}, "Id": "8e60cc34-28e2-456f-8467-632064c0d8b4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "05/20/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07385528832674026, "Height": 0.00978498812764883, "Left": 0.35925760865211487, "Top": 0.424184113740921}, "Polygon": [{"X": 0.35925760865211487, "Y": 0.4241841733455658}, {"X": 0.43311288952827454, "Y": 0.424184113740921}, {"X": 0.43311285972595215, "Y": 0.43396905064582825}, {"X": 0.35925760865211487, "Y": 0.433969110250473}]}, "Id": "7d58425e-afec-4529-be89-e5c7d563cb01"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Free", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029024582356214523, "Height": 0.009168785996735096, "Left": 0.542034387588501, "Top": 0.4244388937950134}, "Polygon": [{"X": 0.5420344471931458, "Y": 0.4244389235973358}, {"X": 0.571058988571167, "Y": 0.4244388937950134}, {"X": 0.571058988571167, "Y": 0.43360766768455505}, {"X": 0.542034387588501, "Y": 0.43360769748687744}]}, "Id": "a182c646-a4bc-4def-add0-e178d4ed9346"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03382464498281479, "Height": 0.008932993747293949, "Left": 0.5746264457702637, "Top": 0.42453137040138245}, "Polygon": [{"X": 0.5746265053749084, "Y": 0.42453137040138245}, {"X": 0.6084511280059814, "Y": 0.42453137040138245}, {"X": 0.6084511280059814, "Y": 0.43346431851387024}, {"X": 0.5746264457702637, "Y": 0.4334643483161926}]}, "Id": "2eecd6e3-355a-4841-8b95-ba8a5293f2b2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016433531418442726, "Height": 0.00953955389559269, "Left": 0.7678049206733704, "Top": 0.4244562089443207}, "Polygon": [{"X": 0.7678049802780151, "Y": 0.4244562089443207}, {"X": 0.7842384576797485, "Y": 0.4244562089443207}, {"X": 0.7842384576797485, "Y": 0.4339957535266876}, {"X": 0.7678049206733704, "Y": 0.4339957535266876}]}, "Id": "9b7285fb-29c8-4f58-b39c-67f432c2e2b7"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Pro", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02244741842150688, "Height": 0.008654028177261353, "Left": 0.17208631336688995, "Top": 0.45812109112739563}, "Polygon": [{"X": 0.17208631336688995, "Y": 0.458121120929718}, {"X": 0.19453372061252594, "Y": 0.45812109112739563}, {"X": 0.19453372061252594, "Y": 0.466775119304657}, {"X": 0.17208631336688995, "Y": 0.466775119304657}]}, "Id": "2132385c-a973-4429-9a12-42fe43bd5c8d"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05551137775182724, "Height": 0.009162886999547482, "Left": 0.1989811807870865, "Top": 0.4575645625591278}, "Polygon": [{"X": 0.19898119568824768, "Y": 0.4575645923614502}, {"X": 0.25449255108833313, "Y": 0.4575645625591278}, {"X": 0.25449255108833313, "Y": 0.4667274057865143}, {"X": 0.1989811807870865, "Y": 0.46672743558883667}]}, "Id": "44835849-75aa-460f-a348-fdccdc8fdfcf"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04921639338135719, "Height": 0.011375298723578453, "Left": 0.4001304507255554, "Top": 0.457475870847702}, "Polygon": [{"X": 0.4001304507255554, "Y": 0.4574759006500244}, {"X": 0.4493468403816223, "Y": 0.457475870847702}, {"X": 0.4493468105792999, "Y": 0.46885111927986145}, {"X": 0.4001304507255554, "Y": 0.46885114908218384}]}, "Id": "836d3cad-2152-4eb1-8c15-84f40a5e5524"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033742185682058334, "Height": 0.008961644023656845, "Left": 0.5585670471191406, "Top": 0.4578540325164795}, "Polygon": [{"X": 0.5585670471191406, "Y": 0.4578540623188019}, {"X": 0.5923092365264893, "Y": 0.4578540325164795}, {"X": 0.5923091769218445, "Y": 0.46681565046310425}, {"X": 0.5585670471191406, "Y": 0.46681568026542664}]}, "Id": "0de78813-f954-42ba-8ee3-021e724c16f4"}, {"BlockType": "WORD", "Confidence": 99.51589965820312, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012545580975711346, "Height": 0.008820226415991783, "Left": 0.5967296957969666, "Top": 0.4579833447933197}, "Polygon": [{"X": 0.5967297554016113, "Y": 0.4579833447933197}, {"X": 0.6092752814292908, "Y": 0.4579833447933197}, {"X": 0.6092752814292908, "Y": 0.46680355072021484}, {"X": 0.5967296957969666, "Y": 0.46680355072021484}]}, "Id": "ebb00785-8210-4bbb-a793-13694cfef538"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03394567593932152, "Height": 0.009067394770681858, "Left": 0.6421647071838379, "Top": 0.4578300416469574}, "Polygon": [{"X": 0.6421647071838379, "Y": 0.4578300714492798}, {"X": 0.6761103868484497, "Y": 0.4578300416469574}, {"X": 0.6761103868484497, "Y": 0.4668973982334137}, {"X": 0.6421647071838379, "Y": 0.4668974280357361}]}, "Id": "8e3bcd9f-1355-44d4-9335-126da6ae207a"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025891704484820366, "Height": 0.008841403760015965, "Left": 0.6798320412635803, "Top": 0.45776909589767456}, "Polygon": [{"X": 0.6798321008682251, "Y": 0.45776909589767456}, {"X": 0.705723762512207, "Y": 0.45776909589767456}, {"X": 0.705723762512207, "Y": 0.46661046147346497}, {"X": 0.6798320412635803, "Y": 0.46661049127578735}]}, "Id": "831cd8c3-c5b4-4ddc-a907-b5a6f5bc4155"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Detention", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06778997927904129, "Height": 0.009047068655490875, "Left": 0.7713633179664612, "Top": 0.45781731605529785}, "Polygon": [{"X": 0.7713633179664612, "Y": 0.45781734585762024}, {"X": 0.8391532897949219, "Y": 0.45781731605529785}, {"X": 0.8391532301902771, "Y": 0.46686434745788574}, {"X": 0.7713633179664612, "Y": 0.46686437726020813}]}, "Id": "0c0c8832-1987-43ed-ade2-20e2cc7ce54e"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0335482694208622, "Height": 0.00914868526160717, "Left": 0.8422802090644836, "Top": 0.4577257037162781}, "Polygon": [{"X": 0.8422802090644836, "Y": 0.45772573351860046}, {"X": 0.8758284449577332, "Y": 0.4577257037162781}, {"X": 0.8758284449577332, "Y": 0.466874361038208}, {"X": 0.8422802090644836, "Y": 0.4668743908405304}]}, "Id": "a22a5c2a-df18-4986-a017-1cc113b2313e"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "212123517", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07323504239320755, "Height": 0.0097573297098279, "Left": 0.1389329433441162, "Top": 0.472911536693573}, "Polygon": [{"X": 0.1389329433441162, "Y": 0.4729115962982178}, {"X": 0.21216797828674316, "Y": 0.472911536693573}, {"X": 0.21216797828674316, "Y": 0.48266884684562683}, {"X": 0.1389329433441162, "Y": 0.4826688766479492}]}, "Id": "f953b68c-71bf-4594-9486-1c301b7a6c95"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "2229", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0323796346783638, "Height": 0.009609190747141838, "Left": 0.3591700494289398, "Top": 0.4730161726474762}, "Polygon": [{"X": 0.3591700494289398, "Y": 0.4730162024497986}, {"X": 0.391549676656723, "Y": 0.4730161726474762}, {"X": 0.391549676656723, "Y": 0.4826253354549408}, {"X": 0.3591700494289398, "Y": 0.4826253652572632}]}, "Id": "46e163e5-bd2e-450d-af18-0bbc66c7af8f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "13:41", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033566903322935104, "Height": 0.00940698105841875, "Left": 0.5485408306121826, "Top": 0.47315138578414917}, "Polygon": [{"X": 0.5485408306121826, "Y": 0.47315141558647156}, {"X": 0.5821077227592468, "Y": 0.47315138578414917}, {"X": 0.5821077227592468, "Y": 0.48255836963653564}, {"X": 0.5485408306121826, "Y": 0.48255836963653564}]}, "Id": "2d395088-fbb5-422b-a5a1-aa8ed35f6b4c"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "14:34", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035402726382017136, "Height": 0.009517006576061249, "Left": 0.6384938359260559, "Top": 0.4730910062789917}, "Polygon": [{"X": 0.6384938955307007, "Y": 0.4730910360813141}, {"X": 0.6738966107368469, "Y": 0.4730910062789917}, {"X": 0.6738965511322021, "Y": 0.48260802030563354}, {"X": 0.6384938359260559, "Y": 0.48260802030563354}]}, "Id": "f016cee4-d25b-45fd-b416-c829c2012df4"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "53", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01501556858420372, "Height": 0.00930704828351736, "Left": 0.7681207060813904, "Top": 0.47314777970314026}, "Polygon": [{"X": 0.7681207656860352, "Y": 0.47314777970314026}, {"X": 0.7831363081932068, "Y": 0.47314777970314026}, {"X": 0.783136248588562, "Y": 0.4824548065662384}, {"X": 0.7681207060813904, "Y": 0.4824548363685608}]}, "Id": "c5fde56e-4760-4f88-b90e-166fb9d5d8e4"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034937888383865356, "Height": 0.00903060007840395, "Left": 0.1373981088399887, "Top": 0.7409117221832275}, "Polygon": [{"X": 0.1373981088399887, "Y": 0.7409117221832275}, {"X": 0.17233599722385406, "Y": 0.7409117221832275}, {"X": 0.17233598232269287, "Y": 0.7499423623085022}, {"X": 0.1373981088399887, "Y": 0.7499423027038574}]}, "Id": "b40340fd-953d-45d5-a81c-49b6d6920a4c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Billable", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049619559198617935, "Height": 0.009130165912210941, "Left": 0.176555335521698, "Top": 0.7407608032226562}, "Polygon": [{"X": 0.1765553504228592, "Y": 0.7407608032226562}, {"X": 0.22617489099502563, "Y": 0.7407608032226562}, {"X": 0.22617487609386444, "Y": 0.7498909831047058}, {"X": 0.176555335521698, "Y": 0.7498909831047058}]}, "Id": "765320bd-f79b-4944-9af2-f9ebc4fb976d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03389165922999382, "Height": 0.009019124321639538, "Left": 0.22935569286346436, "Top": 0.7411022782325745}, "Polygon": [{"X": 0.22935569286346436, "Y": 0.7411022782325745}, {"X": 0.2632473409175873, "Y": 0.7411022782325745}, {"X": 0.2632473409175873, "Y": 0.7501213550567627}, {"X": 0.22935569286346436, "Y": 0.7501213550567627}]}, "Id": "dc2c685e-e045-4a75-b0cd-684468440c5a"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "23", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015582477673888206, "Height": 0.00932667963206768, "Left": 0.359360009431839, "Top": 0.7396042346954346}, "Polygon": [{"X": 0.359360009431839, "Y": 0.7396042346954346}, {"X": 0.37494248151779175, "Y": 0.7396042346954346}, {"X": 0.37494248151779175, "Y": 0.7489309310913086}, {"X": 0.359360009431839, "Y": 0.7489308714866638}]}, "Id": "7308cfec-5b6e-4f0f-9b7a-81934e501e8e"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03484051302075386, "Height": 0.009248202666640282, "Left": 0.13744521141052246, "Top": 0.7574752569198608}, "Polygon": [{"X": 0.13744522631168365, "Y": 0.7574752569198608}, {"X": 0.17228572070598602, "Y": 0.7574752569198608}, {"X": 0.17228572070598602, "Y": 0.7667234539985657}, {"X": 0.13744521141052246, "Y": 0.7667234539985657}]}, "Id": "3487a217-7342-4d4a-a2c5-4d7d7d32602e"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04928787052631378, "Height": 0.011379695497453213, "Left": 0.17617006599903107, "Top": 0.7573843598365784}, "Polygon": [{"X": 0.17617008090019226, "Y": 0.7573843598365784}, {"X": 0.22545793652534485, "Y": 0.7573843598365784}, {"X": 0.22545792162418365, "Y": 0.7687640190124512}, {"X": 0.17617006599903107, "Y": 0.7687640190124512}]}, "Id": "7af0cdbc-20b1-48bd-a376-5c2d98e6e291"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "2229", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03243493288755417, "Height": 0.009528819471597672, "Left": 0.3590348958969116, "Top": 0.758657693862915}, "Polygon": [{"X": 0.3590348958969116, "Y": 0.758657693862915}, {"X": 0.391469806432724, "Y": 0.758657693862915}, {"X": 0.391469806432724, "Y": 0.7681865096092224}, {"X": 0.3590348958969116, "Y": 0.7681865096092224}]}, "Id": "497fc180-b9c2-4461-a51d-7090222acdbc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041492361575365067, "Height": 0.009039398282766342, "Left": 0.13845294713974, "Top": 0.7909696102142334}, "Polygon": [{"X": 0.13845294713974, "Y": 0.7909696102142334}, {"X": 0.17994530498981476, "Y": 0.7909696698188782}, {"X": 0.17994529008865356, "Y": 0.80000901222229}, {"X": 0.13845294713974, "Y": 0.80000901222229}]}, "Id": "4d01ca94-8b13-4519-834f-b1b7ab8530d6"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Certification:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08748794347047806, "Height": 0.009525339119136333, "Left": 0.18349716067314148, "Top": 0.7906875610351562}, "Polygon": [{"X": 0.18349716067314148, "Y": 0.7906875610351562}, {"X": 0.27098509669303894, "Y": 0.790687620639801}, {"X": 0.27098509669303894, "Y": 0.8002129197120667}, {"X": 0.18349716067314148, "Y": 0.8002128601074219}]}, "Id": "862ee2c0-9c71-49c7-9d74-02a9233d5f79"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028900928795337677, "Height": 0.008580746129155159, "Left": 0.14995381236076355, "Top": 0.8110046982765198}, "Polygon": [{"X": 0.14995381236076355, "Y": 0.8110046982765198}, {"X": 0.17885473370552063, "Y": 0.8110046982765198}, {"X": 0.17885473370552063, "Y": 0.8195854425430298}, {"X": 0.14995381236076355, "Y": 0.8195854425430298}]}, "Id": "786047ff-ff94-4846-b332-e01c2e556637"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "required", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047473419457674026, "Height": 0.009647597558796406, "Left": 0.18255116045475006, "Top": 0.8110893964767456}, "Polygon": [{"X": 0.18255116045475006, "Y": 0.8110893964767456}, {"X": 0.2300245761871338, "Y": 0.8110893964767456}, {"X": 0.2300245612859726, "Y": 0.8207370042800903}, {"X": 0.18255116045475006, "Y": 0.8207369446754456}]}, "Id": "b697901f-0219-4aa7-8753-a72d764b7779"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01597793586552143, "Height": 0.008456677198410034, "Left": 0.23381461203098297, "Top": 0.8109359741210938}, "Polygon": [{"X": 0.23381462693214417, "Y": 0.8109359741210938}, {"X": 0.24979254603385925, "Y": 0.8109359741210938}, {"X": 0.24979254603385925, "Y": 0.8193926215171814}, {"X": 0.23381461203098297, "Y": 0.8193926215171814}]}, "Id": "d725809f-5f13-436a-bd84-7e470674ff8a"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "live", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019583726301789284, "Height": 0.008571626618504524, "Left": 0.2530183494091034, "Top": 0.810969889163971}, "Polygon": [{"X": 0.2530183494091034, "Y": 0.810969889163971}, {"X": 0.2726020812988281, "Y": 0.810969889163971}, {"X": 0.27260205149650574, "Y": 0.8195415139198303}, {"X": 0.2530183494091034, "Y": 0.8195415139198303}]}, "Id": "6edf0a28-fb64-447a-b012-34bf8ad594e6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "pickup", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037211958318948746, "Height": 0.010089718736708164, "Left": 0.27649611234664917, "Top": 0.8109998106956482}, "Polygon": [{"X": 0.27649611234664917, "Y": 0.8109998106956482}, {"X": 0.3137080669403076, "Y": 0.8109998106956482}, {"X": 0.31370803713798523, "Y": 0.821089506149292}, {"X": 0.27649611234664917, "Y": 0.821089506149292}]}, "Id": "e339fbb9-7ec6-4643-b47b-7fd8c5a7897e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "exceeds", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04904527962207794, "Height": 0.008563664741814137, "Left": 0.3174999952316284, "Top": 0.8109355568885803}, "Polygon": [{"X": 0.3175000250339508, "Y": 0.8109355568885803}, {"X": 0.36654528975486755, "Y": 0.8109356164932251}, {"X": 0.36654525995254517, "Y": 0.8194992542266846}, {"X": 0.3174999952316284, "Y": 0.8194991946220398}]}, "Id": "9592c9e7-8685-47e3-898a-558ba31a7b8d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "free", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0227833092212677, "Height": 0.008721583522856236, "Left": 0.3697018623352051, "Top": 0.8108998537063599}, "Polygon": [{"X": 0.36970189213752747, "Y": 0.8108998537063599}, {"X": 0.3924851715564728, "Y": 0.8108998537063599}, {"X": 0.3924851715564728, "Y": 0.8196214437484741}, {"X": 0.3697018623352051, "Y": 0.8196214437484741}]}, "Id": "5f7bd8e8-5c08-4cd0-b925-04467515cdc2"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025020567700266838, "Height": 0.008516927249729633, "Left": 0.3960302770137787, "Top": 0.8111708760261536}, "Polygon": [{"X": 0.3960303068161011, "Y": 0.8111708760261536}, {"X": 0.4210508465766907, "Y": 0.8111708760261536}, {"X": 0.4210508465766907, "Y": 0.8196877837181091}, {"X": 0.3960302770137787, "Y": 0.8196877837181091}]}, "Id": "d9da1c9b-34ff-49b7-b53f-86d5dbaf4a9e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "allowed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044166531413793564, "Height": 0.008627244271337986, "Left": 0.424558162689209, "Top": 0.8110710382461548}, "Polygon": [{"X": 0.42455819249153137, "Y": 0.8110710382461548}, {"X": 0.46872469782829285, "Y": 0.8110710382461548}, {"X": 0.46872466802597046, "Y": 0.8196982741355896}, {"X": 0.424558162689209, "Y": 0.8196982145309448}]}, "Id": "3793d729-fd0c-4c2b-b8ec-52e991b6b902"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "per", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019325971603393555, "Height": 0.00843348354101181, "Left": 0.47247514128685, "Top": 0.8129834532737732}, "Polygon": [{"X": 0.47247517108917236, "Y": 0.8129834532737732}, {"X": 0.49180111289024353, "Y": 0.8129834532737732}, {"X": 0.49180111289024353, "Y": 0.8214169144630432}, {"X": 0.47247514128685, "Y": 0.8214169144630432}]}, "Id": "f34b6b35-cd69-4bbe-a2eb-0e3057f45c80"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "stop.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02761196531355381, "Height": 0.009758474305272102, "Left": 0.4955254793167114, "Top": 0.8112159371376038}, "Polygon": [{"X": 0.4955255091190338, "Y": 0.8112159371376038}, {"X": 0.5231374502182007, "Y": 0.8112159371376038}, {"X": 0.5231374502182007, "Y": 0.8209744095802307}, {"X": 0.4955254793167114, "Y": 0.8209744095802307}]}, "Id": "4282a516-63b1-4374-a15d-2d0b14340814"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "8c12436b-e4ee-499e-9c22-7da9e8f538e7", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "8c12436b-e4ee-499e-9c22-7da9e8f538e7", "content-type": "application/x-amz-json-1.1", "content-length": "58663", "date": "<PERSON><PERSON>, 23 Sep 2025 18:38:25 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "ba163971-a49f-4266-8e29-a4e38b03985c", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "<PERSON><PERSON>, 23 Sep 2025 18:38:28 GMT", "content-type": "application/json", "content-length": "996", "connection": "keep-alive", "x-amzn-requestid": "ba163971-a49f-4266-8e29-a4e38b03985c"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page with text. The content: \"W&I Cert #1 ; RECVD: 5/25/2025 8:06:20 AM CENTRAL TRANSPORT Driver Detention Certificate Customer Name ILLING PACKAGING ...\". There is \"Driver Detention Certificate\" and \"W&I Cert #1\" which may refer to certificate of analysis? But content is about driver detention, time in/out, detention time, etc. That's a detention certificate. The doc_type categories include \"log\" for driver log or driver detention certificate under log type. The log type includes driver detention certificate. So this page should be classified as log. Also \"Certificate of Analysis\" (COA) is separate, but this is not a COA. So doc_type=log. Use tool."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"log\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2436, "outputTokens": 179, "totalTokens": 2615}, "metrics": {"latencyMs": 999}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/006df4e3_KE7TCH9TPQZFVA5CZ3HT.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination, keywords indication and key fields/structure are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nW&I Cert #1 ; RECVD: 5/25/2025 8:06:20 AM\nCENTRAL TRANSPORT\nDriver Detention Certificate\nCustomer Name\nILLING PACKAGING % ENVOY LOGISTICS\nCustomer Number\n54903000114\nDetention Company\nILLING COMPANY\nAddress\nW204 N13125 GOLDENDALE RD\nRICHFIELD, WI 53076\nTrailer\n2400970\nDate of Detention\n05/20/2025\nFree Time\n30\nPro Number\nWeight\nTime In\nTime Out\nDetention Time\n212123517\n2229\n13:41\n14:34\n53\nTotal Billable Time\n23\nTotal Weight\n2229\nDriver Certification:\nTime required for live pickup exceeds free time allowed per stop.\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}